{"version": 3, "sources": ["../../../src/lib/typescript/missingDependencyError.ts"], "names": ["missingDepsError", "dir", "missingPackages", "packagesHuman", "getOxfordCommaList", "map", "p", "pkg", "packagesCli", "join", "packageManager", "getPkgManager", "removalMsg", "bold", "cyan", "FatalE<PERSON>r", "red"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;4BAPgB;iCAEG;4BAER;+BACG;AAEvB,SAASA,iBACdC,GAAW,EACXC,eAAoC;IAEpC,MAAMC,gBAAgBC,IAAAA,mCAAkB,EAACF,gBAAgBG,GAAG,CAAC,CAACC,IAAMA,EAAEC,GAAG;IACzE,MAAMC,cAAcN,gBAAgBG,GAAG,CAAC,CAACC,IAAMA,EAAEC,GAAG,EAAEE,IAAI,CAAC;IAC3D,MAAMC,iBAAiBC,IAAAA,4BAAa,EAACV;IAErC,MAAMW,aACJ,SACAC,IAAAA,gBAAI,EACF,gEACEC,IAAAA,gBAAI,EAAC,mBACL;IAGN,MAAM,IAAIC,sBAAU,CAClBF,IAAAA,gBAAI,EACFG,IAAAA,eAAG,EACD,CAAC,gGAAgG,CAAC,KAGpG,SACAH,IAAAA,gBAAI,EAAC,CAAC,eAAe,EAAEA,IAAAA,gBAAI,EAACV,eAAe,YAAY,CAAC,IACxD,SACA,CAAC,EAAE,EAAEU,IAAAA,gBAAI,EACPC,IAAAA,gBAAI,EACF,AAACJ,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACnB,4BACA,wBAAuB,IACzB,MACAF,cAEJ,CAAC,GACHI,aACA;AAEN"}