"use strict";(()=>{var e={};e.id=247,e.ids=[247],e.modules={145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6249:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},3423:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>d,routeModule:()=>_});var i={};r.r(i),r.d(i,{default:()=>c});var o=r(1802),s=r(7153),n=r(6249),a=r(395);async function c(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=e.body;for(let e of["service_id","service_name","customer_info","booking_details"])if(!r[e])return t.status(400).json({error:`Missing required field: ${e}`});let{customer_info:i}=r;for(let e of["first_name","last_name","email","phone"])if(!i[e])return t.status(400).json({error:`Missing customer field: ${e}`});let{booking_details:o}=r;for(let e of["preferred_date","preferred_time","event_type","location"])if(!o[e])return t.status(400).json({error:`Missing booking field: ${e}`});let s=new Date(o.preferred_date),n=new Date;if(n.setHours(0,0,0,0),s<n)return t.status(400).json({error:"Booking date must be in the future"});let c={service_id:r.service_id,service_name:r.service_name,customer_first_name:i.first_name,customer_last_name:i.last_name,customer_email:i.email,customer_phone:i.phone,customer_id:i.customer_id||null,is_guest_booking:!i.is_authenticated,preferred_date:o.preferred_date,preferred_time:o.preferred_time,alternative_date:o.alternative_date||null,alternative_time:o.alternative_time||null,event_type:o.event_type,number_of_people:o.number_of_people||1,location_type:o.location,location_address:o.address||null,special_requests:o.special_requests||null,base_price:r.pricing?.base_price||0,estimated_total:r.pricing?.estimated_total||0,status:"pending",booking_source:"public_website",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};console.log("[Booking API] Creating booking request:",{service:c.service_name,customer:`${c.customer_first_name} ${c.customer_last_name}`,date:c.preferred_date,is_guest:c.is_guest_booking});let{data:d,error:m}=await a.OQ.from("bookings").insert([c]).select().single();if(m){if(console.error("[Booking API] Database error:",m),"23505"===m.code)return t.status(409).json({error:"A booking with these details already exists"});return t.status(500).json({error:"Failed to create booking request",details:void 0})}try{await l(d)}catch(e){console.warn("[Booking API] Email notification failed:",e)}try{await u(d)}catch(e){console.warn("[Booking API] Admin notification failed:",e)}return console.log("[Booking API] Booking created successfully:",d.id),t.status(201).json({success:!0,booking:{id:d.id,status:d.status,service_name:d.service_name,preferred_date:d.preferred_date,preferred_time:d.preferred_time,estimated_total:d.estimated_total},message:"Booking request submitted successfully! We will contact you to confirm your appointment."})}catch(e){return console.error("[Booking API] Unexpected error:",e),t.status(500).json({error:"Internal server error",message:"An unexpected error occurred while processing your booking request"})}}async function l(e){console.log("[Email] Sending confirmation email to:",e.customer_email),e.customer_email,e.customer_first_name,e.customer_last_name,e.service_name,e.preferred_date,e.preferred_time,e.id}async function u(e){console.log("[Notification] New booking received:",{id:e.id,service:e.service_name,customer:`${e.customer_first_name} ${e.customer_last_name}`,date:e.preferred_date})}let d=(0,n.l)(i,"default"),m=(0,n.l)(i,"config"),_=new o.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/bookings/create",pathname:"/api/bookings/create",bundlePath:"",filename:""},userland:i})},395:(e,t,r)=>{r.d(t,{v8:()=>l,OQ:()=>c});let i=require("@supabase/supabase-js"),o="https://ndlgbcsbidyhxbpqzgqp.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI";if(!o||!s)throw Error("Missing required Supabase environment variables for public client");let n={"X-Client-Info":"ocean-soul-sparkles-public@1.0.0","X-Client-Type":"public-website"},a=(e,t={})=>Promise.race([fetch(e,t),new Promise((e,t)=>setTimeout(()=>t(Error("Request timeout")),1e4))]),c=function(){try{console.log("[Public Supabase] Creating public client instance");let e=(0,i.createClient)(o,s,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!1,storageKey:"oss_public_auth_token",storage:void 0,cookieOptions:{path:"/",sameSite:"Lax",secure:!0}},global:{headers:n,fetch:a},realtime:{params:{eventsPerSecond:1}}});return console.log("[Public Supabase] Client created successfully"),e}catch(e){throw console.error("[Public Supabase] Error creating client:",e),e}}(),l={async getServices(){try{let{data:e,error:t}=await c.from("services").select("*").eq("status","active").eq("visible_on_public",!0).gte("duration",120).lte("duration",360).order("name");return{data:e,error:t}}catch(e){return console.error("[Public Data] Error fetching services:",e),{data:null,error:e}}},async getProducts(e=null){try{let t=c.from("products").select("*").eq("status","active");e&&"all"!==e&&(t=t.eq("category_name",e));let{data:r,error:i}=await t.order("name");return{data:r,error:i}}catch(e){return console.error("[Public Data] Error fetching products:",e),{data:null,error:e}}}}},7153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},1802:(e,t,r)=>{e.exports=r(145)}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=3423);module.exports=r})();