[{"C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\bookings\\create.js": "1", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\public\\products.js": "2", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\public\\services.js": "3", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\book-online.js": "4", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\index.js": "5", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\login.js": "6", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\shop.js": "7", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\signup.js": "8", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\_app.js": "9", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\BookingForm.js": "10", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ErrorBoundary.js": "11", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\Layout.js": "12", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ProductCard.js": "13", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\PWAProvider.js": "14", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ServiceSelector.js": "15", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\lib\\supabase.js": "16", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\AnimatedSection.js": "17", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\GoogleVerification.js": "18", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\HeroSection.js": "19", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\NotificationPrompt.js": "20", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\OceanSparklesShowcase.js": "21", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\OneSignalProvider.js": "22", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\SEO\\PageSEO.js": "23", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\SparkleButton.js": "24", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\StaggeredList.js": "25", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\StructuredData\\SchemaManager.js": "26", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\lib\\analytics-public.js": "27", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\lib\\onesignal-public.js": "28"}, {"size": 7017, "mtime": 1749877003628, "results": "29", "hashOfConfig": "30"}, {"size": 3418, "mtime": 1749872170035, "results": "31", "hashOfConfig": "30"}, {"size": 2969, "mtime": 1749872146152, "results": "32", "hashOfConfig": "30"}, {"size": 8176, "mtime": 1749876914430, "results": "33", "hashOfConfig": "30"}, {"size": 14019, "mtime": 1749872069376, "results": "34", "hashOfConfig": "30"}, {"size": 5671, "mtime": 1749876825945, "results": "35", "hashOfConfig": "30"}, {"size": 7634, "mtime": 1749877628943, "results": "36", "hashOfConfig": "30"}, {"size": 10254, "mtime": 1749876856824, "results": "37", "hashOfConfig": "30"}, {"size": 5375, "mtime": 1749872103556, "results": "38", "hashOfConfig": "30"}, {"size": 14279, "mtime": 1749876974749, "results": "39", "hashOfConfig": "30"}, {"size": 5815, "mtime": 1749876781712, "results": "40", "hashOfConfig": "30"}, {"size": 9089, "mtime": 1749872263375, "results": "41", "hashOfConfig": "30"}, {"size": 5145, "mtime": 1749877651064, "results": "42", "hashOfConfig": "30"}, {"size": 6035, "mtime": 1749876804739, "results": "43", "hashOfConfig": "30"}, {"size": 4968, "mtime": 1749876933372, "results": "44", "hashOfConfig": "30"}, {"size": 5805, "mtime": 1749872004100, "results": "45", "hashOfConfig": "30"}, {"size": 3000, "mtime": 1749880284404, "results": "46", "hashOfConfig": "30"}, {"size": 1200, "mtime": 1749882011589, "results": "47", "hashOfConfig": "30"}, {"size": 4274, "mtime": 1749880268295, "results": "48", "hashOfConfig": "30"}, {"size": 4638, "mtime": 1749880184052, "results": "49", "hashOfConfig": "30"}, {"size": 6183, "mtime": 1749880323948, "results": "50", "hashOfConfig": "30"}, {"size": 2305, "mtime": 1749881999829, "results": "51", "hashOfConfig": "30"}, {"size": 5094, "mtime": 1749881982961, "results": "52", "hashOfConfig": "30"}, {"size": 2475, "mtime": 1749880162590, "results": "53", "hashOfConfig": "30"}, {"size": 1654, "mtime": 1749880295918, "results": "54", "hashOfConfig": "30"}, {"size": 5962, "mtime": 1749880208640, "results": "55", "hashOfConfig": "30"}, {"size": 5800, "mtime": 1749882064659, "results": "56", "hashOfConfig": "30"}, {"size": 5186, "mtime": 1749882036016, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1axaeq4", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\bookings\\create.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\public\\products.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\public\\services.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\book-online.js", ["142", "143", "144", "145"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\index.js", ["146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\login.js", ["164"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\shop.js", ["165", "166", "167"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\signup.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\_app.js", ["168", "169", "170"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\BookingForm.js", ["171"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ErrorBoundary.js", ["172", "173"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\Layout.js", ["174", "175", "176", "177", "178", "179"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ProductCard.js", ["180"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\PWAProvider.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ServiceSelector.js", ["181"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\lib\\supabase.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\AnimatedSection.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\GoogleVerification.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\HeroSection.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\NotificationPrompt.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\OceanSparklesShowcase.js", ["182"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\OneSignalProvider.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\SEO\\PageSEO.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\SparkleButton.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\StaggeredList.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\StructuredData\\SchemaManager.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\lib\\analytics-public.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\lib\\onesignal-public.js", [], [], {"ruleId": "183", "severity": 1, "message": "184", "line": 93, "column": 42, "nodeType": "185", "messageId": "186", "suggestions": "187"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 112, "column": 24, "nodeType": "185", "messageId": "186", "suggestions": "188"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 165, "column": 17, "nodeType": "191", "endLine": 165, "endColumn": 70}, {"ruleId": "189", "severity": 1, "message": "192", "line": 166, "column": 17, "nodeType": "191", "endLine": 166, "endColumn": 66}, {"ruleId": "193", "severity": 1, "message": "194", "line": 63, "column": 17, "nodeType": "191", "endLine": 63, "endColumn": 129}, {"ruleId": "193", "severity": 1, "message": "194", "line": 76, "column": 17, "nodeType": "191", "endLine": 76, "endColumn": 112}, {"ruleId": "193", "severity": 1, "message": "194", "line": 89, "column": 17, "nodeType": "191", "endLine": 89, "endColumn": 125}, {"ruleId": "183", "severity": 1, "message": "184", "line": 115, "column": 23, "nodeType": "185", "messageId": "186", "suggestions": "195"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 119, "column": 54, "nodeType": "185", "messageId": "186", "suggestions": "196"}, {"ruleId": "193", "severity": 1, "message": "194", "line": 127, "column": 13, "nodeType": "191", "endLine": 127, "endColumn": 136}, {"ruleId": "193", "severity": 1, "message": "194", "line": 139, "column": 15, "nodeType": "191", "endLine": 139, "endColumn": 122}, {"ruleId": "193", "severity": 1, "message": "194", "line": 145, "column": 15, "nodeType": "191", "endLine": 145, "endColumn": 122}, {"ruleId": "193", "severity": 1, "message": "194", "line": 151, "column": 15, "nodeType": "191", "endLine": 151, "endColumn": 122}, {"ruleId": "193", "severity": 1, "message": "194", "line": 157, "column": 15, "nodeType": "191", "endLine": 157, "endColumn": 122}, {"ruleId": "193", "severity": 1, "message": "194", "line": 163, "column": 15, "nodeType": "191", "endLine": 163, "endColumn": 122}, {"ruleId": "193", "severity": 1, "message": "194", "line": 169, "column": 15, "nodeType": "191", "endLine": 169, "endColumn": 122}, {"ruleId": "193", "severity": 1, "message": "194", "line": 175, "column": 15, "nodeType": "191", "endLine": 175, "endColumn": 122}, {"ruleId": "193", "severity": 1, "message": "194", "line": 181, "column": 15, "nodeType": "191", "endLine": 181, "endColumn": 135}, {"ruleId": "183", "severity": 1, "message": "184", "line": 210, "column": 22, "nodeType": "185", "messageId": "186", "suggestions": "197"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 210, "column": 128, "nodeType": "185", "messageId": "186", "suggestions": "198"}, {"ruleId": "193", "severity": 1, "message": "194", "line": 230, "column": 21, "nodeType": "191", "endLine": 230, "endColumn": 84}, {"ruleId": "193", "severity": 1, "message": "194", "line": 233, "column": 21, "nodeType": "191", "endLine": 233, "endColumn": 82}, {"ruleId": "183", "severity": 1, "message": "184", "line": 145, "column": 18, "nodeType": "185", "messageId": "186", "suggestions": "199"}, {"ruleId": "200", "severity": 1, "message": "201", "line": 18, "column": 6, "nodeType": "202", "endLine": 18, "endColumn": 24, "suggestions": "203"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 152, "column": 48, "nodeType": "185", "messageId": "186", "suggestions": "204"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 184, "column": 58, "nodeType": "185", "messageId": "186", "suggestions": "205"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 128, "column": 24, "nodeType": "185", "messageId": "186", "suggestions": "206"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 128, "column": 45, "nodeType": "185", "messageId": "186", "suggestions": "207"}, {"ruleId": "189", "severity": 1, "message": "208", "line": 129, "column": 9, "nodeType": "191", "endLine": 129, "endColumn": 74}, {"ruleId": "183", "severity": 1, "message": "184", "line": 385, "column": 15, "nodeType": "185", "messageId": "186", "suggestions": "209"}, {"ruleId": "183", "severity": 1, "message": "184", "line": 98, "column": 15, "nodeType": "185", "messageId": "186", "suggestions": "210"}, {"ruleId": "189", "severity": 1, "message": "208", "line": 121, "column": 13, "nodeType": "191", "endLine": 134, "endColumn": 14}, {"ruleId": "193", "severity": 1, "message": "194", "line": 96, "column": 13, "nodeType": "191", "endLine": 96, "endColumn": 75}, {"ruleId": "183", "severity": 1, "message": "211", "line": 149, "column": 47, "nodeType": "185", "messageId": "186", "suggestions": "212"}, {"ruleId": "183", "severity": 1, "message": "211", "line": 149, "column": 125, "nodeType": "185", "messageId": "186", "suggestions": "213"}, {"ruleId": "193", "severity": 1, "message": "194", "line": 153, "column": 19, "nodeType": "191", "endLine": 153, "endColumn": 82}, {"ruleId": "193", "severity": 1, "message": "194", "line": 156, "column": 19, "nodeType": "191", "endLine": 156, "endColumn": 80}, {"ruleId": "193", "severity": 1, "message": "194", "line": 204, "column": 13, "nodeType": "191", "endLine": 204, "endColumn": 126}, {"ruleId": "193", "severity": 1, "message": "194", "line": 59, "column": 11, "nodeType": "191", "endLine": 64, "endColumn": 13}, {"ruleId": "193", "severity": 1, "message": "194", "line": 64, "column": 17, "nodeType": "191", "endLine": 70, "endColumn": 19}, {"ruleId": "193", "severity": 1, "message": "194", "line": 200, "column": 15, "nodeType": "191", "endLine": 207, "endColumn": 17}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["214", "215", "216", "217"], ["218", "219", "220", "221"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/signup/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "Do not use an `<a>` element to navigate to `/login/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", ["222", "223", "224", "225"], ["226", "227", "228", "229"], ["230", "231", "232", "233"], ["234", "235", "236", "237"], ["238", "239", "240", "241"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["242"], ["243", "244", "245", "246"], ["247", "248", "249", "250"], ["251", "252", "253", "254"], ["255", "256", "257", "258"], "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", ["259", "260", "261", "262"], ["263", "264", "265", "266"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["267", "268", "269", "270"], ["271", "272", "273", "274"], {"messageId": "275", "data": "276", "fix": "277", "desc": "278"}, {"messageId": "275", "data": "279", "fix": "280", "desc": "281"}, {"messageId": "275", "data": "282", "fix": "283", "desc": "284"}, {"messageId": "275", "data": "285", "fix": "286", "desc": "287"}, {"messageId": "275", "data": "288", "fix": "289", "desc": "278"}, {"messageId": "275", "data": "290", "fix": "291", "desc": "281"}, {"messageId": "275", "data": "292", "fix": "293", "desc": "284"}, {"messageId": "275", "data": "294", "fix": "295", "desc": "287"}, {"messageId": "275", "data": "296", "fix": "297", "desc": "278"}, {"messageId": "275", "data": "298", "fix": "299", "desc": "281"}, {"messageId": "275", "data": "300", "fix": "301", "desc": "284"}, {"messageId": "275", "data": "302", "fix": "303", "desc": "287"}, {"messageId": "275", "data": "304", "fix": "305", "desc": "278"}, {"messageId": "275", "data": "306", "fix": "307", "desc": "281"}, {"messageId": "275", "data": "308", "fix": "309", "desc": "284"}, {"messageId": "275", "data": "310", "fix": "311", "desc": "287"}, {"messageId": "275", "data": "312", "fix": "313", "desc": "278"}, {"messageId": "275", "data": "314", "fix": "315", "desc": "281"}, {"messageId": "275", "data": "316", "fix": "317", "desc": "284"}, {"messageId": "275", "data": "318", "fix": "319", "desc": "287"}, {"messageId": "275", "data": "320", "fix": "321", "desc": "278"}, {"messageId": "275", "data": "322", "fix": "323", "desc": "281"}, {"messageId": "275", "data": "324", "fix": "325", "desc": "284"}, {"messageId": "275", "data": "326", "fix": "327", "desc": "287"}, {"messageId": "275", "data": "328", "fix": "329", "desc": "278"}, {"messageId": "275", "data": "330", "fix": "331", "desc": "281"}, {"messageId": "275", "data": "332", "fix": "333", "desc": "284"}, {"messageId": "275", "data": "334", "fix": "335", "desc": "287"}, {"desc": "336", "fix": "337"}, {"messageId": "275", "data": "338", "fix": "339", "desc": "278"}, {"messageId": "275", "data": "340", "fix": "341", "desc": "281"}, {"messageId": "275", "data": "342", "fix": "343", "desc": "284"}, {"messageId": "275", "data": "344", "fix": "345", "desc": "287"}, {"messageId": "275", "data": "346", "fix": "347", "desc": "278"}, {"messageId": "275", "data": "348", "fix": "349", "desc": "281"}, {"messageId": "275", "data": "350", "fix": "351", "desc": "284"}, {"messageId": "275", "data": "352", "fix": "353", "desc": "287"}, {"messageId": "275", "data": "354", "fix": "355", "desc": "278"}, {"messageId": "275", "data": "356", "fix": "357", "desc": "281"}, {"messageId": "275", "data": "358", "fix": "359", "desc": "284"}, {"messageId": "275", "data": "360", "fix": "361", "desc": "287"}, {"messageId": "275", "data": "362", "fix": "363", "desc": "278"}, {"messageId": "275", "data": "364", "fix": "365", "desc": "281"}, {"messageId": "275", "data": "366", "fix": "367", "desc": "284"}, {"messageId": "275", "data": "368", "fix": "369", "desc": "287"}, {"messageId": "275", "data": "370", "fix": "371", "desc": "278"}, {"messageId": "275", "data": "372", "fix": "373", "desc": "281"}, {"messageId": "275", "data": "374", "fix": "375", "desc": "284"}, {"messageId": "275", "data": "376", "fix": "377", "desc": "287"}, {"messageId": "275", "data": "378", "fix": "379", "desc": "278"}, {"messageId": "275", "data": "380", "fix": "381", "desc": "281"}, {"messageId": "275", "data": "382", "fix": "383", "desc": "284"}, {"messageId": "275", "data": "384", "fix": "385", "desc": "287"}, {"messageId": "275", "data": "386", "fix": "387", "desc": "388"}, {"messageId": "275", "data": "389", "fix": "390", "desc": "391"}, {"messageId": "275", "data": "392", "fix": "393", "desc": "394"}, {"messageId": "275", "data": "395", "fix": "396", "desc": "397"}, {"messageId": "275", "data": "398", "fix": "399", "desc": "388"}, {"messageId": "275", "data": "400", "fix": "401", "desc": "391"}, {"messageId": "275", "data": "402", "fix": "403", "desc": "394"}, {"messageId": "275", "data": "404", "fix": "405", "desc": "397"}, "replaceWithAlt", {"alt": "406"}, {"range": "407", "text": "408"}, "Replace with `&apos;`.", {"alt": "409"}, {"range": "410", "text": "411"}, "Replace with `&lsquo;`.", {"alt": "412"}, {"range": "413", "text": "414"}, "Replace with `&#39;`.", {"alt": "415"}, {"range": "416", "text": "417"}, "Replace with `&rsquo;`.", {"alt": "406"}, {"range": "418", "text": "419"}, {"alt": "409"}, {"range": "420", "text": "421"}, {"alt": "412"}, {"range": "422", "text": "423"}, {"alt": "415"}, {"range": "424", "text": "425"}, {"alt": "406"}, {"range": "426", "text": "427"}, {"alt": "409"}, {"range": "428", "text": "429"}, {"alt": "412"}, {"range": "430", "text": "431"}, {"alt": "415"}, {"range": "432", "text": "433"}, {"alt": "406"}, {"range": "434", "text": "435"}, {"alt": "409"}, {"range": "436", "text": "437"}, {"alt": "412"}, {"range": "438", "text": "439"}, {"alt": "415"}, {"range": "440", "text": "441"}, {"alt": "406"}, {"range": "442", "text": "443"}, {"alt": "409"}, {"range": "444", "text": "445"}, {"alt": "412"}, {"range": "446", "text": "447"}, {"alt": "415"}, {"range": "448", "text": "449"}, {"alt": "406"}, {"range": "450", "text": "451"}, {"alt": "409"}, {"range": "452", "text": "453"}, {"alt": "412"}, {"range": "454", "text": "455"}, {"alt": "415"}, {"range": "456", "text": "457"}, {"alt": "406"}, {"range": "458", "text": "459"}, {"alt": "409"}, {"range": "460", "text": "461"}, {"alt": "412"}, {"range": "462", "text": "463"}, {"alt": "415"}, {"range": "464", "text": "465"}, "Update the dependencies array to be: [fetchProducts, selectedCategory]", {"range": "466", "text": "467"}, {"alt": "406"}, {"range": "468", "text": "469"}, {"alt": "409"}, {"range": "470", "text": "471"}, {"alt": "412"}, {"range": "472", "text": "473"}, {"alt": "415"}, {"range": "474", "text": "475"}, {"alt": "406"}, {"range": "476", "text": "477"}, {"alt": "409"}, {"range": "478", "text": "479"}, {"alt": "412"}, {"range": "480", "text": "481"}, {"alt": "415"}, {"range": "482", "text": "483"}, {"alt": "406"}, {"range": "484", "text": "485"}, {"alt": "409"}, {"range": "486", "text": "487"}, {"alt": "412"}, {"range": "488", "text": "489"}, {"alt": "415"}, {"range": "490", "text": "491"}, {"alt": "406"}, {"range": "492", "text": "493"}, {"alt": "409"}, {"range": "494", "text": "495"}, {"alt": "412"}, {"range": "496", "text": "497"}, {"alt": "415"}, {"range": "498", "text": "499"}, {"alt": "406"}, {"range": "500", "text": "501"}, {"alt": "409"}, {"range": "502", "text": "503"}, {"alt": "412"}, {"range": "504", "text": "505"}, {"alt": "415"}, {"range": "506", "text": "507"}, {"alt": "406"}, {"range": "508", "text": "509"}, {"alt": "409"}, {"range": "510", "text": "511"}, {"alt": "412"}, {"range": "512", "text": "513"}, {"alt": "415"}, {"range": "514", "text": "515"}, {"alt": "516"}, {"range": "517", "text": "518"}, "Replace with `&quot;`.", {"alt": "519"}, {"range": "520", "text": "521"}, "Replace with `&ldquo;`.", {"alt": "522"}, {"range": "523", "text": "524"}, "Replace with `&#34;`.", {"alt": "525"}, {"range": "526", "text": "527"}, "Replace with `&rdquo;`.", {"alt": "516"}, {"range": "528", "text": "529"}, {"alt": "519"}, {"range": "530", "text": "531"}, {"alt": "522"}, {"range": "532", "text": "533"}, {"alt": "525"}, {"range": "534", "text": "535"}, "&apos;", [3409, 3473], "Select the service you&apos;d like to book from our available options", "&lsquo;", [3409, 3473], "Select the service you&lsquo;d like to book from our available options", "&#39;", [3409, 3473], "Select the service you&#39;d like to book from our available options", "&rsquo;", [3409, 3473], "Select the service you&rsquo;d like to book from our available options", [4113, 4198], "We&apos;re currently updating our services. Please check back soon or contact us directly.", [4113, 4198], "We&lsquo;re currently updating our services. Please check back soon or contact us directly.", [4113, 4198], "We&#39;re currently updating our services. Please check back soon or contact us directly.", [4113, 4198], "We&rsquo;re currently updating our services. Please check back soon or contact us directly.", [5599, 5856], "\n              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,\n              and that&apos;s why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!\n            ", [5599, 5856], "\n              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,\n              and that&lsquo;s why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!\n            ", [5599, 5856], "\n              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,\n              and that&#39;s why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!\n            ", [5599, 5856], "\n              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,\n              and that&rsquo;s why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!\n            ", [5910, 6237], "\n              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,\n              ensuring it leaves no harmful trace. We&apos;re proud to be as friendly to the planet as we are to our customers,\n              and to provide a way for both you and the earth to shine!\n            ", [5910, 6237], "\n              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,\n              ensuring it leaves no harmful trace. We&lsquo;re proud to be as friendly to the planet as we are to our customers,\n              and to provide a way for both you and the earth to shine!\n            ", [5910, 6237], "\n              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,\n              ensuring it leaves no harmful trace. We&#39;re proud to be as friendly to the planet as we are to our customers,\n              and to provide a way for both you and the earth to shine!\n            ", [5910, 6237], "\n              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,\n              ensuring it leaves no harmful trace. We&rsquo;re proud to be as friendly to the planet as we are to our customers,\n              and to provide a way for both you and the earth to shine!\n            ", [10532, 10657], "We&apos;d love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.", [10532, 10657], "We&lsquo;d love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.", [10532, 10657], "We&#39;d love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.", [10532, 10657], "We&rsquo;d love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.", [10532, 10657], "We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we&apos;re here to help.", [10532, 10657], "We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we&lsquo;re here to help.", [10532, 10657], "We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we&#39;re here to help.", [10532, 10657], "We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we&rsquo;re here to help.", [4473, 4510], "\n              Don&apos;t have an account?", [4473, 4510], "\n              Don&lsquo;t have an account?", [4473, 4510], "\n              Don&#39;t have an account?", [4473, 4510], "\n              Don&rsquo;t have an account?", [636, 654], "[fetchProducts, selected<PERSON><PERSON><PERSON><PERSON>]", [5858, 5929], "Our biodegradable products won&apos;t harm marine life or pollute waterways.", [5858, 5929], "Our biodegradable products won&lsquo;t harm marine life or pollute waterways.", [5858, 5929], "Our biodegradable products won&#39;t harm marine life or pollute waterways.", [5858, 5929], "Our biodegradable products won&rsquo;t harm marine life or pollute waterways.", [7201, 7293], "Looking for custom colors or bulk orders? We&apos;d love to help create something unique for you!", [7201, 7293], "Looking for custom colors or bulk orders? We&lsquo;d love to help create something unique for you!", [7201, 7293], "Looking for custom colors or bulk orders? We&#39;d love to help create something unique for you!", [7201, 7293], "Looking for custom colors or bulk orders? We&rsquo;d love to help create something unique for you!", [4840, 4882], "The page you&apos;re looking for doesn't exist.", [4840, 4882], "The page you&lsquo;re looking for doesn't exist.", [4840, 4882], "The page you&#39;re looking for doesn't exist.", [4840, 4882], "The page you&rsquo;re looking for doesn't exist.", [4840, 4882], "The page you're looking for doesn&apos;t exist.", [4840, 4882], "The page you're looking for doesn&lsquo;t exist.", [4840, 4882], "The page you're looking for doesn&#39;t exist.", [4840, 4882], "The page you're looking for doesn&rsquo;t exist.", [12721, 12898], "\n            *Final pricing may vary based on location, duration, and specific requirements. \n            We&apos;ll provide a detailed quote after reviewing your request.\n          ", [12721, 12898], "\n            *Final pricing may vary based on location, duration, and specific requirements. \n            We&lsquo;ll provide a detailed quote after reviewing your request.\n          ", [12721, 12898], "\n            *Final pricing may vary based on location, duration, and specific requirements. \n            We&#39;ll provide a detailed quote after reviewing your request.\n          ", [12721, 12898], "\n            *Final pricing may vary based on location, duration, and specific requirements. \n            We&rsquo;ll provide a detailed quote after reviewing your request.\n          ", [2860, 2991], "\n            We&apos;re sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\n          ", [2860, 2991], "\n            We&lsquo;re sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\n          ", [2860, 2991], "\n            We&#39;re sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\n          ", [2860, 2991], "\n            We&rsquo;re sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\n          ", "&quot;", [5089, 5168], "&quot;Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!\"", "&ldquo;", [5089, 5168], "&ldquo;Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!\"", "&#34;", [5089, 5168], "&#34;Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!\"", "&rdquo;", [5089, 5168], "&rdquo;Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!\"", [5089, 5168], "\"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!&quot;", [5089, 5168], "\"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!&ldquo;", [5089, 5168], "\"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!&#34;", [5089, 5168], "\"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!&rdquo;"]