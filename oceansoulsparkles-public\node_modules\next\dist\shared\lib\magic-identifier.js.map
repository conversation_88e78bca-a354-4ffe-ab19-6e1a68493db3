{"version": 3, "sources": ["../../../src/shared/lib/magic-identifier.ts"], "names": ["MAGIC_IDENTIFIER_REGEX", "decodeMagicIdentifier", "decodeHex", "hexStr", "trim", "Error", "num", "parseInt", "isNaN", "String", "fromCodePoint", "DECODE_REGEX", "identifier", "matches", "match", "inner", "output", "mode", "buffer", "i", "length", "char"], "mappings": ";;;;;;;;;;;;;;;IA8FaA,sBAAsB;eAAtBA;;IAxEGC,qBAAqB;eAArBA;;;AAtBhB,SAASC,UAAUC,MAAc;IAC/B,IAAIA,OAAOC,IAAI,OAAO,IAAI;QACxB,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAMC,MAAMC,SAASJ,QAAQ;IAC7B,IAAIK,MAAMF,MAAM;QACd,MAAM,IAAID,MAAM,AAAC,mBAAiBF,SAAO;IAC3C;IAEA,OAAOM,OAAOC,aAAa,CAACJ;AAC9B;;AASA,MAAMK,eAAe;AAEd,SAASV,sBAAsBW,UAAkB;IACtD,MAAMC,UAAUD,WAAWE,KAAK,CAACH;IACjC,IAAI,CAACE,SAAS;QACZ,OAAOD;IACT;IAEA,MAAMG,QAAQF,OAAO,CAAC,EAAE;IAExB,IAAIG,SAAS;IAEb,IAAIC;IACJ,IAAIC,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,MAAMK,MAAM,EAAED,IAAK;QACrC,MAAME,OAAON,KAAK,CAACI,EAAE;QAErB,IAAIF,YAAoB;YACtB,IAAII,SAAS,KAAK;gBAChBJ;YACF,OAAO,IAAII,SAAS,KAAK;gBACvBJ;YACF,OAAO;gBACLD,UAAUK;YACZ;QACF,OAAO,IAAIJ,YAA0B;YACnC,IAAII,SAAS,KAAK;gBAChBL,UAAU;gBACVC;YACF,OAAO,IAAII,SAAS,KAAK;gBACvBL,UAAU;gBACVC;YACF,OAAO;gBACLD,UAAUK;gBACVJ;YACF;QACF,OAAO,IAAIA,YAAmB;YAC5B,IAAIC,OAAOE,MAAM,KAAK,GAAG;gBACvBJ,UAAUd,UAAUgB;gBACpBA,SAAS;YACX;YAEA,IAAIG,SAAS,KAAK;gBAChB,IAAIH,WAAW,IAAI;oBACjB,MAAM,IAAIb,MAAM,AAAC,mBAAiBa,SAAO;gBAC3C;gBAEAD;YACF,OAAO,IAAII,SAAS,KAAK;gBACvB,IAAIH,WAAW,IAAI;oBACjB,MAAM,IAAIb,MAAM,AAAC,mBAAiBa,SAAO;gBAC3C;gBAEAD;YACF,OAAO;gBACLC,UAAUG;YACZ;QACF,OAAO,IAAIJ,YAAuB;YAChC,IAAII,SAAS,KAAK;gBAChB,MAAM,IAAIhB,MAAM,AAAC,mBAAiBa,CAAAA,SAASG,IAAG,IAAE;YAClD,OAAO,IAAIA,SAAS,KAAK;gBACvBL,UAAUd,UAAUgB;gBACpBA,SAAS;gBAETD;YACF,OAAO;gBACLC,UAAUG;YACZ;QACF;IACF;IAEA,OAAOL;AACT;AAEO,MAAMhB,yBAAyB"}