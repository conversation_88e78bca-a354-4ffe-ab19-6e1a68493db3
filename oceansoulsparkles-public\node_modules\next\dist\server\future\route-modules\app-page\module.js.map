{"version": 3, "sources": ["../../../../../src/server/future/route-modules/app-page/module.ts"], "names": ["AppPageRouteModule", "renderToHTMLOrFlight", "vendored", "vendoredReactRSC", "vendoredReactSSR", "process", "env", "NEXT_RUNTIME", "require", "RouteModule", "render", "req", "res", "context", "page", "query", "renderOpts", "contexts", "vendoredContexts"], "mappings": ";;;;;;;;;;;;;;;;;IAiDaA,kBAAkB;eAAlBA;;IA2Bb,OAAiC;eAAjC;;IAFSC,oBAAoB;eAApBA,+BAAoB;;IAAEC,QAAQ;eAARA;;;2BAnEM;6BAK9B;qEAC2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,IAAIC;AACJ,IAAIC;AAEJ,gFAAgF;AAChF,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCJ,mBAAmBK,QAAQ;IAC3BJ,mBAAmBI,QAAQ;AAC7B;AA2BO,MAAMR,2BAA2BS,wBAAW;IAI1CC,OACLC,GAAoB,EACpBC,GAAmB,EACnBC,OAAmC,EACZ;QACvB,OAAOZ,IAAAA,+BAAoB,EACzBU,KACAC,KACAC,QAAQC,IAAI,EACZD,QAAQE,KAAK,EACbF,QAAQG,UAAU;IAEtB;AACF;AAEA,MAAMd,WAAW;IACf,aAAaC;IACb,aAAaC;IACba,UAAUC;AACZ;MAIA,WAAelB"}