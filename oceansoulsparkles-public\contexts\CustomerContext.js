import React, { createContext, useContext, useState, useEffect } from 'react';
import { customerAuth, getCurrentCustomer } from '@/lib/supabase';
import { toast } from 'react-toastify';

const CustomerContext = createContext();

export function useCustomer() {
  const context = useContext(CustomerContext);
  if (!context) {
    throw new Error('useCustomer must be used within a CustomerProvider');
  }
  return context;
}

export function CustomerProvider({ children }) {
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize customer authentication state
  useEffect(() => {
    let mounted = true;

    async function initializeCustomerAuth() {
      try {
        console.log('[Customer Auth] Initializing customer authentication');
        
        const currentCustomer = await getCurrentCustomer();
        
        if (mounted) {
          setCustomer(currentCustomer);
          setLoading(false);
          
          if (currentCustomer) {
            console.log('[Customer Auth] Customer authenticated:', currentCustomer.email);
          } else {
            console.log('[Customer Auth] No authenticated customer');
          }
        }
      } catch (error) {
        console.error('[Customer Auth] Error initializing:', error);
        if (mounted) {
          setError(error.message);
          setLoading(false);
        }
      }
    }

    initializeCustomerAuth();

    return () => {
      mounted = false;
    };
  }, []);

  // Customer sign in
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await customerAuth.signIn(email, password);

      if (error) {
        throw error;
      }

      setCustomer(data.user);
      toast.success('Welcome back!');
      
      return { success: true, data };
    } catch (error) {
      console.error('[Customer Auth] Sign in error:', error);
      setError(error.message);
      toast.error(error.message || 'Sign in failed');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Customer sign up
  const signUp = async (email, password, metadata = {}) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await customerAuth.signUp(email, password, metadata);

      if (error) {
        throw error;
      }

      // Note: Customer will need to verify email before being fully authenticated
      toast.success('Account created! Please check your email to verify your account.');
      
      return { success: true, data };
    } catch (error) {
      console.error('[Customer Auth] Sign up error:', error);
      setError(error.message);
      toast.error(error.message || 'Sign up failed');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Customer sign out
  const signOut = async () => {
    try {
      setLoading(true);
      
      const { error } = await customerAuth.signOut();

      if (error) {
        throw error;
      }

      setCustomer(null);
      setError(null);
      toast.success('Signed out successfully');
      
      return { success: true };
    } catch (error) {
      console.error('[Customer Auth] Sign out error:', error);
      toast.error('Sign out failed');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Guest checkout (no account required)
  const createGuestBooking = async (bookingData) => {
    try {
      setLoading(true);
      
      // For guest bookings, we don't require authentication
      // This will be handled by the booking API endpoint
      console.log('[Customer] Creating guest booking:', bookingData);
      
      return { success: true, isGuest: true };
    } catch (error) {
      console.error('[Customer] Guest booking error:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const value = {
    // State
    customer,
    loading,
    error,
    isAuthenticated: !!customer,
    
    // Actions
    signIn,
    signUp,
    signOut,
    createGuestBooking,
    
    // Helper functions
    getCustomerEmail: () => customer?.email || null,
    getCustomerId: () => customer?.id || null,
    isCustomerVerified: () => customer?.email_confirmed_at ? true : false
  };

  return (
    <CustomerContext.Provider value={value}>
      {children}
    </CustomerContext.Provider>
  );
}
