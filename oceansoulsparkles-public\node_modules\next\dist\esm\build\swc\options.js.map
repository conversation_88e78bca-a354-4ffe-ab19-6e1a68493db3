{"version": 3, "sources": ["../../../src/build/swc/options.ts"], "names": ["WEBPACK_LAYERS", "nextDistPath", "nodeModulesPath", "regeneratorRuntimePath", "require", "resolve", "isTypeScriptFile", "filename", "endsWith", "isCommonJSFile", "shouldOutputCommonJs", "test", "getParserOptions", "jsConfig", "rest", "isTSFile", "hasTsSyntax", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "esm", "modularizeImports", "swcPlugins", "resolvedBaseUrl", "swcCacheDir", "serverComponents", "bundleLayer", "isReactServerLayer", "reactServerComponents", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "experimental", "keepImportAttributes", "emitAssertForImportAttributes", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "emotion", "runtime", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "removeConsole", "reactRemoveProperties", "Object", "fromEntries", "entries", "mod", "config", "key", "value", "relay", "styledJsx", "getEmotionOptions", "styledComponents", "getStyledComponentsOptions", "serverActions", "enabled", "hashSalt", "preferEsm", "styledComponentsConfig", "displayName", "emotionConfig", "autoLabel", "sourcemap", "importMap", "labelFormat", "sourceMap", "getJestSWCOptions", "isServer", "pagesDir", "baseOptions", "useCjsModules", "env", "targets", "node", "module", "type", "disableNextSsg", "disablePageConfig", "getLoaderSWCOptions", "appDir", "isPageFile", "optimizeServerReact", "optimizePackageImports", "supportedBrowsers", "relativeFilePathFromRoot", "fontLoaders", "cjsRequireOptimizer", "packages", "transforms", "NextRequest", "NextResponse", "ImageResponse", "userAgentFromString", "userAgent", "optimize_use_state", "autoModularizeImports", "isNodeModules", "isAppBrowserLayer", "appPagesBrowser", "moduleResolutionConfig", "isDevelopment", "isServerCompiler", "length", "target"], "mappings": "AAAA,SAASA,cAAc,QAA+B,sBAAqB;AAS3E,MAAMC,eACJ;AAEF,MAAMC,kBAAkB;AAExB,MAAMC,yBAAyBC,QAAQC,OAAO,CAC5C;AAGF,SAASC,iBAAiBC,QAAgB;IACxC,OAAOA,SAASC,QAAQ,CAAC,UAAUD,SAASC,QAAQ,CAAC;AACvD;AAEA,SAASC,eAAeF,QAAgB;IACtC,OAAOA,SAASC,QAAQ,CAAC;AAC3B;AAEA,qEAAqE;AACrE,mHAAmH;AACnH,SAASE,qBAAqBH,QAAgB;IAC5C,OAAOE,eAAeF,aAAaN,aAAaU,IAAI,CAACJ;AACvD;AAEA,OAAO,SAASK,iBAAiB,EAAEL,QAAQ,EAAEM,QAAQ,EAAE,GAAGC,MAAW;QAIjED;IAHF,MAAME,WAAWR,SAASC,QAAQ,CAAC;IACnC,MAAMQ,cAAcV,iBAAiBC;IACrC,MAAMU,mBAAmBC,QACvBL,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2BO,sBAAsB;IAEnD,OAAO;QACL,GAAGN,IAAI;QACPO,QAAQL,cAAc,eAAe;QACrCM,eAAe;QACfC,YAAYN;QACZ,qKAAqK;QACrK,CAACD,cAAc,QAAQ,MAAM,EAAE,CAACD;QAChCS,kBAAkB;IACpB;AACF;AAEA,SAASC,kBAAkB,EACzBlB,QAAQ,EACRmB,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,YAAY,EACZC,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfc,eAAe,EACfpB,QAAQ,EACRqB,WAAW,EACXC,gBAAgB,EAChBC,WAAW,EAgBZ;QAIevB,2BAEZA,4BAGAA,4BAGAA,4BAoCQA;IA/CV,MAAMwB,qBACJD,gBAAgBpC,eAAesC,qBAAqB;IACtD,MAAMC,eAAe3B,iBAAiB;QAAEL;QAAUM;IAAS;IAC3D,MAAM2B,QAAQ3B,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2B2B,KAAK;IAC9C,MAAMvB,mBAAmBC,QACvBL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BO,sBAAsB;IAEnD,MAAMqB,wBAAwBvB,QAC5BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2B4B,qBAAqB;IAElD,MAAMC,0BAA0BxB,QAC9BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2B6B,uBAAuB;IAEpD,MAAMC,UAAU,AAACX,CAAAA,cAAc,EAAE,AAAD,EAC7BY,MAAM,CAACC,MAAMC,OAAO,EACpBC,GAAG,CAAC,CAAC,CAACC,MAAMC,QAAa,GAAK;YAAC7C,QAAQC,OAAO,CAAC2C;YAAOC;SAAQ;IAEjE,OAAO;QACLC,KAAK;YACH,GAAIjB,mBAAmBO,QACnB;gBACEW,SAASlB,gBAAgBkB,OAAO;gBAChCX;YACF,IACA,CAAC,CAAC;YACNY,iBAAiB,CAACC,QAAQC,QAAQ,CAACC,GAAG,IAAI,CAAC7B;YAC3C8B,QAAQjB;YACRkB,cAAc;gBACZC,sBAAsB;gBACtBC,+BAA+B;gBAC/BhB;gBACAiB,WAAW1B;YACb;YACA2B,WAAW;gBACT,sIAAsI;gBACtI,GAAInC,OACA;oBACEoC,QAAQ;wBACNpC,MAAM;oBACR;gBACF,IACA,CAAC,CAAC;gBACNqC,iBAAiB9C;gBACjB+C,mBAAmBvB;gBACnBC,yBAAyBA;gBACzBuB,OAAO;oBACLC,cACErD,CAAAA,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BsD,eAAe,KACzChD,CAAAA,CAAAA,mCAAAA,gBAAiBiD,OAAO,KAAI,CAAC/B,qBAC1B,mBACA,OAAM;oBACZgC,SAAS;oBACTC,YAAY;oBACZC,kBAAkB;oBAClB5C,aAAa,CAAC,CAACA;oBACf6C,aAAa;oBACbC,SAAS,CAAC,CAAC7C;gBACb;gBACA8C,WAAW;oBACTC,UAAU;oBACVC,SAASlD,OACL,OACA;wBACEmD,SAAS;4BACPC,QAAQjD,eAAe,WAAW;wBACpC;wBACAkD,MAAM;4BACJC,UAAUrD,cAAc,kBAAkB;wBAC5C;oBAEF;gBACN;gBACAsD,aAAa;oBACXC,YAAY/E;gBACd;YACF;QACF;QACAgF,YAAYzD,OAAO,WAAW0D;QAC9BC,aAAa,EAAElE,mCAAAA,gBAAiBkE,aAAa;QAC7C,sDAAsD;QACtD,yDAAyD;QACzDC,uBAAuB5D,OACnB,QACAP,mCAAAA,gBAAiBmE,qBAAqB;QAC1C,wCAAwC;QACxCvD,mBAAmBA,oBACfwD,OAAOC,WAAW,CAChBD,OAAOE,OAAO,CAAC1D,mBAAmBgB,GAAG,CAAC,CAAC,CAAC2C,KAAKC,OAAO,GAAK;gBACvDD;gBACA;oBACE,GAAGC,MAAM;oBACT9B,WACE,OAAO8B,OAAO9B,SAAS,KAAK,WACxB8B,OAAO9B,SAAS,GAChB0B,OAAOE,OAAO,CAACE,OAAO9B,SAAS,EAAEd,GAAG,CAAC,CAAC,CAAC6C,KAAKC,MAAM,GAAK;4BACrDD;4BACAC;yBACD;gBACT;aACD,KAEHT;QACJU,KAAK,EAAE3E,mCAAAA,gBAAiB2E,KAAK;QAC7B,kFAAkF;QAClFC,WAAW,CAAC;QACZ,2GAA2G;QAC3G,GAAI,CAAC1D,sBAAsB;YACzB,mEAAmE;YACnE+B,SAAS4B,kBAAkB7E,mCAAAA,gBAAiBiD,OAAO,EAAEzC;YACrD,mEAAmE;YACnEsE,kBAAkBC,2BAChB/E,mCAAAA,gBAAiB8E,gBAAgB,EACjCtE;QAEJ,CAAC;QACDQ,kBACEA,oBAAoB,CAACT,OACjB;YACEW;QACF,IACA+C;QACNe,eACEhE,oBAAoB,CAACT,OACjB;YACE,+BAA+B;YAC/B,2BAA2B;YAC3B0E,SAAS;YACT/D;YACAgE,UAAU;QACZ,IACAjB;QACN,0CAA0C;QAC1C,gDAAgD;QAChDkB,WAAWxE;IACb;AACF;AAEA,SAASoE,2BACPK,sBAAoE,EACpE5E,WAAgB;IAEhB,IAAI,CAAC4E,wBAAwB;QAC3B,OAAO;IACT,OAAO,IAAI,OAAOA,2BAA2B,UAAU;QACrD,OAAO;YACL,GAAGA,sBAAsB;YACzBC,aAAaD,uBAAuBC,WAAW,IAAItF,QAAQS;QAC7D;IACF,OAAO;QACL,OAAO;YACL6E,aAAatF,QAAQS;QACvB;IACF;AACF;AAEA,SAASqE,kBACPS,aAAkD,EAClD9E,WAAoB;IAEpB,IAAI,CAAC8E,eAAe;QAClB,OAAO;IACT;IACA,IAAIC,YAAY,CAAC,CAAC/E;IAClB,OAAQ,OAAO8E,kBAAkB,YAAYA,cAAcC,SAAS;QAClE,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;QACL;YACE;IACJ;IACA,OAAO;QACLN,SAAS;QACTM;QACAC,WAAWhF;QACX,GAAI,OAAO8E,kBAAkB,YAAY;YACvCG,WAAWH,cAAcG,SAAS;YAClCC,aAAaJ,cAAcI,WAAW;YACtCF,WAAWhF,eAAe8E,cAAcK,SAAS;QACnD,CAAC;IACH;AACF;AAEA,OAAO,SAASC,kBAAkB,EAChCC,QAAQ,EACRzG,QAAQ,EACRuB,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRoB,eAAe,EACfgF,QAAQ,EAYT;IACC,IAAIC,cAAczF,kBAAkB;QAClClB;QACAmB,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,cAAc,CAACmF;QACfjF;QACAC;QACAb;QACAN;QACAoB;QACAH;QACA,oDAAoD;QACpD,oDAAoD;QACpDM,aAAagD;QACbjD,kBAAkB;IACpB;IAEA,MAAMgF,gBAAgBzG,qBAAqBH;IAC3C,OAAO;QACL,GAAG2G,WAAW;QACdE,KAAK;YACHC,SAAS;gBACP,yCAAyC;gBACzCC,MAAMjE,QAAQC,QAAQ,CAACgE,IAAI;YAC7B;QACF;QACAC,QAAQ;YACNC,MAAM1F,OAAO,CAACqF,gBAAgB,QAAQ;QACxC;QACAM,gBAAgB;QAChBC,mBAAmB;QACnBT;IACF;AACF;AAEA,OAAO,SAASU,oBAAoB,EAClC,+EAA+E;AAC/E,mBAAmB;AACnBpH,QAAQ,EACRoB,WAAW,EACXqF,QAAQ,EACRC,QAAQ,EACRW,MAAM,EACNC,UAAU,EACVjG,eAAe,EACfG,iBAAiB,EACjB+F,mBAAmB,EACnBC,sBAAsB,EACtB/F,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRmH,iBAAiB,EACjB9F,WAAW,EACX+F,wBAAwB,EACxB9F,gBAAgB,EAChBC,WAAW,EACXN,GAAG,EAuBJ;IACC,IAAIoF,cAAmBzF,kBAAkB;QACvClB;QACAoB;QACAE,cAAc,CAACmF;QACfpF;QACAG;QACAC;QACAb;QACAN;QACA,mBAAmB;QACnBqB;QACAE;QACAD;QACAL,KAAK,CAAC,CAACA;IACT;IACAoF,YAAYgB,WAAW,GAAG;QACxBA,aAAa;YACX;YACA;YAEA,8CAA8C;YAC9C;YACA;SACD;QACDD;IACF;IACAf,YAAYiB,mBAAmB,GAAG;QAChCC,UAAU;YACR,eAAe;gBACbC,YAAY;oBACVC,aAAa;oBACbC,cAAc;oBACdC,eAAe;oBACfC,qBAAqB;oBACrBC,WAAW;gBACb;YACF;QACF;IACF;IAEA,IAAIZ,uBAAuBd,YAAY,CAACrF,aAAa;QACnDuF,YAAYY,mBAAmB,GAAG;YAChCa,oBAAoB;QACtB;IACF;IAEA,kDAAkD;IAClD,IAAIZ,wBAAwB;QAC1Bb,YAAY0B,qBAAqB,GAAG;YAClCR,UAAUL;QACZ;IACF;IAEA,MAAMc,gBAAgB3I,gBAAgBS,IAAI,CAACJ;IAC3C,MAAMuI,oBAAoB1G,gBAAgBpC,eAAe+I,eAAe;IACxE,MAAMC,yBAAyBtI,qBAAqBH,YAChD;QACEgH,QAAQ;YACNC,MAAM;QACR;IACF,IACA,CAAC;IAEL,IAAIvE;IACJ,IAAI+D,UAAU;QACZ/D,UAAU;YACR,GAAGiE,WAAW;YACd,GAAG8B,sBAAsB;YACzB,8FAA8F;YAC9FvB,gBAAgB;YAChBC,mBAAmB;YACnBuB,eAAetH;YACfuH,kBAAkBlC;YAClBC;YACAW;YACAtB,WAAW,CAAC,CAACxE;YACb+F;YACAT,KAAK;gBACHC,SAAS;oBACP,yCAAyC;oBACzCC,MAAMjE,QAAQC,QAAQ,CAACgE,IAAI;gBAC7B;YACF;QACF;IACF,OAAO;QACLrE,UAAU;YACR,GAAGiE,WAAW;YACd,GAAG8B,sBAAsB;YACzBvB,gBAAgB,CAACI;YACjBoB,eAAetH;YACfuH,kBAAkBlC;YAClBC;YACAW;YACAC;YACA,GAAIG,qBAAqBA,kBAAkBmB,MAAM,GAAG,IAChD;gBACE/B,KAAK;oBACHC,SAASW;gBACX;YACF,IACA,CAAC,CAAC;QACR;QACA,IAAI,CAAC/E,QAAQmE,GAAG,EAAE;YAChB,6CAA6C;YAC7CnE,QAAQC,GAAG,CAACkG,MAAM,GAAG;QACvB;IACF;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,IAAIN,qBAAqBD,eAAe;YAQlC5F;QAPJA,QAAQwE,cAAc,GAAG;QACzBxE,QAAQyE,iBAAiB,GAAG;QAC5BzE,QAAQ4E,UAAU,GAAG;QACrB5E,QAAQ6E,mBAAmB,GAAG1C;QAC9BnC,QAAQkF,mBAAmB,GAAG/C;QAC9B,6FAA6F;QAC7F,uEAAuE;QACvE,KAAInC,2CAAAA,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,qBAAvC3B,yCAAyC4B,OAAO,EAAE;YACpD,OAAO5B,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,CAACC,OAAO,CAACC,MAAM;QAC/D;IACF;IAEA,OAAO7B;AACT"}