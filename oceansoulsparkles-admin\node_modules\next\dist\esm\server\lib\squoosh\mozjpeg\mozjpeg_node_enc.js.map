{"version": 3, "sources": ["../../../../../src/server/lib/squoosh/mozjpeg/mozjpeg_node_enc.js"], "names": ["<PERSON><PERSON><PERSON>", "readyPromiseResolve", "readyPromiseReject", "Promise", "resolve", "reject", "moduleOverrides", "key", "hasOwnProperty", "arguments_", "thisProgram", "quit_", "status", "toThrow", "ENVIRONMENT_IS_WEB", "ENVIRONMENT_IS_WORKER", "ENVIRONMENT_IS_NODE", "scriptDirectory", "locateFile", "path", "read_", "readBinary", "nodeFS", "nodePath", "require", "dirname", "__dirname", "shell_read", "filename", "binary", "ret", "buffer", "Uint8Array", "assert", "process", "length", "replace", "slice", "out", "console", "log", "bind", "err", "warn", "tempRet0", "setTempRet0", "value", "wasmBinary", "noExitRuntime", "WebAssembly", "abort", "was<PERSON><PERSON><PERSON><PERSON>", "ABORT", "EXITSTATUS", "condition", "text", "UTF8Decoder", "TextDecoder", "UTF8ArrayToString", "heap", "idx", "maxBytesToRead", "endIdx", "endPtr", "decode", "subarray", "UTF8ToString", "ptr", "maxPtr", "end", "HEAPU8", "stringToUTF8Array", "str", "outIdx", "maxBytesToWrite", "startIdx", "i", "u", "charCodeAt", "u1", "stringToUTF8", "outPtr", "lengthBytesUTF8", "len", "UTF16Decoder", "UTF16ToString", "maxIdx", "HEAPU16", "codeUnit", "HEAP16", "String", "fromCharCode", "stringToUTF16", "undefined", "startPtr", "numCharsToWrite", "lengthBytesUTF16", "UTF32ToString", "utf32", "HEAP32", "ch", "stringToUTF32", "trailSurrogate", "lengthBytesUTF32", "writeAsciiToMemory", "dontAdd<PERSON>ull", "HEAP8", "alignUp", "x", "multiple", "HEAPU32", "HEAPF32", "HEAPF64", "updateGlobalBufferAndViews", "buf", "Int8Array", "Int16Array", "Int32Array", "Uint16Array", "Uint32Array", "Float32Array", "Float64Array", "INITIAL_MEMORY", "wasmTable", "__ATPRERUN__", "__ATINIT__", "__ATPOSTRUN__", "runtimeInitialized", "runtimeExited", "preRun", "addOnPreRun", "shift", "callRuntimeCallbacks", "initRuntime", "exitRuntime", "postRun", "addOnPostRun", "cb", "unshift", "addOnInit", "runDependencies", "runDependencyWatcher", "dependenciesFulfilled", "addRunDependency", "id", "removeRunDependency", "clearInterval", "callback", "what", "e", "RuntimeError", "dataURIPrefix", "isDataURI", "startsWith", "wasmBinaryFile", "Error", "getBinary", "file", "getBinaryPromise", "then", "createWasm", "info", "a", "asmLibraryArg", "receiveInstance", "instance", "module", "exports", "receiveInstantiationResult", "result", "instantiateArrayBuffer", "receiver", "instantiate", "reason", "instantiateAsync", "catch", "callbacks", "func", "arg", "get", "runtime<PERSON><PERSON><PERSON>ve<PERSON>ounter", "keepRuntimeAlive", "_atexit", "___cxa_thread_atexit", "a0", "a1", "structRegistrations", "runDestructors", "destructors", "pop", "del", "simpleReadValueFromPointer", "pointer", "awaitingDependencies", "registeredTypes", "typeDependencies", "char_0", "char_9", "makeLegalFunctionName", "name", "f", "createNamedFunction", "body", "Function", "extendError", "baseErrorType", "errorName", "errorClass", "message", "stack", "toString", "prototype", "Object", "create", "constructor", "InternalError", "throwInternalError", "whenDependentTypesAreResolved", "myTypes", "dependentTypes", "getTypeConverters", "for<PERSON>ach", "type", "onComplete", "typeConverters", "myTypeConverters", "registerType", "Array", "unregisteredTypes", "registered", "dt", "push", "__embind_finalize_value_object", "structType", "reg", "rawConstructor", "rawDestructor", "fieldRecords", "fields", "fieldTypes", "map", "field", "getterReturnType", "concat", "setterArgumentType", "fieldName", "getter", "getterContext", "setter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read", "write", "o", "fromWireType", "rv", "toWireType", "TypeError", "argPackAdvance", "readValueFromPointer", "destructorFunction", "__embind_register_bigint", "primitiveType", "size", "minRange", "max<PERSON><PERSON><PERSON>", "getShiftFromSize", "embind_init_charCodes", "codes", "embind_charCodes", "readLatin1String", "c", "BindingError", "throwBindingError", "rawType", "registeredInstance", "options", "ignoreDuplicateRegistrations", "__embind_register_bool", "trueValue", "falseValue", "wt", "emval_free_list", "emval_handle_array", "__emval_decref", "handle", "refcount", "count_emval_handles", "count", "get_first_emval", "init_emval", "__emval_register", "__embind_register_emval", "_embind_repr", "v", "t", "floatReadValueFromPointer", "__embind_register_float", "new_", "argumentList", "dummy", "obj", "r", "apply", "craftInvokerFunction", "humanName", "argTypes", "classType", "cppInvokerFunc", "cppTargetFunc", "argCount", "isClassMethodFunc", "needsDestructorStack", "returns", "argsList", "argsListWired", "invokerFnBody", "dtorStack", "args1", "args2", "paramName", "invokerFunction", "ensureOverloadTable", "proto", "methodName", "overloadTable", "prevFunc", "arguments", "exposePublicSymbol", "numArguments", "heap32VectorToArray", "firstElement", "array", "replacePublicSymbol", "dynCallLegacy", "sig", "args", "call", "dynCall", "includes", "getDynCaller", "<PERSON><PERSON><PERSON><PERSON>", "embind__requireFunction", "signature", "rawFunction", "makeDynCaller", "fp", "UnboundTypeError", "getTypeName", "___getTypeName", "_free", "throwUnboundTypeError", "types", "unboundTypes", "seen", "visit", "join", "__embind_register_function", "rawArgTypesAddr", "rawInvoker", "fn", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "integerReadValueFromPointer", "signed", "readS8FromPointer", "readU8FromPointer", "readS16FromPointer", "readU16FromPointer", "readS32FromPointer", "readU32FromPointer", "__embind_register_integer", "bitshift", "isUnsignedType", "__embind_register_memory_view", "dataTypeIndex", "typeMapping", "TA", "decodeMemoryView", "data", "__embind_register_std_string", "stdStringIsUTF8", "decodeStartPtr", "currentBytePtr", "maxRead", "stringSegment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "valueIsOfTypeString", "Uint8ClampedArray", "_malloc", "charCode", "__embind_register_std_wstring", "charSize", "decodeString", "encodeString", "getHeap", "lengthBytesUTF", "HEAP", "maxReadBytes", "__embind_register_value_object", "constructorSignature", "destructorSignature", "__embind_register_value_object_field", "getterSignature", "setterSignature", "__embind_register_void", "isVoid", "emval_symbols", "getStringOrSymbol", "address", "symbol", "emval_get_global", "globalThis", "__emval_get_global", "__emval_incref", "requireRegisteredType", "impl", "craftEmvalAllocator", "functionBody", "emval_newers", "<PERSON><PERSON><PERSON><PERSON>", "__emval_new", "newer", "_abort", "_emscripten_memcpy_big", "dest", "src", "num", "copyWithin", "emscripten_realloc_buffer", "grow", "byteLength", "_emscripten_resize_heap", "requestedSize", "oldSize", "maxHeapSize", "cutDown", "overGrownHeapSize", "Math", "min", "newSize", "max", "replacement", "ENV", "getExecutableName", "getEnvStrings", "strings", "lang", "navigator", "languages", "env", "USER", "LOGNAME", "PATH", "PWD", "HOME", "LANG", "_", "SYSCALLS", "mappings", "buffers", "printChar", "stream", "curr", "varargs", "getStr", "get64", "low", "high", "_environ_get", "__environ", "environ_buf", "bufSize", "string", "_environ_sizes_get", "penviron_count", "penviron_buf_size", "_exit", "exit", "_fd_close", "fd", "_fd_seek", "offset_low", "offset_high", "whence", "newOffset", "_fd_write", "iov", "iovcnt", "pnum", "j", "_setTempRet0", "val", "B", "l", "p", "y", "b", "m", "z", "g", "k", "n", "h", "d", "s", "A", "w", "q", "asm", "___wasm_call_ctors", "___embind_register_native_and_builtin_types", "dyn<PERSON>all_jiji", "calledRun", "ExitStatus", "runCaller", "run", "doRun", "setTimeout", "implicit", "ready"], "mappings": "AAAA,kBAAkB,GAClB,IAAIA,SAAS,AAAC;IACZ,OAAO,SAAUA,MAAM;QACrBA,SAASA,UAAU,CAAC;QAEpB,IAAIA,SAAS,OAAOA,WAAW,cAAcA,SAAS,CAAC;QACvD,IAAIC,qBAAqBC;QACzBF,MAAM,CAAC,QAAQ,GAAG,IAAIG,QAAQ,SAAUC,OAAO,EAAEC,MAAM;YACrDJ,sBAAsBG;YACtBF,qBAAqBG;QACvB;QACA,IAAIC,kBAAkB,CAAC;QACvB,IAAIC;QACJ,IAAKA,OAAOP,OAAQ;YAClB,IAAIA,OAAOQ,cAAc,CAACD,MAAM;gBAC9BD,eAAe,CAACC,IAAI,GAAGP,MAAM,CAACO,IAAI;YACpC;QACF;QACA,IAAIE,aAAa,EAAE;QACnB,IAAIC,cAAc;QAClB,IAAIC,QAAQ,SAAUC,MAAM,EAAEC,OAAO;YACnC,MAAMA;QACR;QACA,IAAIC,qBAAqB;QACzB,IAAIC,wBAAwB;QAC5B,IAAIC,sBAAsB;QAC1B,IAAIC,kBAAkB;QACtB,SAASC,WAAWC,IAAI;YACtB,IAAInB,MAAM,CAAC,aAAa,EAAE;gBACxB,OAAOA,MAAM,CAAC,aAAa,CAACmB,MAAMF;YACpC;YACA,OAAOA,kBAAkBE;QAC3B;QACA,IAAIC,OAAOC;QACX,IAAIC;QACJ,IAAIC;QACJ,IAAIP,qBAAqB;YACvB,IAAID,uBAAuB;gBACzBE,kBAAkBO,QAAQ,QAAQC,OAAO,CAACR,mBAAmB;YAC/D,OAAO;gBACLA,kBAAkBS,YAAY;YAChC;YACAN,QAAQ,SAASO,WAAWC,QAAQ,EAAEC,MAAM;gBAC1C,IAAI,CAACP,QAAQA,SAASE,QAAQ;gBAC9B,IAAI,CAACD,UAAUA,WAAWC,QAAQ;gBAClCI,WAAWL,QAAQ,CAAC,YAAY,CAACK;gBACjC,OAAON,MAAM,CAAC,eAAe,CAACM,UAAUC,SAAS,OAAO;YAC1D;YACAR,aAAa,SAASA,WAAWO,QAAQ;gBACvC,IAAIE,MAAMV,MAAMQ,UAAU;gBAC1B,IAAI,CAACE,IAAIC,MAAM,EAAE;oBACfD,MAAM,IAAIE,WAAWF;gBACvB;gBACAG,OAAOH,IAAIC,MAAM;gBACjB,OAAOD;YACT;YACA,IAAII,OAAO,CAAC,OAAO,CAACC,MAAM,GAAG,GAAG;gBAC9BzB,cAAcwB,OAAO,CAAC,OAAO,CAAC,EAAE,CAACE,OAAO,CAAC,OAAO;YAClD;YACA3B,aAAayB,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC;YACnC1B,QAAQ,SAAUC,MAAM;gBACtBsB,OAAO,CAAC,OAAO,CAACtB;YAClB;YACAZ,MAAM,CAAC,UAAU,GAAG;gBAClB,OAAO;YACT;QACF,OAAO,CACP;QACA,IAAIsC,MAAMtC,MAAM,CAAC,QAAQ,IAAIuC,QAAQC,GAAG,CAACC,IAAI,CAACF;QAC9C,IAAIG,MAAM1C,MAAM,CAAC,WAAW,IAAIuC,QAAQI,IAAI,CAACF,IAAI,CAACF;QAClD,IAAKhC,OAAOD,gBAAiB;YAC3B,IAAIA,gBAAgBE,cAAc,CAACD,MAAM;gBACvCP,MAAM,CAACO,IAAI,GAAGD,eAAe,CAACC,IAAI;YACpC;QACF;QACAD,kBAAkB;QAClB,IAAIN,MAAM,CAAC,YAAY,EAAES,aAAaT,MAAM,CAAC,YAAY;QACzD,IAAIA,MAAM,CAAC,cAAc,EAAEU,cAAcV,MAAM,CAAC,cAAc;QAC9D,IAAIA,MAAM,CAAC,OAAO,EAAEW,QAAQX,MAAM,CAAC,OAAO;QAC1C,IAAI4C,WAAW;QACf,IAAIC,cAAc,SAAUC,KAAK;YAC/BF,WAAWE;QACb;QACA,IAAIC;QACJ,IAAI/C,MAAM,CAAC,aAAa,EAAE+C,aAAa/C,MAAM,CAAC,aAAa;QAC3D,IAAIgD,gBAAgBhD,MAAM,CAAC,gBAAgB,IAAI;QAC/C,IAAI,OAAOiD,gBAAgB,UAAU;YACnCC,MAAM;QACR;QACA,IAAIC;QACJ,IAAIC,QAAQ;QACZ,IAAIC;QACJ,SAASpB,OAAOqB,SAAS,EAAEC,IAAI;YAC7B,IAAI,CAACD,WAAW;gBACdJ,MAAM,uBAAuBK;YAC/B;QACF;QACA,IAAIC,cAAc,IAAIC,YAAY;QAClC,SAASC,kBAAkBC,IAAI,EAAEC,GAAG,EAAEC,cAAc;YAClD,IAAIC,SAASF,MAAMC;YACnB,IAAIE,SAASH;YACb,MAAOD,IAAI,CAACI,OAAO,IAAI,CAAEA,CAAAA,UAAUD,MAAK,EAAI,EAAEC;YAC9C,OAAOP,YAAYQ,MAAM,CACvBL,KAAKM,QAAQ,GACTN,KAAKM,QAAQ,CAACL,KAAKG,UACnB,IAAI/B,WAAW2B,KAAKtB,KAAK,CAACuB,KAAKG;QAEvC;QACA,SAASG,aAAaC,GAAG,EAAEN,cAAc;YACvC,IAAI,CAACM,KAAK,OAAO;YACjB,IAAIC,SAASD,MAAMN;YACnB,IAAK,IAAIQ,MAAMF,KAAK,CAAEE,CAAAA,OAAOD,MAAK,KAAME,MAAM,CAACD,IAAI,EAAI,EAAEA;YACzD,OAAOb,YAAYQ,MAAM,CAACM,OAAOL,QAAQ,CAACE,KAAKE;QACjD;QACA,SAASE,kBAAkBC,GAAG,EAAEb,IAAI,EAAEc,MAAM,EAAEC,eAAe;YAC3D,IAAI,CAAEA,CAAAA,kBAAkB,CAAA,GAAI,OAAO;YACnC,IAAIC,WAAWF;YACf,IAAIX,SAASW,SAASC,kBAAkB;YACxC,IAAK,IAAIE,IAAI,GAAGA,IAAIJ,IAAIrC,MAAM,EAAE,EAAEyC,EAAG;gBACnC,IAAIC,IAAIL,IAAIM,UAAU,CAACF;gBACvB,IAAIC,KAAK,SAASA,KAAK,OAAO;oBAC5B,IAAIE,KAAKP,IAAIM,UAAU,CAAC,EAAEF;oBAC1BC,IAAI,AAAC,QAAS,CAAA,AAACA,CAAAA,IAAI,IAAG,KAAM,EAAC,IAAOE,KAAK;gBAC3C;gBACA,IAAIF,KAAK,KAAK;oBACZ,IAAIJ,UAAUX,QAAQ;oBACtBH,IAAI,CAACc,SAAS,GAAGI;gBACnB,OAAO,IAAIA,KAAK,MAAM;oBACpB,IAAIJ,SAAS,KAAKX,QAAQ;oBAC1BH,IAAI,CAACc,SAAS,GAAG,MAAOI,KAAK;oBAC7BlB,IAAI,CAACc,SAAS,GAAG,MAAOI,IAAI;gBAC9B,OAAO,IAAIA,KAAK,OAAO;oBACrB,IAAIJ,SAAS,KAAKX,QAAQ;oBAC1BH,IAAI,CAACc,SAAS,GAAG,MAAOI,KAAK;oBAC7BlB,IAAI,CAACc,SAAS,GAAG,MAAO,AAACI,KAAK,IAAK;oBACnClB,IAAI,CAACc,SAAS,GAAG,MAAOI,IAAI;gBAC9B,OAAO;oBACL,IAAIJ,SAAS,KAAKX,QAAQ;oBAC1BH,IAAI,CAACc,SAAS,GAAG,MAAOI,KAAK;oBAC7BlB,IAAI,CAACc,SAAS,GAAG,MAAO,AAACI,KAAK,KAAM;oBACpClB,IAAI,CAACc,SAAS,GAAG,MAAO,AAACI,KAAK,IAAK;oBACnClB,IAAI,CAACc,SAAS,GAAG,MAAOI,IAAI;gBAC9B;YACF;YACAlB,IAAI,CAACc,OAAO,GAAG;YACf,OAAOA,SAASE;QAClB;QACA,SAASK,aAAaR,GAAG,EAAES,MAAM,EAAEP,eAAe;YAChD,OAAOH,kBAAkBC,KAAKF,QAAQW,QAAQP;QAChD;QACA,SAASQ,gBAAgBV,GAAG;YAC1B,IAAIW,MAAM;YACV,IAAK,IAAIP,IAAI,GAAGA,IAAIJ,IAAIrC,MAAM,EAAE,EAAEyC,EAAG;gBACnC,IAAIC,IAAIL,IAAIM,UAAU,CAACF;gBACvB,IAAIC,KAAK,SAASA,KAAK,OACrBA,IAAI,AAAC,QAAS,CAAA,AAACA,CAAAA,IAAI,IAAG,KAAM,EAAC,IAAOL,IAAIM,UAAU,CAAC,EAAEF,KAAK;gBAC5D,IAAIC,KAAK,KAAK,EAAEM;qBACX,IAAIN,KAAK,MAAMM,OAAO;qBACtB,IAAIN,KAAK,OAAOM,OAAO;qBACvBA,OAAO;YACd;YACA,OAAOA;QACT;QACA,IAAIC,eAAe,IAAI3B,YAAY;QACnC,SAAS4B,cAAclB,GAAG,EAAEN,cAAc;YACxC,IAAIE,SAASI;YACb,IAAIP,MAAMG,UAAU;YACpB,IAAIuB,SAAS1B,MAAMC,iBAAiB;YACpC,MAAO,CAAED,CAAAA,OAAO0B,MAAK,KAAMC,OAAO,CAAC3B,IAAI,CAAE,EAAEA;YAC3CG,SAASH,OAAO;YAChB,OAAOwB,aAAapB,MAAM,CAACM,OAAOL,QAAQ,CAACE,KAAKJ;YAChD,IAAIS,MAAM;YACV,IAAK,IAAII,IAAI,GAAG,CAAEA,CAAAA,KAAKf,iBAAiB,CAAA,GAAI,EAAEe,EAAG;gBAC/C,IAAIY,WAAWC,MAAM,CAAC,AAACtB,MAAMS,IAAI,KAAM,EAAE;gBACzC,IAAIY,YAAY,GAAG;gBACnBhB,OAAOkB,OAAOC,YAAY,CAACH;YAC7B;YACA,OAAOhB;QACT;QACA,SAASoB,cAAcpB,GAAG,EAAES,MAAM,EAAEP,eAAe;YACjD,IAAIA,oBAAoBmB,WAAW;gBACjCnB,kBAAkB;YACpB;YACA,IAAIA,kBAAkB,GAAG,OAAO;YAChCA,mBAAmB;YACnB,IAAIoB,WAAWb;YACf,IAAIc,kBACFrB,kBAAkBF,IAAIrC,MAAM,GAAG,IAAIuC,kBAAkB,IAAIF,IAAIrC,MAAM;YACrE,IAAK,IAAIyC,IAAI,GAAGA,IAAImB,iBAAiB,EAAEnB,EAAG;gBACxC,IAAIY,WAAWhB,IAAIM,UAAU,CAACF;gBAC9Ba,MAAM,CAACR,UAAU,EAAE,GAAGO;gBACtBP,UAAU;YACZ;YACAQ,MAAM,CAACR,UAAU,EAAE,GAAG;YACtB,OAAOA,SAASa;QAClB;QACA,SAASE,iBAAiBxB,GAAG;YAC3B,OAAOA,IAAIrC,MAAM,GAAG;QACtB;QACA,SAAS8D,cAAc9B,GAAG,EAAEN,cAAc;YACxC,IAAIe,IAAI;YACR,IAAIJ,MAAM;YACV,MAAO,CAAEI,CAAAA,KAAKf,iBAAiB,CAAA,EAAI;gBACjC,IAAIqC,QAAQC,MAAM,CAAC,AAAChC,MAAMS,IAAI,KAAM,EAAE;gBACtC,IAAIsB,SAAS,GAAG;gBAChB,EAAEtB;gBACF,IAAIsB,SAAS,OAAO;oBAClB,IAAIE,KAAKF,QAAQ;oBACjB1B,OAAOkB,OAAOC,YAAY,CAAC,QAASS,MAAM,IAAK,QAASA,KAAK;gBAC/D,OAAO;oBACL5B,OAAOkB,OAAOC,YAAY,CAACO;gBAC7B;YACF;YACA,OAAO1B;QACT;QACA,SAAS6B,cAAc7B,GAAG,EAAES,MAAM,EAAEP,eAAe;YACjD,IAAIA,oBAAoBmB,WAAW;gBACjCnB,kBAAkB;YACpB;YACA,IAAIA,kBAAkB,GAAG,OAAO;YAChC,IAAIoB,WAAWb;YACf,IAAIlB,SAAS+B,WAAWpB,kBAAkB;YAC1C,IAAK,IAAIE,IAAI,GAAGA,IAAIJ,IAAIrC,MAAM,EAAE,EAAEyC,EAAG;gBACnC,IAAIY,WAAWhB,IAAIM,UAAU,CAACF;gBAC9B,IAAIY,YAAY,SAASA,YAAY,OAAO;oBAC1C,IAAIc,iBAAiB9B,IAAIM,UAAU,CAAC,EAAEF;oBACtCY,WACE,AAAC,QAAS,CAAA,AAACA,CAAAA,WAAW,IAAG,KAAM,EAAC,IAAOc,iBAAiB;gBAC5D;gBACAH,MAAM,CAAClB,UAAU,EAAE,GAAGO;gBACtBP,UAAU;gBACV,IAAIA,SAAS,IAAIlB,QAAQ;YAC3B;YACAoC,MAAM,CAAClB,UAAU,EAAE,GAAG;YACtB,OAAOA,SAASa;QAClB;QACA,SAASS,iBAAiB/B,GAAG;YAC3B,IAAIW,MAAM;YACV,IAAK,IAAIP,IAAI,GAAGA,IAAIJ,IAAIrC,MAAM,EAAE,EAAEyC,EAAG;gBACnC,IAAIY,WAAWhB,IAAIM,UAAU,CAACF;gBAC9B,IAAIY,YAAY,SAASA,YAAY,OAAO,EAAEZ;gBAC9CO,OAAO;YACT;YACA,OAAOA;QACT;QACA,SAASqB,mBAAmBhC,GAAG,EAAEzC,MAAM,EAAE0E,WAAW;YAClD,IAAK,IAAI7B,IAAI,GAAGA,IAAIJ,IAAIrC,MAAM,EAAE,EAAEyC,EAAG;gBACnC8B,KAAK,CAAC3E,YAAY,EAAE,GAAGyC,IAAIM,UAAU,CAACF;YACxC;YACA,IAAI,CAAC6B,aAAaC,KAAK,CAAC3E,UAAU,EAAE,GAAG;QACzC;QACA,SAAS4E,QAAQC,CAAC,EAAEC,QAAQ;YAC1B,IAAID,IAAIC,WAAW,GAAG;gBACpBD,KAAKC,WAAYD,IAAIC;YACvB;YACA,OAAOD;QACT;QACA,IAAI7E,QACF2E,OACApC,QACAmB,QACAF,SACAY,QACAW,SACAC,SACAC;QACF,SAASC,2BAA2BC,GAAG;YACrCnF,SAASmF;YACTlH,MAAM,CAAC,QAAQ,GAAG0G,QAAQ,IAAIS,UAAUD;YACxClH,MAAM,CAAC,SAAS,GAAGyF,SAAS,IAAI2B,WAAWF;YAC3ClH,MAAM,CAAC,SAAS,GAAGmG,SAAS,IAAIkB,WAAWH;YAC3ClH,MAAM,CAAC,SAAS,GAAGsE,SAAS,IAAItC,WAAWkF;YAC3ClH,MAAM,CAAC,UAAU,GAAGuF,UAAU,IAAI+B,YAAYJ;YAC9ClH,MAAM,CAAC,UAAU,GAAG8G,UAAU,IAAIS,YAAYL;YAC9ClH,MAAM,CAAC,UAAU,GAAG+G,UAAU,IAAIS,aAAaN;YAC/ClH,MAAM,CAAC,UAAU,GAAGgH,UAAU,IAAIS,aAAaP;QACjD;QACA,IAAIQ,iBAAiB1H,MAAM,CAAC,iBAAiB,IAAI;QACjD,IAAI2H;QACJ,IAAIC,eAAe,EAAE;QACrB,IAAIC,aAAa,EAAE;QACnB,IAAIC,gBAAgB,EAAE;QACtB,IAAIC,qBAAqB;QACzB,IAAIC,gBAAgB;QACpB,SAASC;YACP,IAAIjI,MAAM,CAAC,SAAS,EAAE;gBACpB,IAAI,OAAOA,MAAM,CAAC,SAAS,IAAI,YAC7BA,MAAM,CAAC,SAAS,GAAG;oBAACA,MAAM,CAAC,SAAS;iBAAC;gBACvC,MAAOA,MAAM,CAAC,SAAS,CAACmC,MAAM,CAAE;oBAC9B+F,YAAYlI,MAAM,CAAC,SAAS,CAACmI,KAAK;gBACpC;YACF;YACAC,qBAAqBR;QACvB;QACA,SAASS;YACPN,qBAAqB;YACrBK,qBAAqBP;QACvB;QACA,SAASS;YACPN,gBAAgB;QAClB;QACA,SAASO;YACP,IAAIvI,MAAM,CAAC,UAAU,EAAE;gBACrB,IAAI,OAAOA,MAAM,CAAC,UAAU,IAAI,YAC9BA,MAAM,CAAC,UAAU,GAAG;oBAACA,MAAM,CAAC,UAAU;iBAAC;gBACzC,MAAOA,MAAM,CAAC,UAAU,CAACmC,MAAM,CAAE;oBAC/BqG,aAAaxI,MAAM,CAAC,UAAU,CAACmI,KAAK;gBACtC;YACF;YACAC,qBAAqBN;QACvB;QACA,SAASI,YAAYO,EAAE;YACrBb,aAAac,OAAO,CAACD;QACvB;QACA,SAASE,UAAUF,EAAE;YACnBZ,WAAWa,OAAO,CAACD;QACrB;QACA,SAASD,aAAaC,EAAE;YACtBX,cAAcY,OAAO,CAACD;QACxB;QACA,IAAIG,kBAAkB;QACtB,IAAIC,uBAAuB;QAC3B,IAAIC,wBAAwB;QAC5B,SAASC,iBAAiBC,EAAE;YAC1BJ;YACA,IAAI5I,MAAM,CAAC,yBAAyB,EAAE;gBACpCA,MAAM,CAAC,yBAAyB,CAAC4I;YACnC;QACF;QACA,SAASK,oBAAoBD,EAAE;YAC7BJ;YACA,IAAI5I,MAAM,CAAC,yBAAyB,EAAE;gBACpCA,MAAM,CAAC,yBAAyB,CAAC4I;YACnC;YACA,IAAIA,mBAAmB,GAAG;gBACxB,IAAIC,yBAAyB,MAAM;oBACjCK,cAAcL;oBACdA,uBAAuB;gBACzB;gBACA,IAAIC,uBAAuB;oBACzB,IAAIK,WAAWL;oBACfA,wBAAwB;oBACxBK;gBACF;YACF;QACF;QACAnJ,MAAM,CAAC,kBAAkB,GAAG,CAAC;QAC7BA,MAAM,CAAC,kBAAkB,GAAG,CAAC;QAC7B,SAASkD,MAAMkG,IAAI;YACjB,IAAIpJ,MAAM,CAAC,UAAU,EAAE;gBACrBA,MAAM,CAAC,UAAU,CAACoJ;YACpB;YACAA,QAAQ;YACR1G,IAAI0G;YACJhG,QAAQ;YACRC,aAAa;YACb+F,OAAO,WAAWA,OAAO;YACzB,IAAIC,IAAI,IAAIpG,YAAYqG,YAAY,CAACF;YACrClJ,mBAAmBmJ;YACnB,MAAMA;QACR;QACA,IAAIE,gBAAgB;QACpB,SAASC,UAAU5H,QAAQ;YACzB,OAAOA,SAAS6H,UAAU,CAACF;QAC7B;QACA,IAAIvJ,MAAM,CAAC,aAAa,EAAE;YACxB,IAAI0J,iBAAiB;YACrB,IAAI,CAACF,UAAUE,iBAAiB;gBAC9BA,iBAAiBxI,WAAWwI;YAC9B;QACF,OAAO;YACL,MAAM,IAAIC,MAAM;QAClB;QACA,SAASC,UAAUC,IAAI;YACrB,IAAI;gBACF,IAAIA,QAAQH,kBAAkB3G,YAAY;oBACxC,OAAO,IAAIf,WAAWe;gBACxB;gBACA,IAAI1B,YAAY;oBACd,OAAOA,WAAWwI;gBACpB,OAAO;oBACL,MAAM;gBACR;YACF,EAAE,OAAOnH,KAAK;gBACZQ,MAAMR;YACR;QACF;QACA,SAASoH;YACP,OAAO3J,QAAQC,OAAO,GAAG2J,IAAI,CAAC;gBAC5B,OAAOH,UAAUF;YACnB;QACF;QACA,SAASM;YACP,IAAIC,OAAO;gBAAEC,GAAGC;YAAc;YAC9B,SAASC,gBAAgBC,QAAQ,EAAEC,MAAM;gBACvC,IAAIC,UAAUF,SAASE,OAAO;gBAC9BvK,MAAM,CAAC,MAAM,GAAGuK;gBAChBpH,aAAanD,MAAM,CAAC,MAAM,CAAC,IAAI;gBAC/BiH,2BAA2B9D,WAAWpB,MAAM;gBAC5C4F,YAAY3H,MAAM,CAAC,MAAM,CAAC,IAAI;gBAC9B2I,UAAU3I,MAAM,CAAC,MAAM,CAAC,IAAI;gBAC5BiJ,oBAAoB;YACtB;YACAF,iBAAiB;YACjB,SAASyB,2BAA2BC,MAAM;gBACxCL,gBAAgBK,MAAM,CAAC,WAAW;YACpC;YACA,SAASC,uBAAuBC,QAAQ;gBACtC,OAAOb,mBACJC,IAAI,CAAC,SAAUlI,MAAM;oBACpB,IAAI4I,SAASxH,YAAY2H,WAAW,CAAC/I,QAAQoI;oBAC7C,OAAOQ;gBACT,GACCV,IAAI,CAACY,UAAU,SAAUE,MAAM;oBAC9BnI,IAAI,4CAA4CmI;oBAChD3H,MAAM2H;gBACR;YACJ;YACA,SAASC;gBACP,OAAOJ,uBAAuBF;YAChC;YACA,IAAIxK,MAAM,CAAC,kBAAkB,EAAE;gBAC7B,IAAI;oBACF,IAAIuK,UAAUvK,MAAM,CAAC,kBAAkB,CAACiK,MAAMG;oBAC9C,OAAOG;gBACT,EAAE,OAAOlB,GAAG;oBACV3G,IAAI,wDAAwD2G;oBAC5D,OAAO;gBACT;YACF;YACAyB,mBAAmBC,KAAK,CAAC7K;YACzB,OAAO,CAAC;QACV;QACA,SAASkI,qBAAqB4C,SAAS;YACrC,MAAOA,UAAU7I,MAAM,GAAG,EAAG;gBAC3B,IAAIgH,WAAW6B,UAAU7C,KAAK;gBAC9B,IAAI,OAAOgB,YAAY,YAAY;oBACjCA,SAASnJ;oBACT;gBACF;gBACA,IAAIiL,OAAO9B,SAAS8B,IAAI;gBACxB,IAAI,OAAOA,SAAS,UAAU;oBAC5B,IAAI9B,SAAS+B,GAAG,KAAKrF,WAAW;wBAC9B8B,UAAUwD,GAAG,CAACF;oBAChB,OAAO;wBACLtD,UAAUwD,GAAG,CAACF,MAAM9B,SAAS+B,GAAG;oBAClC;gBACF,OAAO;oBACLD,KAAK9B,SAAS+B,GAAG,KAAKrF,YAAY,OAAOsD,SAAS+B,GAAG;gBACvD;YACF;QACF;QACA,IAAIE,0BAA0B;QAC9B,SAASC;YACP,OAAOrI,iBAAiBoI,0BAA0B;QACpD;QACA,SAASE,QAAQL,IAAI,EAAEC,GAAG,GAAG;QAC7B,SAASK,qBAAqBC,EAAE,EAAEC,EAAE;YAClC,OAAOH,QAAQE,IAAIC;QACrB;QACA,IAAIC,sBAAsB,CAAC;QAC3B,SAASC,eAAeC,WAAW;YACjC,MAAOA,YAAYzJ,MAAM,CAAE;gBACzB,IAAIgC,MAAMyH,YAAYC,GAAG;gBACzB,IAAIC,MAAMF,YAAYC,GAAG;gBACzBC,IAAI3H;YACN;QACF;QACA,SAAS4H,2BAA2BC,OAAO;YACzC,OAAO,IAAI,CAAC,eAAe,CAAClF,OAAO,CAACkF,WAAW,EAAE;QACnD;QACA,IAAIC,uBAAuB,CAAC;QAC5B,IAAIC,kBAAkB,CAAC;QACvB,IAAIC,mBAAmB,CAAC;QACxB,IAAIC,SAAS;QACb,IAAIC,SAAS;QACb,SAASC,sBAAsBC,IAAI;YACjC,IAAI1G,cAAc0G,MAAM;gBACtB,OAAO;YACT;YACAA,OAAOA,KAAKnK,OAAO,CAAC,kBAAkB;YACtC,IAAIoK,IAAID,KAAKzH,UAAU,CAAC;YACxB,IAAI0H,KAAKJ,UAAUI,KAAKH,QAAQ;gBAC9B,OAAO,MAAME;YACf,OAAO;gBACL,OAAOA;YACT;QACF;QACA,SAASE,oBAAoBF,IAAI,EAAEG,IAAI;YACrCH,OAAOD,sBAAsBC;YAC7B,OAAO,IAAII,SACT,QACA,qBACEJ,OACA,WACA,sBACA,8CACA,QACFG;QACJ;QACA,SAASE,YAAYC,aAAa,EAAEC,SAAS;YAC3C,IAAIC,aAAaN,oBAAoBK,WAAW,SAAUE,OAAO;gBAC/D,IAAI,CAACT,IAAI,GAAGO;gBACZ,IAAI,CAACE,OAAO,GAAGA;gBACf,IAAIC,QAAQ,IAAItD,MAAMqD,SAASC,KAAK;gBACpC,IAAIA,UAAUpH,WAAW;oBACvB,IAAI,CAACoH,KAAK,GACR,IAAI,CAACC,QAAQ,KAAK,OAAOD,MAAM7K,OAAO,CAAC,sBAAsB;gBACjE;YACF;YACA2K,WAAWI,SAAS,GAAGC,OAAOC,MAAM,CAACR,cAAcM,SAAS;YAC5DJ,WAAWI,SAAS,CAACG,WAAW,GAAGP;YACnCA,WAAWI,SAAS,CAACD,QAAQ,GAAG;gBAC9B,IAAI,IAAI,CAACF,OAAO,KAAKnH,WAAW;oBAC9B,OAAO,IAAI,CAAC0G,IAAI;gBAClB,OAAO;oBACL,OAAO,IAAI,CAACA,IAAI,GAAG,OAAO,IAAI,CAACS,OAAO;gBACxC;YACF;YACA,OAAOD;QACT;QACA,IAAIQ,gBAAgB1H;QACpB,SAAS2H,mBAAmBR,OAAO;YACjC,MAAM,IAAIO,cAAcP;QAC1B;QACA,SAASS,8BACPC,OAAO,EACPC,cAAc,EACdC,iBAAiB;YAEjBF,QAAQG,OAAO,CAAC,SAAUC,IAAI;gBAC5B3B,gBAAgB,CAAC2B,KAAK,GAAGH;YAC3B;YACA,SAASI,WAAWC,cAAc;gBAChC,IAAIC,mBAAmBL,kBAAkBI;gBACzC,IAAIC,iBAAiB9L,MAAM,KAAKuL,QAAQvL,MAAM,EAAE;oBAC9CqL,mBAAmB;gBACrB;gBACA,IAAK,IAAI5I,IAAI,GAAGA,IAAI8I,QAAQvL,MAAM,EAAE,EAAEyC,EAAG;oBACvCsJ,aAAaR,OAAO,CAAC9I,EAAE,EAAEqJ,gBAAgB,CAACrJ,EAAE;gBAC9C;YACF;YACA,IAAIoJ,iBAAiB,IAAIG,MAAMR,eAAexL,MAAM;YACpD,IAAIiM,oBAAoB,EAAE;YAC1B,IAAIC,aAAa;YACjBV,eAAeE,OAAO,CAAC,SAAUS,EAAE,EAAE1J,CAAC;gBACpC,IAAIsH,gBAAgB1L,cAAc,CAAC8N,KAAK;oBACtCN,cAAc,CAACpJ,EAAE,GAAGsH,eAAe,CAACoC,GAAG;gBACzC,OAAO;oBACLF,kBAAkBG,IAAI,CAACD;oBACvB,IAAI,CAACrC,qBAAqBzL,cAAc,CAAC8N,KAAK;wBAC5CrC,oBAAoB,CAACqC,GAAG,GAAG,EAAE;oBAC/B;oBACArC,oBAAoB,CAACqC,GAAG,CAACC,IAAI,CAAC;wBAC5BP,cAAc,CAACpJ,EAAE,GAAGsH,eAAe,CAACoC,GAAG;wBACvC,EAAED;wBACF,IAAIA,eAAeD,kBAAkBjM,MAAM,EAAE;4BAC3C4L,WAAWC;wBACb;oBACF;gBACF;YACF;YACA,IAAI,MAAMI,kBAAkBjM,MAAM,EAAE;gBAClC4L,WAAWC;YACb;QACF;QACA,SAASQ,+BAA+BC,UAAU;YAChD,IAAIC,MAAMhD,mBAAmB,CAAC+C,WAAW;YACzC,OAAO/C,mBAAmB,CAAC+C,WAAW;YACtC,IAAIE,iBAAiBD,IAAIC,cAAc;YACvC,IAAIC,gBAAgBF,IAAIE,aAAa;YACrC,IAAIC,eAAeH,IAAII,MAAM;YAC7B,IAAIC,aAAaF,aACdG,GAAG,CAAC,SAAUC,KAAK;gBAClB,OAAOA,MAAMC,gBAAgB;YAC/B,GACCC,MAAM,CACLN,aAAaG,GAAG,CAAC,SAAUC,KAAK;gBAC9B,OAAOA,MAAMG,kBAAkB;YACjC;YAEJ3B,8BACE;gBAACgB;aAAW,EACZM,YACA,SAAUA,UAAU;gBAClB,IAAID,SAAS,CAAC;gBACdD,aAAahB,OAAO,CAAC,SAAUoB,KAAK,EAAErK,CAAC;oBACrC,IAAIyK,YAAYJ,MAAMI,SAAS;oBAC/B,IAAIH,mBAAmBH,UAAU,CAACnK,EAAE;oBACpC,IAAI0K,SAASL,MAAMK,MAAM;oBACzB,IAAIC,gBAAgBN,MAAMM,aAAa;oBACvC,IAAIH,qBAAqBL,UAAU,CAACnK,IAAIiK,aAAa1M,MAAM,CAAC;oBAC5D,IAAIqN,SAASP,MAAMO,MAAM;oBACzB,IAAIC,gBAAgBR,MAAMQ,aAAa;oBACvCX,MAAM,CAACO,UAAU,GAAG;wBAClBK,MAAM,SAAUvL,GAAG;4BACjB,OAAO+K,gBAAgB,CAAC,eAAe,CACrCI,OAAOC,eAAepL;wBAE1B;wBACAwL,OAAO,SAAUxL,GAAG,EAAEyL,CAAC;4BACrB,IAAIhE,cAAc,EAAE;4BACpB4D,OACEC,eACAtL,KACAiL,kBAAkB,CAAC,aAAa,CAACxD,aAAagE;4BAEhDjE,eAAeC;wBACjB;oBACF;gBACF;gBACA,OAAO;oBACL;wBACEW,MAAMmC,IAAInC,IAAI;wBACdsD,cAAc,SAAU1L,GAAG;4BACzB,IAAI2L,KAAK,CAAC;4BACV,IAAK,IAAIlL,KAAKkK,OAAQ;gCACpBgB,EAAE,CAAClL,EAAE,GAAGkK,MAAM,CAAClK,EAAE,CAAC8K,IAAI,CAACvL;4BACzB;4BACAyK,cAAczK;4BACd,OAAO2L;wBACT;wBACAC,YAAY,SAAUnE,WAAW,EAAEgE,CAAC;4BAClC,IAAK,IAAIP,aAAaP,OAAQ;gCAC5B,IAAI,CAAEO,CAAAA,aAAaO,CAAAA,GAAI;oCACrB,MAAM,IAAII,UAAU,sBAAsBX,YAAY;gCACxD;4BACF;4BACA,IAAIlL,MAAMwK;4BACV,IAAKU,aAAaP,OAAQ;gCACxBA,MAAM,CAACO,UAAU,CAACM,KAAK,CAACxL,KAAKyL,CAAC,CAACP,UAAU;4BAC3C;4BACA,IAAIzD,gBAAgB,MAAM;gCACxBA,YAAY2C,IAAI,CAACK,eAAezK;4BAClC;4BACA,OAAOA;wBACT;wBACA8L,gBAAgB;wBAChBC,sBAAsBnE;wBACtBoE,oBAAoBvB;oBACtB;iBACD;YACH;QAEJ;QACA,SAASwB,yBACPC,aAAa,EACb9D,IAAI,EACJ+D,IAAI,EACJC,QAAQ,EACRC,QAAQ,GACP;QACH,SAASC,iBAAiBH,IAAI;YAC5B,OAAQA;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,MAAM,IAAIN,UAAU,wBAAwBM;YAChD;QACF;QACA,SAASI;YACP,IAAIC,QAAQ,IAAIxC,MAAM;YACtB,IAAK,IAAIvJ,IAAI,GAAGA,IAAI,KAAK,EAAEA,EAAG;gBAC5B+L,KAAK,CAAC/L,EAAE,GAAGc,OAAOC,YAAY,CAACf;YACjC;YACAgM,mBAAmBD;QACrB;QACA,IAAIC,mBAAmB/K;QACvB,SAASgL,iBAAiB1M,GAAG;YAC3B,IAAIrC,MAAM;YACV,IAAIgP,IAAI3M;YACR,MAAOG,MAAM,CAACwM,EAAE,CAAE;gBAChBhP,OAAO8O,gBAAgB,CAACtM,MAAM,CAACwM,IAAI,CAAC;YACtC;YACA,OAAOhP;QACT;QACA,IAAIiP,eAAelL;QACnB,SAASmL,kBAAkBhE,OAAO;YAChC,MAAM,IAAI+D,aAAa/D;QACzB;QACA,SAASkB,aAAa+C,OAAO,EAAEC,kBAAkB,EAAEC,OAAO;YACxDA,UAAUA,WAAW,CAAC;YACtB,IAAI,CAAE,CAAA,oBAAoBD,kBAAiB,GAAI;gBAC7C,MAAM,IAAIlB,UACR;YAEJ;YACA,IAAIzD,OAAO2E,mBAAmB3E,IAAI;YAClC,IAAI,CAAC0E,SAAS;gBACZD,kBACE,WAAWzE,OAAO;YAEtB;YACA,IAAIL,gBAAgB1L,cAAc,CAACyQ,UAAU;gBAC3C,IAAIE,QAAQC,4BAA4B,EAAE;oBACxC;gBACF,OAAO;oBACLJ,kBAAkB,2BAA2BzE,OAAO;gBACtD;YACF;YACAL,eAAe,CAAC+E,QAAQ,GAAGC;YAC3B,OAAO/E,gBAAgB,CAAC8E,QAAQ;YAChC,IAAIhF,qBAAqBzL,cAAc,CAACyQ,UAAU;gBAChD,IAAIjG,YAAYiB,oBAAoB,CAACgF,QAAQ;gBAC7C,OAAOhF,oBAAoB,CAACgF,QAAQ;gBACpCjG,UAAU6C,OAAO,CAAC,SAAUpF,EAAE;oBAC5BA;gBACF;YACF;QACF;QACA,SAAS4I,uBACPJ,OAAO,EACP1E,IAAI,EACJ+D,IAAI,EACJgB,SAAS,EACTC,UAAU;YAEV,IAAIpJ,QAAQsI,iBAAiBH;YAC7B/D,OAAOsE,iBAAiBtE;YACxB2B,aAAa+C,SAAS;gBACpB1E,MAAMA;gBACNsD,cAAc,SAAU2B,EAAE;oBACxB,OAAO,CAAC,CAACA;gBACX;gBACAzB,YAAY,SAAUnE,WAAW,EAAEgE,CAAC;oBAClC,OAAOA,IAAI0B,YAAYC;gBACzB;gBACAtB,gBAAgB;gBAChBC,sBAAsB,SAAUlE,OAAO;oBACrC,IAAIrI;oBACJ,IAAI2M,SAAS,GAAG;wBACd3M,OAAO+C;oBACT,OAAO,IAAI4J,SAAS,GAAG;wBACrB3M,OAAO8B;oBACT,OAAO,IAAI6K,SAAS,GAAG;wBACrB3M,OAAOwC;oBACT,OAAO;wBACL,MAAM,IAAI6J,UAAU,gCAAgCzD;oBACtD;oBACA,OAAO,IAAI,CAAC,eAAe,CAAC5I,IAAI,CAACqI,WAAW7D,MAAM;gBACpD;gBACAgI,oBAAoB;YACtB;QACF;QACA,IAAIsB,kBAAkB,EAAE;QACxB,IAAIC,qBAAqB;YACvB,CAAC;YACD;gBAAE5O,OAAO+C;YAAU;YACnB;gBAAE/C,OAAO;YAAK;YACd;gBAAEA,OAAO;YAAK;YACd;gBAAEA,OAAO;YAAM;SAChB;QACD,SAAS6O,eAAeC,MAAM;YAC5B,IAAIA,SAAS,KAAK,MAAM,EAAEF,kBAAkB,CAACE,OAAO,CAACC,QAAQ,EAAE;gBAC7DH,kBAAkB,CAACE,OAAO,GAAG/L;gBAC7B4L,gBAAgBlD,IAAI,CAACqD;YACvB;QACF;QACA,SAASE;YACP,IAAIC,QAAQ;YACZ,IAAK,IAAInN,IAAI,GAAGA,IAAI8M,mBAAmBvP,MAAM,EAAE,EAAEyC,EAAG;gBAClD,IAAI8M,kBAAkB,CAAC9M,EAAE,KAAKiB,WAAW;oBACvC,EAAEkM;gBACJ;YACF;YACA,OAAOA;QACT;QACA,SAASC;YACP,IAAK,IAAIpN,IAAI,GAAGA,IAAI8M,mBAAmBvP,MAAM,EAAE,EAAEyC,EAAG;gBAClD,IAAI8M,kBAAkB,CAAC9M,EAAE,KAAKiB,WAAW;oBACvC,OAAO6L,kBAAkB,CAAC9M,EAAE;gBAC9B;YACF;YACA,OAAO;QACT;QACA,SAASqN;YACPjS,MAAM,CAAC,sBAAsB,GAAG8R;YAChC9R,MAAM,CAAC,kBAAkB,GAAGgS;QAC9B;QACA,SAASE,iBAAiBpP,KAAK;YAC7B,OAAQA;gBACN,KAAK+C;oBAAW;wBACd,OAAO;oBACT;gBACA,KAAK;oBAAM;wBACT,OAAO;oBACT;gBACA,KAAK;oBAAM;wBACT,OAAO;oBACT;gBACA,KAAK;oBAAO;wBACV,OAAO;oBACT;gBACA;oBAAS;wBACP,IAAI+L,SAASH,gBAAgBtP,MAAM,GAC/BsP,gBAAgB5F,GAAG,KACnB6F,mBAAmBvP,MAAM;wBAC7BuP,kBAAkB,CAACE,OAAO,GAAG;4BAAEC,UAAU;4BAAG/O,OAAOA;wBAAM;wBACzD,OAAO8O;oBACT;YACF;QACF;QACA,SAASO,wBAAwBlB,OAAO,EAAE1E,IAAI;YAC5CA,OAAOsE,iBAAiBtE;YACxB2B,aAAa+C,SAAS;gBACpB1E,MAAMA;gBACNsD,cAAc,SAAU+B,MAAM;oBAC5B,IAAI9B,KAAK4B,kBAAkB,CAACE,OAAO,CAAC9O,KAAK;oBACzC6O,eAAeC;oBACf,OAAO9B;gBACT;gBACAC,YAAY,SAAUnE,WAAW,EAAE9I,KAAK;oBACtC,OAAOoP,iBAAiBpP;gBAC1B;gBACAmN,gBAAgB;gBAChBC,sBAAsBnE;gBACtBoE,oBAAoB;YACtB;QACF;QACA,SAASiC,aAAaC,CAAC;YACrB,IAAIA,MAAM,MAAM;gBACd,OAAO;YACT;YACA,IAAIC,IAAI,OAAOD;YACf,IAAIC,MAAM,YAAYA,MAAM,WAAWA,MAAM,YAAY;gBACvD,OAAOD,EAAEnF,QAAQ;YACnB,OAAO;gBACL,OAAO,KAAKmF;YACd;QACF;QACA,SAASE,0BAA0BhG,IAAI,EAAEpE,KAAK;YAC5C,OAAQA;gBACN,KAAK;oBACH,OAAO,SAAU6D,OAAO;wBACtB,OAAO,IAAI,CAAC,eAAe,CAACjF,OAAO,CAACiF,WAAW,EAAE;oBACnD;gBACF,KAAK;oBACH,OAAO,SAAUA,OAAO;wBACtB,OAAO,IAAI,CAAC,eAAe,CAAChF,OAAO,CAACgF,WAAW,EAAE;oBACnD;gBACF;oBACE,MAAM,IAAIgE,UAAU,yBAAyBzD;YACjD;QACF;QACA,SAASiG,wBAAwBvB,OAAO,EAAE1E,IAAI,EAAE+D,IAAI;YAClD,IAAInI,QAAQsI,iBAAiBH;YAC7B/D,OAAOsE,iBAAiBtE;YACxB2B,aAAa+C,SAAS;gBACpB1E,MAAMA;gBACNsD,cAAc,SAAU/M,KAAK;oBAC3B,OAAOA;gBACT;gBACAiN,YAAY,SAAUnE,WAAW,EAAE9I,KAAK;oBACtC,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,WAAW;wBAC3D,MAAM,IAAIkN,UACR,qBAAqBoC,aAAatP,SAAS,UAAU,IAAI,CAACyJ,IAAI;oBAElE;oBACA,OAAOzJ;gBACT;gBACAmN,gBAAgB;gBAChBC,sBAAsBqC,0BAA0BhG,MAAMpE;gBACtDgI,oBAAoB;YACtB;QACF;QACA,SAASsC,KAAKnF,WAAW,EAAEoF,YAAY;YACrC,IAAI,CAAEpF,CAAAA,uBAAuBX,QAAO,GAAI;gBACtC,MAAM,IAAIqD,UACR,uCACE,OAAO1C,cACP;YAEN;YACA,IAAIqF,QAAQlG,oBACVa,YAAYf,IAAI,IAAI,uBACpB,YAAa;YAEfoG,MAAMxF,SAAS,GAAGG,YAAYH,SAAS;YACvC,IAAIyF,MAAM,IAAID;YACd,IAAIE,IAAIvF,YAAYwF,KAAK,CAACF,KAAKF;YAC/B,OAAOG,aAAazF,SAASyF,IAAID;QACnC;QACA,SAASG,qBACPC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,aAAa;YAEb,IAAIC,WAAWJ,SAAS9Q,MAAM;YAC9B,IAAIkR,WAAW,GAAG;gBAChBrC,kBACE;YAEJ;YACA,IAAIsC,oBAAoBL,QAAQ,CAAC,EAAE,KAAK,QAAQC,cAAc;YAC9D,IAAIK,uBAAuB;YAC3B,IAAK,IAAI3O,IAAI,GAAGA,IAAIqO,SAAS9Q,MAAM,EAAE,EAAEyC,EAAG;gBACxC,IACEqO,QAAQ,CAACrO,EAAE,KAAK,QAChBqO,QAAQ,CAACrO,EAAE,CAACuL,kBAAkB,KAAKtK,WACnC;oBACA0N,uBAAuB;oBACvB;gBACF;YACF;YACA,IAAIC,UAAUP,QAAQ,CAAC,EAAE,CAAC1G,IAAI,KAAK;YACnC,IAAIkH,WAAW;YACf,IAAIC,gBAAgB;YACpB,IAAK,IAAI9O,IAAI,GAAGA,IAAIyO,WAAW,GAAG,EAAEzO,EAAG;gBACrC6O,YAAY,AAAC7O,CAAAA,MAAM,IAAI,OAAO,EAAC,IAAK,QAAQA;gBAC5C8O,iBAAiB,AAAC9O,CAAAA,MAAM,IAAI,OAAO,EAAC,IAAK,QAAQA,IAAI;YACvD;YACA,IAAI+O,gBACF,qBACArH,sBAAsB0G,aACtB,MACAS,WACA,UACA,8BACCJ,CAAAA,WAAW,CAAA,IACZ,UACA,iCACAL,YACA,+DACCK,CAAAA,WAAW,CAAA,IACZ,gBACA;YACF,IAAIE,sBAAsB;gBACxBI,iBAAiB;YACnB;YACA,IAAIC,YAAYL,uBAAuB,gBAAgB;YACvD,IAAIM,QAAQ;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,IAAIC,QAAQ;gBACV9C;gBACAmC;gBACAC;gBACAzH;gBACAsH,QAAQ,CAAC,EAAE;gBACXA,QAAQ,CAAC,EAAE;aACZ;YACD,IAAIK,mBAAmB;gBACrBK,iBACE,2CAA2CC,YAAY;YAC3D;YACA,IAAK,IAAIhP,IAAI,GAAGA,IAAIyO,WAAW,GAAG,EAAEzO,EAAG;gBACrC+O,iBACE,YACA/O,IACA,oBACAA,IACA,iBACAgP,YACA,UACAhP,IACA,WACAqO,QAAQ,CAACrO,IAAI,EAAE,CAAC2H,IAAI,GACpB;gBACFsH,MAAMtF,IAAI,CAAC,YAAY3J;gBACvBkP,MAAMvF,IAAI,CAAC0E,QAAQ,CAACrO,IAAI,EAAE;YAC5B;YACA,IAAI0O,mBAAmB;gBACrBI,gBACE,cAAeA,CAAAA,cAAcvR,MAAM,GAAG,IAAI,OAAO,EAAC,IAAKuR;YAC3D;YACAC,iBACE,AAACH,CAAAA,UAAU,cAAc,EAAC,IAC1B,eACCE,CAAAA,cAAcvR,MAAM,GAAG,IAAI,OAAO,EAAC,IACpCuR,gBACA;YACF,IAAIH,sBAAsB;gBACxBI,iBAAiB;YACnB,OAAO;gBACL,IAAK,IAAI/O,IAAI0O,oBAAoB,IAAI,GAAG1O,IAAIqO,SAAS9Q,MAAM,EAAE,EAAEyC,EAAG;oBAChE,IAAImP,YAAYnP,MAAM,IAAI,cAAc,QAASA,CAAAA,IAAI,CAAA,IAAK;oBAC1D,IAAIqO,QAAQ,CAACrO,EAAE,CAACuL,kBAAkB,KAAK,MAAM;wBAC3CwD,iBACEI,YACA,WACAA,YACA,WACAd,QAAQ,CAACrO,EAAE,CAAC2H,IAAI,GAChB;wBACFsH,MAAMtF,IAAI,CAACwF,YAAY;wBACvBD,MAAMvF,IAAI,CAAC0E,QAAQ,CAACrO,EAAE,CAACuL,kBAAkB;oBAC3C;gBACF;YACF;YACA,IAAIqD,SAAS;gBACXG,iBACE,0CAA0C;YAC9C,OAAO,CACP;YACAA,iBAAiB;YACjBE,MAAMtF,IAAI,CAACoF;YACX,IAAIK,kBAAkBvB,KAAK9F,UAAUkH,OAAOf,KAAK,CAAC,MAAMgB;YACxD,OAAOE;QACT;QACA,SAASC,oBAAoBC,KAAK,EAAEC,UAAU,EAAEnB,SAAS;YACvD,IAAInN,cAAcqO,KAAK,CAACC,WAAW,CAACC,aAAa,EAAE;gBACjD,IAAIC,WAAWH,KAAK,CAACC,WAAW;gBAChCD,KAAK,CAACC,WAAW,GAAG;oBAClB,IACE,CAACD,KAAK,CAACC,WAAW,CAACC,aAAa,CAAC5T,cAAc,CAAC8T,UAAUnS,MAAM,GAChE;wBACA6O,kBACE,eACEgC,YACA,mDACAsB,UAAUnS,MAAM,GAChB,yBACA+R,KAAK,CAACC,WAAW,CAACC,aAAa,GAC/B;oBAEN;oBACA,OAAOF,KAAK,CAACC,WAAW,CAACC,aAAa,CAACE,UAAUnS,MAAM,CAAC,CAAC2Q,KAAK,CAC5D,IAAI,EACJwB;gBAEJ;gBACAJ,KAAK,CAACC,WAAW,CAACC,aAAa,GAAG,EAAE;gBACpCF,KAAK,CAACC,WAAW,CAACC,aAAa,CAACC,SAAShB,QAAQ,CAAC,GAAGgB;YACvD;QACF;QACA,SAASE,mBAAmBhI,IAAI,EAAEzJ,KAAK,EAAE0R,YAAY;YACnD,IAAIxU,OAAOQ,cAAc,CAAC+L,OAAO;gBAC/B,IACE1G,cAAc2O,gBACb3O,cAAc7F,MAAM,CAACuM,KAAK,CAAC6H,aAAa,IACvCvO,cAAc7F,MAAM,CAACuM,KAAK,CAAC6H,aAAa,CAACI,aAAa,EACxD;oBACAxD,kBAAkB,kCAAkCzE,OAAO;gBAC7D;gBACA0H,oBAAoBjU,QAAQuM,MAAMA;gBAClC,IAAIvM,OAAOQ,cAAc,CAACgU,eAAe;oBACvCxD,kBACE,yFACEwD,eACA;gBAEN;gBACAxU,MAAM,CAACuM,KAAK,CAAC6H,aAAa,CAACI,aAAa,GAAG1R;YAC7C,OAAO;gBACL9C,MAAM,CAACuM,KAAK,GAAGzJ;gBACf,IAAI+C,cAAc2O,cAAc;oBAC9BxU,MAAM,CAACuM,KAAK,CAACiI,YAAY,GAAGA;gBAC9B;YACF;QACF;QACA,SAASC,oBAAoB1C,KAAK,EAAE2C,YAAY;YAC9C,IAAIC,QAAQ,EAAE;YACd,IAAK,IAAI/P,IAAI,GAAGA,IAAImN,OAAOnN,IAAK;gBAC9B+P,MAAMpG,IAAI,CAACpI,MAAM,CAAC,AAACuO,CAAAA,gBAAgB,CAAA,IAAK9P,EAAE;YAC5C;YACA,OAAO+P;QACT;QACA,SAASC,oBAAoBrI,IAAI,EAAEzJ,KAAK,EAAE0R,YAAY;YACpD,IAAI,CAACxU,OAAOQ,cAAc,CAAC+L,OAAO;gBAChCiB,mBAAmB;YACrB;YACA,IACE3H,cAAc7F,MAAM,CAACuM,KAAK,CAAC6H,aAAa,IACxCvO,cAAc2O,cACd;gBACAxU,MAAM,CAACuM,KAAK,CAAC6H,aAAa,CAACI,aAAa,GAAG1R;YAC7C,OAAO;gBACL9C,MAAM,CAACuM,KAAK,GAAGzJ;gBACf9C,MAAM,CAACuM,KAAK,CAAC8G,QAAQ,GAAGmB;YAC1B;QACF;QACA,SAASK,cAAcC,GAAG,EAAE3Q,GAAG,EAAE4Q,IAAI;YACnC,IAAIvI,IAAIxM,MAAM,CAAC,aAAa8U,IAAI;YAChC,OAAOC,QAAQA,KAAK5S,MAAM,GACtBqK,EAAEsG,KAAK,CAAC,MAAM;gBAAC3O;aAAI,CAACgL,MAAM,CAAC4F,SAC3BvI,EAAEwI,IAAI,CAAC,MAAM7Q;QACnB;QACA,SAAS8Q,QAAQH,GAAG,EAAE3Q,GAAG,EAAE4Q,IAAI;YAC7B,IAAID,IAAII,QAAQ,CAAC,MAAM;gBACrB,OAAOL,cAAcC,KAAK3Q,KAAK4Q;YACjC;YACA,OAAOpN,UAAUwD,GAAG,CAAChH,KAAK2O,KAAK,CAAC,MAAMiC;QACxC;QACA,SAASI,aAAaL,GAAG,EAAE3Q,GAAG;YAC5B,IAAIiR,WAAW,EAAE;YACjB,OAAO;gBACLA,SAASjT,MAAM,GAAGmS,UAAUnS,MAAM;gBAClC,IAAK,IAAIyC,IAAI,GAAGA,IAAI0P,UAAUnS,MAAM,EAAEyC,IAAK;oBACzCwQ,QAAQ,CAACxQ,EAAE,GAAG0P,SAAS,CAAC1P,EAAE;gBAC5B;gBACA,OAAOqQ,QAAQH,KAAK3Q,KAAKiR;YAC3B;QACF;QACA,SAASC,wBAAwBC,SAAS,EAAEC,WAAW;YACrDD,YAAYzE,iBAAiByE;YAC7B,SAASE;gBACP,IAAIF,UAAUJ,QAAQ,CAAC,MAAM;oBAC3B,OAAOC,aAAaG,WAAWC;gBACjC;gBACA,OAAO5N,UAAUwD,GAAG,CAACoK;YACvB;YACA,IAAIE,KAAKD;YACT,IAAI,OAAOC,OAAO,YAAY;gBAC5BzE,kBACE,6CACEsE,YACA,OACAC;YAEN;YACA,OAAOE;QACT;QACA,IAAIC,mBAAmB7P;QACvB,SAAS8P,YAAY7H,IAAI;YACvB,IAAI3J,MAAMyR,eAAe9H;YACzB,IAAIgC,KAAKe,iBAAiB1M;YAC1B0R,MAAM1R;YACN,OAAO2L;QACT;QACA,SAASgG,sBAAsB9I,OAAO,EAAE+I,KAAK;YAC3C,IAAIC,eAAe,EAAE;YACrB,IAAIC,OAAO,CAAC;YACZ,SAASC,MAAMpI,IAAI;gBACjB,IAAImI,IAAI,CAACnI,KAAK,EAAE;oBACd;gBACF;gBACA,IAAI5B,eAAe,CAAC4B,KAAK,EAAE;oBACzB;gBACF;gBACA,IAAI3B,gBAAgB,CAAC2B,KAAK,EAAE;oBAC1B3B,gBAAgB,CAAC2B,KAAK,CAACD,OAAO,CAACqI;oBAC/B;gBACF;gBACAF,aAAazH,IAAI,CAACT;gBAClBmI,IAAI,CAACnI,KAAK,GAAG;YACf;YACAiI,MAAMlI,OAAO,CAACqI;YACd,MAAM,IAAIR,iBACR1I,UAAU,OAAOgJ,aAAahH,GAAG,CAAC2G,aAAaQ,IAAI,CAAC;gBAAC;aAAK;QAE9D;QACA,SAASC,2BACP7J,IAAI,EACJ8G,QAAQ,EACRgD,eAAe,EACff,SAAS,EACTgB,UAAU,EACVC,EAAE;YAEF,IAAItD,WAAWwB,oBAAoBpB,UAAUgD;YAC7C9J,OAAOsE,iBAAiBtE;YACxB+J,aAAajB,wBAAwBC,WAAWgB;YAChD/B,mBACEhI,MACA;gBACEuJ,sBACE,iBAAiBvJ,OAAO,yBACxB0G;YAEJ,GACAI,WAAW;YAEb5F,8BAA8B,EAAE,EAAEwF,UAAU,SAAUA,QAAQ;gBAC5D,IAAIuD,mBAAmB;oBAACvD,QAAQ,CAAC,EAAE;oBAAE;iBAAK,CAAC9D,MAAM,CAAC8D,SAAS5Q,KAAK,CAAC;gBACjEuS,oBACErI,MACAwG,qBAAqBxG,MAAMiK,kBAAkB,MAAMF,YAAYC,KAC/DlD,WAAW;gBAEb,OAAO,EAAE;YACX;QACF;QACA,SAASoD,4BAA4BlK,IAAI,EAAEpE,KAAK,EAAEuO,MAAM;YACtD,OAAQvO;gBACN,KAAK;oBACH,OAAOuO,SACH,SAASC,kBAAkB3K,OAAO;wBAChC,OAAOtF,KAAK,CAACsF,QAAQ;oBACvB,IACA,SAAS4K,kBAAkB5K,OAAO;wBAChC,OAAO1H,MAAM,CAAC0H,QAAQ;oBACxB;gBACN,KAAK;oBACH,OAAO0K,SACH,SAASG,mBAAmB7K,OAAO;wBACjC,OAAOvG,MAAM,CAACuG,WAAW,EAAE;oBAC7B,IACA,SAAS8K,mBAAmB9K,OAAO;wBACjC,OAAOzG,OAAO,CAACyG,WAAW,EAAE;oBAC9B;gBACN,KAAK;oBACH,OAAO0K,SACH,SAASK,mBAAmB/K,OAAO;wBACjC,OAAO7F,MAAM,CAAC6F,WAAW,EAAE;oBAC7B,IACA,SAASgL,mBAAmBhL,OAAO;wBACjC,OAAOlF,OAAO,CAACkF,WAAW,EAAE;oBAC9B;gBACN;oBACE,MAAM,IAAIgE,UAAU,2BAA2BzD;YACnD;QACF;QACA,SAAS0K,0BACP5G,aAAa,EACb9D,IAAI,EACJ+D,IAAI,EACJC,QAAQ,EACRC,QAAQ;YAERjE,OAAOsE,iBAAiBtE;YACxB,IAAIiE,aAAa,CAAC,GAAG;gBACnBA,WAAW;YACb;YACA,IAAIrI,QAAQsI,iBAAiBH;YAC7B,IAAIT,eAAe,SAAU/M,KAAK;gBAChC,OAAOA;YACT;YACA,IAAIyN,aAAa,GAAG;gBAClB,IAAI2G,WAAW,KAAK,IAAI5G;gBACxBT,eAAe,SAAU/M,KAAK;oBAC5B,OAAO,AAACA,SAASoU,aAAcA;gBACjC;YACF;YACA,IAAIC,iBAAiB5K,KAAK2I,QAAQ,CAAC;YACnChH,aAAamC,eAAe;gBAC1B9D,MAAMA;gBACNsD,cAAcA;gBACdE,YAAY,SAAUnE,WAAW,EAAE9I,KAAK;oBACtC,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,WAAW;wBAC3D,MAAM,IAAIkN,UACR,qBAAqBoC,aAAatP,SAAS,UAAU,IAAI,CAACyJ,IAAI;oBAElE;oBACA,IAAIzJ,QAAQyN,YAAYzN,QAAQ0N,UAAU;wBACxC,MAAM,IAAIR,UACR,uBACEoC,aAAatP,SACb,0DACAyJ,OACA,0CACAgE,WACA,OACAC,WACA;oBAEN;oBACA,OAAO2G,iBAAiBrU,UAAU,IAAIA,QAAQ;gBAChD;gBACAmN,gBAAgB;gBAChBC,sBAAsBuG,4BACpBlK,MACApE,OACAoI,aAAa;gBAEfJ,oBAAoB;YACtB;QACF;QACA,SAASiH,8BAA8BnG,OAAO,EAAEoG,aAAa,EAAE9K,IAAI;YACjE,IAAI+K,cAAc;gBAChBnQ;gBACAnF;gBACAoF;gBACAE;gBACAD;gBACAE;gBACAC;gBACAC;aACD;YACD,IAAI8P,KAAKD,WAAW,CAACD,cAAc;YACnC,SAASG,iBAAiB5F,MAAM;gBAC9BA,SAASA,UAAU;gBACnB,IAAIjO,OAAOmD;gBACX,IAAIwJ,OAAO3M,IAAI,CAACiO,OAAO;gBACvB,IAAI6F,OAAO9T,IAAI,CAACiO,SAAS,EAAE;gBAC3B,OAAO,IAAI2F,GAAGxV,QAAQ0V,MAAMnH;YAC9B;YACA/D,OAAOsE,iBAAiBtE;YACxB2B,aACE+C,SACA;gBACE1E,MAAMA;gBACNsD,cAAc2H;gBACdvH,gBAAgB;gBAChBC,sBAAsBsH;YACxB,GACA;gBAAEpG,8BAA8B;YAAK;QAEzC;QACA,SAASsG,6BAA6BzG,OAAO,EAAE1E,IAAI;YACjDA,OAAOsE,iBAAiBtE;YACxB,IAAIoL,kBAAkBpL,SAAS;YAC/B2B,aAAa+C,SAAS;gBACpB1E,MAAMA;gBACNsD,cAAc,SAAU/M,KAAK;oBAC3B,IAAIX,SAAS2E,OAAO,CAAChE,SAAS,EAAE;oBAChC,IAAI0B;oBACJ,IAAImT,iBAAiB;wBACnB,IAAIC,iBAAiB9U,QAAQ;wBAC7B,IAAK,IAAI8B,IAAI,GAAGA,KAAKzC,QAAQ,EAAEyC,EAAG;4BAChC,IAAIiT,iBAAiB/U,QAAQ,IAAI8B;4BACjC,IAAIA,KAAKzC,UAAUmC,MAAM,CAACuT,eAAe,IAAI,GAAG;gCAC9C,IAAIC,UAAUD,iBAAiBD;gCAC/B,IAAIG,gBAAgB7T,aAAa0T,gBAAgBE;gCACjD,IAAItT,QAAQqB,WAAW;oCACrBrB,MAAMuT;gCACR,OAAO;oCACLvT,OAAOkB,OAAOC,YAAY,CAAC;oCAC3BnB,OAAOuT;gCACT;gCACAH,iBAAiBC,iBAAiB;4BACpC;wBACF;oBACF,OAAO;wBACL,IAAI3N,IAAI,IAAIiE,MAAMhM;wBAClB,IAAK,IAAIyC,IAAI,GAAGA,IAAIzC,QAAQ,EAAEyC,EAAG;4BAC/BsF,CAAC,CAACtF,EAAE,GAAGc,OAAOC,YAAY,CAACrB,MAAM,CAACxB,QAAQ,IAAI8B,EAAE;wBAClD;wBACAJ,MAAM0F,EAAEiM,IAAI,CAAC;oBACf;oBACAN,MAAM/S;oBACN,OAAO0B;gBACT;gBACAuL,YAAY,SAAUnE,WAAW,EAAE9I,KAAK;oBACtC,IAAIA,iBAAiBkV,aAAa;wBAChClV,QAAQ,IAAId,WAAWc;oBACzB;oBACA,IAAImV;oBACJ,IAAIC,sBAAsB,OAAOpV,UAAU;oBAC3C,IACE,CACEoV,CAAAA,uBACApV,iBAAiBd,cACjBc,iBAAiBqV,qBACjBrV,iBAAiBqE,SAAQ,GAE3B;wBACA6J,kBAAkB;oBACpB;oBACA,IAAI2G,mBAAmBO,qBAAqB;wBAC1CD,YAAY;4BACV,OAAO/S,gBAAgBpC;wBACzB;oBACF,OAAO;wBACLmV,YAAY;4BACV,OAAOnV,MAAMX,MAAM;wBACrB;oBACF;oBACA,IAAIA,SAAS8V;oBACb,IAAI9T,MAAMiU,QAAQ,IAAIjW,SAAS;oBAC/B2E,OAAO,CAAC3C,OAAO,EAAE,GAAGhC;oBACpB,IAAIwV,mBAAmBO,qBAAqB;wBAC1ClT,aAAalC,OAAOqB,MAAM,GAAGhC,SAAS;oBACxC,OAAO;wBACL,IAAI+V,qBAAqB;4BACvB,IAAK,IAAItT,IAAI,GAAGA,IAAIzC,QAAQ,EAAEyC,EAAG;gCAC/B,IAAIyT,WAAWvV,MAAMgC,UAAU,CAACF;gCAChC,IAAIyT,WAAW,KAAK;oCAClBxC,MAAM1R;oCACN6M,kBACE;gCAEJ;gCACA1M,MAAM,CAACH,MAAM,IAAIS,EAAE,GAAGyT;4BACxB;wBACF,OAAO;4BACL,IAAK,IAAIzT,IAAI,GAAGA,IAAIzC,QAAQ,EAAEyC,EAAG;gCAC/BN,MAAM,CAACH,MAAM,IAAIS,EAAE,GAAG9B,KAAK,CAAC8B,EAAE;4BAChC;wBACF;oBACF;oBACA,IAAIgH,gBAAgB,MAAM;wBACxBA,YAAY2C,IAAI,CAACsH,OAAO1R;oBAC1B;oBACA,OAAOA;gBACT;gBACA8L,gBAAgB;gBAChBC,sBAAsBnE;gBACtBoE,oBAAoB,SAAUhM,GAAG;oBAC/B0R,MAAM1R;gBACR;YACF;QACF;QACA,SAASmU,8BAA8BrH,OAAO,EAAEsH,QAAQ,EAAEhM,IAAI;YAC5DA,OAAOsE,iBAAiBtE;YACxB,IAAIiM,cAAcC,cAAcC,SAASC,gBAAgBxQ;YACzD,IAAIoQ,aAAa,GAAG;gBAClBC,eAAenT;gBACfoT,eAAe7S;gBACf+S,iBAAiB3S;gBACjB0S,UAAU;oBACR,OAAOnT;gBACT;gBACA4C,QAAQ;YACV,OAAO,IAAIoQ,aAAa,GAAG;gBACzBC,eAAevS;gBACfwS,eAAepS;gBACfsS,iBAAiBpS;gBACjBmS,UAAU;oBACR,OAAO5R;gBACT;gBACAqB,QAAQ;YACV;YACA+F,aAAa+C,SAAS;gBACpB1E,MAAMA;gBACNsD,cAAc,SAAU/M,KAAK;oBAC3B,IAAIX,SAAS2E,OAAO,CAAChE,SAAS,EAAE;oBAChC,IAAI8V,OAAOF;oBACX,IAAIlU;oBACJ,IAAIoT,iBAAiB9U,QAAQ;oBAC7B,IAAK,IAAI8B,IAAI,GAAGA,KAAKzC,QAAQ,EAAEyC,EAAG;wBAChC,IAAIiT,iBAAiB/U,QAAQ,IAAI8B,IAAI2T;wBACrC,IAAI3T,KAAKzC,UAAUyW,IAAI,CAACf,kBAAkB1P,MAAM,IAAI,GAAG;4BACrD,IAAI0Q,eAAehB,iBAAiBD;4BACpC,IAAIG,gBAAgBS,aAAaZ,gBAAgBiB;4BACjD,IAAIrU,QAAQqB,WAAW;gCACrBrB,MAAMuT;4BACR,OAAO;gCACLvT,OAAOkB,OAAOC,YAAY,CAAC;gCAC3BnB,OAAOuT;4BACT;4BACAH,iBAAiBC,iBAAiBU;wBACpC;oBACF;oBACA1C,MAAM/S;oBACN,OAAO0B;gBACT;gBACAuL,YAAY,SAAUnE,WAAW,EAAE9I,KAAK;oBACtC,IAAI,CAAE,CAAA,OAAOA,UAAU,QAAO,GAAI;wBAChCkO,kBACE,+CAA+CzE;oBAEnD;oBACA,IAAIpK,SAASwW,eAAe7V;oBAC5B,IAAIqB,MAAMiU,QAAQ,IAAIjW,SAASoW;oBAC/BzR,OAAO,CAAC3C,OAAO,EAAE,GAAGhC,UAAUgG;oBAC9BsQ,aAAa3V,OAAOqB,MAAM,GAAGhC,SAASoW;oBACtC,IAAI3M,gBAAgB,MAAM;wBACxBA,YAAY2C,IAAI,CAACsH,OAAO1R;oBAC1B;oBACA,OAAOA;gBACT;gBACA8L,gBAAgB;gBAChBC,sBAAsBnE;gBACtBoE,oBAAoB,SAAUhM,GAAG;oBAC/B0R,MAAM1R;gBACR;YACF;QACF;QACA,SAAS2U,+BACP7H,OAAO,EACP1E,IAAI,EACJwM,oBAAoB,EACpBpK,cAAc,EACdqK,mBAAmB,EACnBpK,aAAa;YAEblD,mBAAmB,CAACuF,QAAQ,GAAG;gBAC7B1E,MAAMsE,iBAAiBtE;gBACvBoC,gBAAgB0G,wBACd0D,sBACApK;gBAEFC,eAAeyG,wBACb2D,qBACApK;gBAEFE,QAAQ,EAAE;YACZ;QACF;QACA,SAASmK,qCACPxK,UAAU,EACVY,SAAS,EACTH,gBAAgB,EAChBgK,eAAe,EACf5J,MAAM,EACNC,aAAa,EACbH,kBAAkB,EAClB+J,eAAe,EACf3J,MAAM,EACNC,aAAa;YAEb/D,mBAAmB,CAAC+C,WAAW,CAACK,MAAM,CAACP,IAAI,CAAC;gBAC1Cc,WAAWwB,iBAAiBxB;gBAC5BH,kBAAkBA;gBAClBI,QAAQ+F,wBAAwB6D,iBAAiB5J;gBACjDC,eAAeA;gBACfH,oBAAoBA;gBACpBI,QAAQ6F,wBAAwB8D,iBAAiB3J;gBACjDC,eAAeA;YACjB;QACF;QACA,SAAS2J,uBAAuBnI,OAAO,EAAE1E,IAAI;YAC3CA,OAAOsE,iBAAiBtE;YACxB2B,aAAa+C,SAAS;gBACpBoI,QAAQ;gBACR9M,MAAMA;gBACN0D,gBAAgB;gBAChBJ,cAAc;oBACZ,OAAOhK;gBACT;gBACAkK,YAAY,SAAUnE,WAAW,EAAEgE,CAAC;oBAClC,OAAO/J;gBACT;YACF;QACF;QACA,IAAIyT,gBAAgB,CAAC;QACrB,SAASC,kBAAkBC,OAAO;YAChC,IAAIC,SAASH,aAAa,CAACE,QAAQ;YACnC,IAAIC,WAAW5T,WAAW;gBACxB,OAAOgL,iBAAiB2I;YAC1B,OAAO;gBACL,OAAOC;YACT;QACF;QACA,SAASC;YACP,IAAI,OAAOC,eAAe,UAAU;gBAClC,OAAOA;YACT;YACA,OAAO,AAAC,CAAA;gBACN,OAAOhN;YACT,CAAA,IAAK;QACP;QACA,SAASiN,mBAAmBrN,IAAI;YAC9B,IAAIA,SAAS,GAAG;gBACd,OAAO2F,iBAAiBwH;YAC1B,OAAO;gBACLnN,OAAOgN,kBAAkBhN;gBACzB,OAAO2F,iBAAiBwH,kBAAkB,CAACnN,KAAK;YAClD;QACF;QACA,SAASsN,eAAejI,MAAM;YAC5B,IAAIA,SAAS,GAAG;gBACdF,kBAAkB,CAACE,OAAO,CAACC,QAAQ,IAAI;YACzC;QACF;QACA,SAASiI,sBAAsB7I,OAAO,EAAE+B,SAAS;YAC/C,IAAI+G,OAAO7N,eAAe,CAAC+E,QAAQ;YACnC,IAAIpL,cAAckU,MAAM;gBACtB/I,kBACEgC,YAAY,uBAAuB2C,YAAY1E;YAEnD;YACA,OAAO8I;QACT;QACA,SAASC,oBAAoB3G,QAAQ;YACnC,IAAII,WAAW;YACf,IAAK,IAAI7O,IAAI,GAAGA,IAAIyO,UAAU,EAAEzO,EAAG;gBACjC6O,YAAY,AAAC7O,CAAAA,MAAM,IAAI,OAAO,EAAC,IAAK,QAAQA;YAC9C;YACA,IAAIqV,eACF,qCACA5G,WACA;YACF,IAAK,IAAIzO,IAAI,GAAGA,IAAIyO,UAAU,EAAEzO,EAAG;gBACjCqV,gBACE,gBACArV,IACA,kEACAA,IACA,mBACAA,IACA,UACA,YACAA,IACA,eACAA,IACA,mCACA,oBACAA,IACA;YACJ;YACAqV,gBACE,+BACAxG,WACA,SACA,oCACA;YACF,OAAO,IAAI9G,SACT,yBACA,UACA,oBACAsN,cACAH,uBAAuB9Z,QAAQkS;QACnC;QACA,IAAIgI,eAAe,CAAC;QACpB,SAASC,cAAcvI,MAAM;YAC3B,IAAI,CAACA,QAAQ;gBACXZ,kBAAkB,sCAAsCY;YAC1D;YACA,OAAOF,kBAAkB,CAACE,OAAO,CAAC9O,KAAK;QACzC;QACA,SAASsX,YAAYxI,MAAM,EAAEyB,QAAQ,EAAEJ,QAAQ,EAAE8B,IAAI;YACnDnD,SAASuI,cAAcvI;YACvB,IAAIyI,QAAQH,YAAY,CAAC7G,SAAS;YAClC,IAAI,CAACgH,OAAO;gBACVA,QAAQL,oBAAoB3G;gBAC5B6G,YAAY,CAAC7G,SAAS,GAAGgH;YAC3B;YACA,OAAOA,MAAMzI,QAAQqB,UAAU8B;QACjC;QACA,SAASuF;YACPpX;QACF;QACA,SAASqX,uBAAuBC,IAAI,EAAEC,GAAG,EAAEC,GAAG;YAC5CpW,OAAOqW,UAAU,CAACH,MAAMC,KAAKA,MAAMC;QACrC;QACA,SAASE,0BAA0BtK,IAAI;YACrC,IAAI;gBACFnN,WAAW0X,IAAI,CAAC,AAACvK,OAAOvO,OAAO+Y,UAAU,GAAG,UAAW;gBACvD7T,2BAA2B9D,WAAWpB,MAAM;gBAC5C,OAAO;YACT,EAAE,OAAOsH,GAAG,CAAC;QACf;QACA,SAAS0R,wBAAwBC,aAAa;YAC5C,IAAIC,UAAU3W,OAAOnC,MAAM;YAC3B6Y,gBAAgBA,kBAAkB;YAClC,IAAIE,cAAc;YAClB,IAAIF,gBAAgBE,aAAa;gBAC/B,OAAO;YACT;YACA,IAAK,IAAIC,UAAU,GAAGA,WAAW,GAAGA,WAAW,EAAG;gBAChD,IAAIC,oBAAoBH,UAAW,CAAA,IAAI,MAAME,OAAM;gBACnDC,oBAAoBC,KAAKC,GAAG,CAC1BF,mBACAJ,gBAAgB;gBAElB,IAAIO,UAAUF,KAAKC,GAAG,CACpBJ,aACAvU,QAAQ0U,KAAKG,GAAG,CAACR,eAAeI,oBAAoB;gBAEtD,IAAIK,cAAcb,0BAA0BW;gBAC5C,IAAIE,aAAa;oBACf,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,IAAIC,MAAM,CAAC;QACX,SAASC;YACP,OAAOjb,eAAe;QACxB;QACA,SAASkb;YACP,IAAI,CAACA,cAAcC,OAAO,EAAE;gBAC1B,IAAIC,OACF,AACE,CAAA,AAAC,OAAOC,cAAc,YACpBA,UAAUC,SAAS,IACnBD,UAAUC,SAAS,CAAC,EAAE,IACxB,GAAE,EACF5Z,OAAO,CAAC,KAAK,OAAO;gBACxB,IAAI6Z,MAAM;oBACRC,MAAM;oBACNC,SAAS;oBACTC,MAAM;oBACNC,KAAK;oBACLC,MAAM;oBACNC,MAAMT;oBACNU,GAAGb;gBACL;gBACA,IAAK,IAAI/U,KAAK8U,IAAK;oBACjBO,GAAG,CAACrV,EAAE,GAAG8U,GAAG,CAAC9U,EAAE;gBACjB;gBACA,IAAIiV,UAAU,EAAE;gBAChB,IAAK,IAAIjV,KAAKqV,IAAK;oBACjBJ,QAAQtN,IAAI,CAAC3H,IAAI,MAAMqV,GAAG,CAACrV,EAAE;gBAC/B;gBACAgV,cAAcC,OAAO,GAAGA;YAC1B;YACA,OAAOD,cAAcC,OAAO;QAC9B;QACA,IAAIY,WAAW;YACbC,UAAU,CAAC;YACXC,SAAS;gBAAC;gBAAM,EAAE;gBAAE,EAAE;aAAC;YACvBC,WAAW,SAAUC,MAAM,EAAEC,IAAI;gBAC/B,IAAI/a,SAAS0a,SAASE,OAAO,CAACE,OAAO;gBACrC,IAAIC,SAAS,KAAKA,SAAS,IAAI;oBAC3BD,CAAAA,WAAW,IAAIva,MAAMI,GAAE,EAAGgB,kBAAkB3B,QAAQ;oBACtDA,OAAOI,MAAM,GAAG;gBAClB,OAAO;oBACLJ,OAAOwM,IAAI,CAACuO;gBACd;YACF;YACAC,SAASlX;YACTsF,KAAK;gBACHsR,SAASM,OAAO,IAAI;gBACpB,IAAIjb,MAAMqE,MAAM,CAAC,AAACsW,SAASM,OAAO,GAAG,KAAM,EAAE;gBAC7C,OAAOjb;YACT;YACAkb,QAAQ,SAAU7Y,GAAG;gBACnB,IAAIrC,MAAMoC,aAAaC;gBACvB,OAAOrC;YACT;YACAmb,OAAO,SAAUC,GAAG,EAAEC,IAAI;gBACxB,OAAOD;YACT;QACF;QACA,SAASE,aAAaC,SAAS,EAAEC,WAAW;YAC1C,IAAIC,UAAU;YACd3B,gBAAgB/N,OAAO,CAAC,SAAU2P,MAAM,EAAE5Y,CAAC;gBACzC,IAAIT,MAAMmZ,cAAcC;gBACxBpX,MAAM,CAAC,AAACkX,YAAYzY,IAAI,KAAM,EAAE,GAAGT;gBACnCqC,mBAAmBgX,QAAQrZ;gBAC3BoZ,WAAWC,OAAOrb,MAAM,GAAG;YAC7B;YACA,OAAO;QACT;QACA,SAASsb,mBAAmBC,cAAc,EAAEC,iBAAiB;YAC3D,IAAI9B,UAAUD;YACdzV,MAAM,CAACuX,kBAAkB,EAAE,GAAG7B,QAAQ1Z,MAAM;YAC5C,IAAIob,UAAU;YACd1B,QAAQhO,OAAO,CAAC,SAAU2P,MAAM;gBAC9BD,WAAWC,OAAOrb,MAAM,GAAG;YAC7B;YACAgE,MAAM,CAACwX,qBAAqB,EAAE,GAAGJ;YACjC,OAAO;QACT;QACA,SAASK,MAAMhd,MAAM;YACnBid,KAAKjd;QACP;QACA,SAASkd,UAAUC,EAAE;YACnB,OAAO;QACT;QACA,SAASC,SAASD,EAAE,EAAEE,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,GAAG;QACnE,SAASC,UAAUN,EAAE,EAAEO,GAAG,EAAEC,MAAM,EAAEC,IAAI;YACtC,IAAI9D,MAAM;YACV,IAAK,IAAI9V,IAAI,GAAGA,IAAI2Z,QAAQ3Z,IAAK;gBAC/B,IAAIT,MAAMgC,MAAM,CAAC,AAACmY,MAAM1Z,IAAI,KAAM,EAAE;gBACpC,IAAIO,MAAMgB,MAAM,CAAC,AAACmY,MAAO1Z,CAAAA,IAAI,IAAI,CAAA,KAAO,EAAE;gBAC1C,IAAK,IAAI6Z,IAAI,GAAGA,IAAItZ,KAAKsZ,IAAK;oBAC5BhC,SAASG,SAAS,CAACmB,IAAIzZ,MAAM,CAACH,MAAMsa,EAAE;gBACxC;gBACA/D,OAAOvV;YACT;YACAgB,MAAM,CAACqY,QAAQ,EAAE,GAAG9D;YACpB,OAAO;QACT;QACA,SAASgE,aAAaC,GAAG;YACvB9b,YAAY8b;QACd;QACApR,gBAAgBvN,MAAM,CAAC,gBAAgB,GAAG4M,YACxCjD,OACA;QAEF+G;QACAK,eAAe/Q,MAAM,CAAC,eAAe,GAAG4M,YAAYjD,OAAO;QAC3DsI;QACAyD,mBAAmB1V,MAAM,CAAC,mBAAmB,GAAG4M,YAC9CjD,OACA;QAEF,IAAIQ,gBAAgB;YAClByU,GAAGrT;YACHsT,GAAGrQ;YACHsQ,GAAG1O;YACH2O,GAAG1N;YACHzK,GAAGuL;YACHvN,GAAG4N;YACHhG,GAAG4J;YACHtF,GAAGmG;YACH+H,GAAG5H;YACHqH,GAAG/G;YACHrO,GAAGiP;YACH2G,GAAGnG;YACH5O,GAAG+O;YACHiG,GAAG9F;YACH+F,GAAGxN;YACH9M,GAAG+U;YACHwF,GAAGvF;YACHwF,GAAGjF;YACHkF,GAAGhF;YACHzH,GAAG0H;YACHgF,GAAGxE;YACHyE,GAAGpC;YACH9K,GAAGmL;YACHgC,GAAG7B;YACH8B,GAAG5B;YACHlO,GAAGoO;YACH3L,GAAGgM;YACHsB,GAAGjB;QACL;QACA,IAAIkB,MAAM5V;QACV,IAAI6V,qBAAsB7f,MAAM,CAAC,qBAAqB,GAAG;YACvD,OAAO,AAAC6f,CAAAA,qBAAqB7f,MAAM,CAAC,qBAAqB,GACvDA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8S,KAAK,CAAC,MAAMwB;QACpC;QACA,IAAI8D,UAAWpY,MAAM,CAAC,UAAU,GAAG;YACjC,OAAO,AAACoY,CAAAA,UAAUpY,MAAM,CAAC,UAAU,GAAGA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8S,KAAK,CAC7D,MACAwB;QAEJ;QACA,IAAIuB,QAAS7V,MAAM,CAAC,QAAQ,GAAG;YAC7B,OAAO,AAAC6V,CAAAA,QAAQ7V,MAAM,CAAC,QAAQ,GAAGA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8S,KAAK,CACzD,MACAwB;QAEJ;QACA,IAAIsB,iBAAkB5V,MAAM,CAAC,iBAAiB,GAAG;YAC/C,OAAO,AAAC4V,CAAAA,iBAAiB5V,MAAM,CAAC,iBAAiB,GAC/CA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8S,KAAK,CAAC,MAAMwB;QACpC;QACA,IAAIwL,8CAA+C9f,MAAM,CACvD,8CACD,GAAG;YACF,OAAO,AAAC8f,CAAAA,8CAA8C9f,MAAM,CAC1D,8CACD,GACCA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8S,KAAK,CAAC,MAAMwB;QACpC;QACA,IAAIyL,eAAgB/f,MAAM,CAAC,eAAe,GAAG;YAC3C,OAAO,AAAC+f,CAAAA,eAAe/f,MAAM,CAAC,eAAe,GAAGA,MAAM,CAAC,MAAM,CAAC,IAAI,AAAD,EAAG8S,KAAK,CACvE,MACAwB;QAEJ;QACA,IAAI0L;QACJ,SAASC,WAAWrf,MAAM;YACxB,IAAI,CAAC2L,IAAI,GAAG;YACZ,IAAI,CAACS,OAAO,GAAG,kCAAkCpM,SAAS;YAC1D,IAAI,CAACA,MAAM,GAAGA;QAChB;QACAkI,wBAAwB,SAASoX;YAC/B,IAAI,CAACF,WAAWG;YAChB,IAAI,CAACH,WAAWlX,wBAAwBoX;QAC1C;QACA,SAASC,IAAIpL,IAAI;YACfA,OAAOA,QAAQtU;YACf,IAAImI,kBAAkB,GAAG;gBACvB;YACF;YACAX;YACA,IAAIW,kBAAkB,GAAG;gBACvB;YACF;YACA,SAASwX;gBACP,IAAIJ,WAAW;gBACfA,YAAY;gBACZhgB,MAAM,CAAC,YAAY,GAAG;gBACtB,IAAIoD,OAAO;gBACXiF;gBACApI,oBAAoBD;gBACpB,IAAIA,MAAM,CAAC,uBAAuB,EAAEA,MAAM,CAAC,uBAAuB;gBAClEuI;YACF;YACA,IAAIvI,MAAM,CAAC,YAAY,EAAE;gBACvBA,MAAM,CAAC,YAAY,CAAC;gBACpBqgB,WAAW;oBACTA,WAAW;wBACTrgB,MAAM,CAAC,YAAY,CAAC;oBACtB,GAAG;oBACHogB;gBACF,GAAG;YACL,OAAO;gBACLA;YACF;QACF;QACApgB,MAAM,CAAC,MAAM,GAAGmgB;QAChB,SAAStC,KAAKjd,MAAM,EAAE0f,QAAQ;YAC5Bjd,aAAazC;YACb,IAAI0f,YAAYjV,sBAAsBzK,WAAW,GAAG;gBAClD;YACF;YACA,IAAIyK,oBAAoB,CACxB,OAAO;gBACL/C;gBACA,IAAItI,MAAM,CAAC,SAAS,EAAEA,MAAM,CAAC,SAAS,CAACY;gBACvCwC,QAAQ;YACV;YACAzC,MAAMC,QAAQ,IAAIqf,WAAWrf;QAC/B;QACA,IAAIZ,MAAM,CAAC,UAAU,EAAE;YACrB,IAAI,OAAOA,MAAM,CAAC,UAAU,IAAI,YAC9BA,MAAM,CAAC,UAAU,GAAG;gBAACA,MAAM,CAAC,UAAU;aAAC;YACzC,MAAOA,MAAM,CAAC,UAAU,CAACmC,MAAM,GAAG,EAAG;gBACnCnC,MAAM,CAAC,UAAU,CAAC6L,GAAG;YACvB;QACF;QACAsU;QAEA,OAAOngB,OAAOugB,KAAK;IACrB;AACF;AACA,eAAevgB,OAAM"}