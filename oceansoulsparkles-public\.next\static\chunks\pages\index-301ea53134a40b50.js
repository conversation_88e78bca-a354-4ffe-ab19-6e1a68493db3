(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{5557:function(e,a,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return t(7630)}])},7630:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return b}});var s=t(5893),r=t(9008),n=t.n(r),i=t(1664),l=t.n(i),o=t(7294),c=t(4609),d=t.n(c),m=t(5497),p=t(645),g=t.n(p);function h(e){let{title:a,subtitle:t,backgroundImage:r,ctaText:n,ctaLink:i,secondaryCtaText:c,secondaryCtaLink:d,height:m="100vh"}=e,[p,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{h(!0)},[]);let x=Array.from({length:20},(e,a)=>(0,s.jsx)("div",{style:{position:"absolute",width:"4px",height:"4px",background:"white",borderRadius:"50%",top:"".concat(100*Math.random(),"%"),left:"".concat(100*Math.random(),"%"),animation:"sparkle ".concat(3+4*Math.random(),"s linear infinite"),animationDelay:"".concat(3*Math.random(),"s"),opacity:.7}},a));return(0,s.jsxs)("section",{style:{height:m,background:r?"linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url(".concat(r,")"):"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",backgroundSize:"cover",backgroundPosition:"center",backgroundAttachment:"fixed",display:"flex",alignItems:"center",justifyContent:"center",position:"relative",overflow:"hidden"},className:"jsx-73d744028f9dcda6",children:[x,(0,s.jsxs)("div",{style:{textAlign:"center",color:"white",maxWidth:"800px",padding:"0 20px",transform:p?"translateY(0)":"translateY(50px)",opacity:p?1:0,transition:"all 1s ease-out"},className:"jsx-73d744028f9dcda6",children:[(0,s.jsx)("h1",{style:{fontSize:"clamp(2.5rem, 5vw, 4rem)",fontWeight:"700",marginBottom:"24px",textShadow:"2px 2px 4px rgba(0, 0, 0, 0.5)",background:"linear-gradient(135deg, #ffffff, #f0f0f0)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},className:"jsx-73d744028f9dcda6",children:a}),(0,s.jsx)("p",{style:{fontSize:"clamp(1.1rem, 2.5vw, 1.5rem)",marginBottom:"40px",textShadow:"1px 1px 2px rgba(0, 0, 0, 0.5)",lineHeight:"1.6",color:"rgba(255, 255, 255, 0.95)"},className:"jsx-73d744028f9dcda6",children:t}),(0,s.jsxs)("div",{style:{display:"flex",gap:"20px",justifyContent:"center",flexWrap:"wrap"},className:"jsx-73d744028f9dcda6",children:[n&&i&&(0,s.jsxs)(l(),{href:i,style:{display:"inline-flex",alignItems:"center",gap:"8px",padding:"16px 32px",background:"linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%)",color:"white",textDecoration:"none",borderRadius:"50px",fontSize:"18px",fontWeight:"600",transition:"all 0.3s ease",boxShadow:"0 4px 20px rgba(55, 136, 216, 0.4)",border:"none",cursor:"pointer"},children:[n,(0,s.jsx)("span",{style:{fontSize:"20px"},className:"jsx-73d744028f9dcda6",children:"✨"})]}),c&&d&&(0,s.jsxs)(l(),{href:d,style:{display:"inline-flex",alignItems:"center",gap:"8px",padding:"16px 32px",background:"rgba(255, 255, 255, 0.1)",color:"white",textDecoration:"none",borderRadius:"50px",fontSize:"18px",fontWeight:"600",transition:"all 0.3s ease",border:"2px solid rgba(255, 255, 255, 0.3)",backdropFilter:"blur(10px)"},children:[c,(0,s.jsx)("span",{style:{fontSize:"16px"},className:"jsx-73d744028f9dcda6",children:"↓"})]})]})]}),(0,s.jsx)(g(),{id:"73d744028f9dcda6",children:"@-webkit-keyframes sparkle{0%,100%{opacity:0;-webkit-transform:scale(0);transform:scale(0)}50%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}@-moz-keyframes sparkle{0%,100%{opacity:0;-moz-transform:scale(0);transform:scale(0)}50%{opacity:1;-moz-transform:scale(1);transform:scale(1)}}@-o-keyframes sparkle{0%,100%{opacity:0;-o-transform:scale(0);transform:scale(0)}50%{opacity:1;-o-transform:scale(1);transform:scale(1)}}@keyframes sparkle{0%,100%{opacity:0;-webkit-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0)}50%{opacity:1;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}}@media(max-width:768px){section.jsx-73d744028f9dcda6{background-attachment:scroll!important}}"})]})}function x(e){let{children:a,animation:t="fade-in",delay:r=0,className:n="",threshold:i=.1}=e,[l,c]=(0,o.useState)(!1),[d,m]=(0,o.useState)(!1),p=(0,o.useRef)(null);return(0,o.useEffect)(()=>{let e=new IntersectionObserver(e=>{let[a]=e;a.isIntersecting&&!d&&setTimeout(()=>{c(!0),m(!0)},r)},{threshold:i}),a=p.current;return a&&e.observe(a),()=>{a&&e.unobserve(a)}},[r,i,d]),(0,s.jsx)("div",{ref:p,className:n,style:(()=>{let e={transition:"all 0.8s cubic-bezier(0.4, 0, 0.2, 1)"};switch(t){case"fade-in":return{...e,opacity:l?1:0,transform:l?"translateY(0)":"translateY(30px)"};case"slide-left":return{...e,opacity:l?1:0,transform:l?"translateX(0)":"translateX(-50px)"};case"slide-right":return{...e,opacity:l?1:0,transform:l?"translateX(0)":"translateX(50px)"};case"slide-up":return{...e,opacity:l?1:0,transform:l?"translateY(0)":"translateY(50px)"};case"slide-down":return{...e,opacity:l?1:0,transform:l?"translateY(0)":"translateY(-50px)"};case"scale-in":return{...e,opacity:l?1:0,transform:l?"scale(1)":"scale(0.8)"};case"rotate-in":return{...e,opacity:l?1:0,transform:l?"rotate(0deg) scale(1)":"rotate(-10deg) scale(0.8)"};case"bounce-in":return{...e,transition:l?"all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)":"all 0.8s cubic-bezier(0.4, 0, 0.2, 1)",opacity:l?1:0,transform:l?"scale(1)":"scale(0.3)"};default:return{...e,opacity:l?1:0}}})(),children:a})}function u(e){let{children:a,staggerDelay:t=100,baseDelay:r=0,className:n="",threshold:i=.1}=e,[l,c]=(0,o.useState)(new Set),[d,m]=(0,o.useState)(!1),p=(0,o.useRef)(null);(0,o.useEffect)(()=>{let e=new IntersectionObserver(e=>{let[s]=e;s.isIntersecting&&!d&&(m(!0),o.Children.toArray(a).forEach((e,a)=>{setTimeout(()=>{c(e=>new Set([...e,a]))},r+a*t)}))},{threshold:i}),s=p.current;return s&&e.observe(s),()=>{s&&e.unobserve(s)}},[a,t,r,i,d]);let g=e=>({opacity:l.has(e)?1:0,transform:l.has(e)?"translateY(0)":"translateY(30px)",transition:"all 0.6s cubic-bezier(0.4, 0, 0.2, 1)"});return(0,s.jsx)("div",{ref:p,className:n,children:o.Children.map(a,(e,a)=>(0,s.jsx)("div",{style:g(a),children:e},a))})}function f(e){let{title:a,subtitle:t,ctaText:r,ctaLink:n}=e,[i,c]=(0,o.useState)(0),d=[{title:"Face Painting Magic",description:"Transform into your favorite character with our professional face painting services",image:"/images/showcase/face-painting.jpg",color:"#FF6B6B"},{title:"Airbrush Artistry",description:"Stunning airbrush body art that brings your imagination to life",image:"/images/showcase/airbrush-art.jpg",color:"#4ECDC4"},{title:"Festival Braiding",description:"Colorful braids and hair art perfect for festivals and special events",image:"/images/showcase/braiding.jpg",color:"#45B7D1"},{title:"Eco-Friendly Glitter",description:"Biodegradable glitter that sparkles without harming the environment",image:"/images/showcase/eco-glitter.jpg",color:"#96CEB4"}];(0,o.useEffect)(()=>{let e=setInterval(()=>{c(e=>(e+1)%d.length)},4e3);return()=>clearInterval(e)},[d.length]);let m=e=>({position:"absolute",top:0,left:0,width:"100%",height:"100%",background:"linear-gradient(135deg, ".concat(d[e].color,"aa, ").concat(d[e].color,"dd)"),display:"flex",alignItems:"center",justifyContent:"space-between",padding:"40px",opacity:e===i?1:0,transform:e===i?"translateX(0)":"translateX(100%)",transition:"all 0.8s cubic-bezier(0.4, 0, 0.2, 1)"}),p={flex:1,paddingRight:"40px"},g={width:"300px",height:"250px",borderRadius:"16px",objectFit:"cover",boxShadow:"0 10px 30px rgba(0, 0, 0, 0.3)"},h=e=>({width:"12px",height:"12px",borderRadius:"50%",background:e===i?"white":"rgba(255, 255, 255, 0.4)",cursor:"pointer",transition:"all 0.3s ease",transform:e===i?"scale(1.2)":"scale(1)"});return(0,s.jsxs)("section",{style:{padding:"80px 0",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",position:"relative",overflow:"hidden"},children:[(0,s.jsx)("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:'url("/images/sparkle-pattern.png") repeat',opacity:.1,zIndex:1}}),(0,s.jsxs)("div",{style:{maxWidth:"1200px",margin:"0 auto",padding:"0 20px",position:"relative",zIndex:2},children:[(0,s.jsxs)("div",{style:{textAlign:"center",marginBottom:"60px"},children:[(0,s.jsx)("h2",{style:{fontSize:"clamp(2rem, 4vw, 3rem)",fontWeight:"700",marginBottom:"16px",background:"linear-gradient(135deg, #ffffff, #f0f0f0)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:a}),(0,s.jsx)("p",{style:{fontSize:"clamp(1rem, 2vw, 1.2rem)",color:"rgba(255, 255, 255, 0.9)",maxWidth:"600px",margin:"0 auto"},children:t})]}),(0,s.jsx)("div",{style:{position:"relative",height:"400px",borderRadius:"20px",overflow:"hidden",marginBottom:"40px",boxShadow:"0 20px 40px rgba(0, 0, 0, 0.3)"},children:d.map((e,a)=>(0,s.jsxs)("div",{style:m(a),children:[(0,s.jsxs)("div",{style:p,children:[(0,s.jsx)("h3",{style:{fontSize:"2rem",fontWeight:"700",marginBottom:"16px",color:"white"},children:e.title}),(0,s.jsx)("p",{style:{fontSize:"1.1rem",color:"rgba(255, 255, 255, 0.95)",lineHeight:"1.6"},children:e.description})]}),(0,s.jsx)("img",{src:e.image,alt:e.title,style:g,onError:e=>{e.target.style.display="none"}})]},a))}),(0,s.jsx)("div",{style:{display:"flex",justifyContent:"center",gap:"12px",marginBottom:"40px"},children:d.map((e,a)=>(0,s.jsx)("button",{style:h(a),onClick:()=>c(a),"aria-label":"Go to slide ".concat(a+1)},a))}),r&&n&&(0,s.jsx)("div",{style:{textAlign:"center"},children:(0,s.jsxs)(l(),{href:n,style:{display:"inline-flex",alignItems:"center",gap:"8px",padding:"16px 32px",background:"rgba(255, 255, 255, 0.2)",color:"white",textDecoration:"none",borderRadius:"50px",fontSize:"18px",fontWeight:"600",transition:"all 0.3s ease",border:"2px solid rgba(255, 255, 255, 0.3)",backdropFilter:"blur(10px)"},children:[r,(0,s.jsx)("span",{style:{fontSize:"20px"},children:"✨"})]})})]})]})}var j=t(1163);function y(e){let{title:a,description:t,keywords:r,ogImage:i,ogType:l="website",twitterCard:o="summary_large_image",canonical:c,noindex:d=!1,nofollow:m=!1,structuredData:p}=e,g=(0,j.useRouter)(),h="https://www.oceansoulsparkles.com.au",x=c||"".concat(h).concat(g.asPath),u=i||"".concat(h,"/images/og-default.jpg"),f=[];d&&f.push("noindex"),m&&f.push("nofollow");let y=f.length>0?f.join(", "):"index, follow";return(0,s.jsxs)(n(),{children:[(0,s.jsx)("title",{children:a}),(0,s.jsx)("meta",{name:"description",content:t}),r&&(0,s.jsx)("meta",{name:"keywords",content:r}),(0,s.jsx)("meta",{name:"robots",content:y}),(0,s.jsx)("link",{rel:"canonical",href:x}),(0,s.jsx)("meta",{property:"og:title",content:a}),(0,s.jsx)("meta",{property:"og:description",content:t}),(0,s.jsx)("meta",{property:"og:type",content:l}),(0,s.jsx)("meta",{property:"og:url",content:x}),(0,s.jsx)("meta",{property:"og:image",content:u}),(0,s.jsx)("meta",{property:"og:image:width",content:"1200"}),(0,s.jsx)("meta",{property:"og:image:height",content:"630"}),(0,s.jsx)("meta",{property:"og:site_name",content:"Ocean Soul Sparkles"}),(0,s.jsx)("meta",{property:"og:locale",content:"en_AU"}),(0,s.jsx)("meta",{name:"twitter:card",content:o}),(0,s.jsx)("meta",{name:"twitter:title",content:a}),(0,s.jsx)("meta",{name:"twitter:description",content:t}),(0,s.jsx)("meta",{name:"twitter:image",content:u}),(0,s.jsx)("meta",{name:"twitter:site",content:"@oceansoulsparkles"}),(0,s.jsx)("meta",{name:"twitter:creator",content:"@oceansoulsparkles"}),(0,s.jsx)("meta",{name:"author",content:"Ocean Soul Sparkles"}),(0,s.jsx)("meta",{name:"copyright",content:"Ocean Soul Sparkles"}),(0,s.jsx)("meta",{name:"language",content:"English"}),(0,s.jsx)("meta",{name:"revisit-after",content:"7 days"}),(0,s.jsx)("meta",{name:"distribution",content:"web"}),(0,s.jsx)("meta",{name:"rating",content:"general"}),(0,s.jsx)("meta",{name:"geo.region",content:"AU-VIC"}),(0,s.jsx)("meta",{name:"geo.placename",content:"Melbourne"}),(0,s.jsx)("meta",{name:"geo.position",content:"-37.8136;144.9631"}),(0,s.jsx)("meta",{name:"ICBM",content:"-37.8136, 144.9631"}),(0,s.jsx)("meta",{name:"business:contact_data:street_address",content:"Melbourne"}),(0,s.jsx)("meta",{name:"business:contact_data:locality",content:"Melbourne"}),(0,s.jsx)("meta",{name:"business:contact_data:region",content:"Victoria"}),(0,s.jsx)("meta",{name:"business:contact_data:postal_code",content:"3000"}),(0,s.jsx)("meta",{name:"business:contact_data:country_name",content:"Australia"}),(0,s.jsx)("meta",{name:"business:contact_data:email",content:"<EMAIL>"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0"}),(0,s.jsx)("meta",{name:"format-detection",content:"telephone=yes"}),(0,s.jsx)("meta",{name:"format-detection",content:"address=yes"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Ocean Soul Sparkles"}),(0,s.jsx)("meta",{name:"msapplication-TileColor",content:"#3788d8"}),(0,s.jsx)("meta",{name:"msapplication-config",content:"/browserconfig.xml"}),(0,s.jsx)("meta",{name:"theme-color",content:"#3788d8"}),(0,s.jsx)("meta",{name:"msapplication-navbutton-color",content:"#3788d8"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"#3788d8"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://ndlgbcsbidyhxbpqzgqp.supabase.co"}),(0,s.jsx)("link",{rel:"dns-prefetch",href:"https://js.squareup.com"}),(0,s.jsx)("link",{rel:"dns-prefetch",href:"https://cdn.onesignal.com"}),p&&(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(p)}}),(0,s.jsx)("link",{rel:"alternate",hrefLang:"en-au",href:x}),(0,s.jsx)("link",{rel:"alternate",hrefLang:"en",href:x}),(0,s.jsx)("link",{rel:"alternate",hrefLang:"x-default",href:x})]})}function b(){let e=(0,o.useRef)(null);return(0,s.jsxs)(m.Z,{children:[(0,s.jsxs)(n(),{children:[(0,s.jsx)("title",{children:"Ocean Soul Sparkles - Face Painting & Body Art Services"}),(0,s.jsx)("meta",{name:"description",content:"Professional face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne. Eco-friendly and sustainable glitter options available."}),(0,s.jsx)("meta",{name:"keywords",content:"face painting, body art, airbrush, braiding, melbourne, events, festivals, eco-friendly glitter"}),(0,s.jsx)("meta",{name:"google-site-verification",content:"HtjqFmAXzFBvlS4IEWJe8iyN_UK4dROGIIt_oY-i1Ag"})]}),(0,s.jsx)(y,{title:"OceanSoulSparkles | Melbourne Facepaint & Entertainment",description:"OceanSoulSparkles offers face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne. Eco-friendly and sustainable glitter options available.",ogImage:"https://www.oceansoulsparkles.com.au/images/gallery/gallery-1.jpg"}),(0,s.jsxs)("main",{className:d().main,children:[(0,s.jsx)(h,{title:"A world of sparkling self expression awaits you.",subtitle:"We exist to help you unleash your creativity and SHINE. Let's create something wild & magical together.",backgroundImage:"/UV-Generic-Psychadelia.jpg",ctaText:"Sparkle Up",ctaLink:"/book-online",secondaryCtaText:"Our Services",secondaryCtaLink:"#services",height:"100vh"}),(0,s.jsx)(f,{title:"Dive Into a World of Magical Artistry",subtitle:"Express yourself with vibrant colors, dazzling sparkles, and creative designs that capture your unique spirit",ctaText:"Let's Create Magic",ctaLink:"/book-online"}),(0,s.jsxs)("section",{id:"services",ref:e,className:d().services,children:[(0,s.jsxs)(x,{animation:"fade-in",children:[(0,s.jsx)("h2",{className:d().sectionTitle,children:"Our Services"}),(0,s.jsx)("p",{className:d().sectionDescription,children:"We offer a range of services to suit all needs - hit the link to see more!"})]}),(0,s.jsxs)(u,{className:d().serviceGrid,staggerDelay:150,children:[(0,s.jsxs)("div",{className:d().serviceCard,children:[(0,s.jsx)("div",{className:d().serviceImage,children:(0,s.jsx)("img",{src:"/Airbrush Face Body Painting.png",alt:"Airbrush Face & Body Painting",className:d().serviceImg})}),(0,s.jsx)("h3",{children:"Airbrush Face & Body Painting"}),(0,s.jsx)("p",{children:"Add flair and colour to any event with our airbrush face and body painting. From intricate designs to bold statements, we tailor our artistry to suit your theme or outfit. Perfect for festivals, birthdays, and corporate gatherings."}),(0,s.jsx)(l(),{href:"/services",className:d().serviceLink,children:"Learn More"})]}),(0,s.jsxs)("div",{className:d().serviceCard,children:[(0,s.jsx)("div",{className:d().serviceImage,children:(0,s.jsx)("img",{src:"/images/services/festival-braids.jpg",alt:"Braiding",className:d().serviceImg})}),(0,s.jsx)("h3",{children:"Braiding"}),(0,s.jsx)("p",{children:"Transform your look with our vibrant braiding services, including festival-ready styles and coloured extensions to match your vibe. We offer single bookings, braid parties, and festival services, starting from $60."}),(0,s.jsx)(l(),{href:"/services",className:d().serviceLink,children:"Learn More"})]}),(0,s.jsxs)("div",{className:d().serviceCard,children:[(0,s.jsx)("div",{className:d().serviceImage,children:(0,s.jsx)("img",{src:"/Airbrush Temporary Tattoos.png",alt:"Airbrush Temporary Tattoos",className:d().serviceImg})}),(0,s.jsx)("h3",{children:"Airbrush Temporary Tattoos"}),(0,s.jsx)("p",{children:"Explore our stunning airbrush temporary tattoos that last for days! Perfect for events, parties, or just trying out a new look without the commitment. Our designs range from delicate and intricate to bold and eye-catching."}),(0,s.jsx)(l(),{href:"/services",className:d().serviceLink,children:"Learn More"})]})]}),(0,s.jsx)(x,{animation:"fade-in",delay:300,children:(0,s.jsx)("div",{className:d().servicesLink,children:(0,s.jsx)(l(),{href:"/services",className:"button",children:"Explore All Services"})})})]}),(0,s.jsxs)("section",{className:d().ecoFriendly,children:[(0,s.jsxs)(x,{animation:"slide-right",className:d().ecoContent,children:[(0,s.jsx)("h2",{className:d().ecoTitle,children:"Shine Sustainably With Eco-Friendly Glitter"}),(0,s.jsx)("p",{className:d().ecoDescription,children:"We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do, and that's why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!"}),(0,s.jsx)("p",{className:d().ecoDescription,children:"Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water, ensuring it leaves no harmful trace. We're proud to be as friendly to the planet as we are to our customers, and to provide a way for both you and the earth to shine!"}),(0,s.jsx)(l(),{href:"/shop",className:"button button--secondary mt-4",children:"Shop Eco Glitter"})]}),(0,s.jsx)(x,{animation:"slide-left",className:d().ecoImage,children:(0,s.jsx)("img",{src:"/images/products/biodegradable-glitter.jpg",alt:"Eco-Friendly Biodegradable Glitter",className:d().ecoImg})})]}),(0,s.jsxs)("section",{className:d().gallery,children:[(0,s.jsxs)(x,{animation:"fade-in",children:[(0,s.jsx)("h2",{className:d().sectionTitle,children:"Our Gallery"}),(0,s.jsx)("p",{className:d().sectionDescription,children:"Take a peek at some of our magical creations"})]}),(0,s.jsxs)(u,{className:d().galleryGrid,staggerDelay:100,baseDelay:200,children:[(0,s.jsxs)("div",{className:d().galleryItem,children:[(0,s.jsx)("img",{src:"/images/gallery/gallery-1.jpg",alt:"Ocean Soul Sparkles Gallery",className:d().galleryImg}),(0,s.jsx)("div",{className:d().galleryOverlay,children:(0,s.jsx)("span",{children:"Face Painting"})})]}),(0,s.jsxs)("div",{className:d().galleryItem,children:[(0,s.jsx)("img",{src:"/images/gallery/gallery-2.jpg",alt:"Ocean Soul Sparkles Gallery",className:d().galleryImg}),(0,s.jsx)("div",{className:d().galleryOverlay,children:(0,s.jsx)("span",{children:"Festival Makeup"})})]}),(0,s.jsxs)("div",{className:d().galleryItem,children:[(0,s.jsx)("img",{src:"/images/gallery/gallery-3.jpg",alt:"Ocean Soul Sparkles Gallery",className:d().galleryImg}),(0,s.jsx)("div",{className:d().galleryOverlay,children:(0,s.jsx)("span",{children:"Airbrush Art"})})]}),(0,s.jsxs)("div",{className:d().galleryItem,children:[(0,s.jsx)("img",{src:"/images/gallery/gallery-4.jpg",alt:"Ocean Soul Sparkles Gallery",className:d().galleryImg}),(0,s.jsx)("div",{className:d().galleryOverlay,children:(0,s.jsx)("span",{children:"Kids Designs"})})]}),(0,s.jsxs)("div",{className:d().galleryItem,children:[(0,s.jsx)("img",{src:"/images/gallery/gallery-5.jpg",alt:"Ocean Soul Sparkles Gallery",className:d().galleryImg}),(0,s.jsx)("div",{className:d().galleryOverlay,children:(0,s.jsx)("span",{children:"Braiding"})})]}),(0,s.jsxs)("div",{className:d().galleryItem,children:[(0,s.jsx)("img",{src:"/images/gallery/gallery-6.jpg",alt:"Ocean Soul Sparkles Gallery",className:d().galleryImg}),(0,s.jsx)("div",{className:d().galleryOverlay,children:(0,s.jsx)("span",{children:"Glitter Art"})})]}),(0,s.jsxs)("div",{className:d().galleryItem,children:[(0,s.jsx)("img",{src:"/images/gallery/gallery-7.jpg",alt:"Ocean Soul Sparkles Gallery",className:d().galleryImg}),(0,s.jsx)("div",{className:d().galleryOverlay,children:(0,s.jsx)("span",{children:"Special Events"})})]}),(0,s.jsxs)("div",{className:d().galleryItem,children:[(0,s.jsx)("img",{src:"/images/products/biodegradable-glitter.jpg",alt:"Ocean Soul Sparkles Gallery",className:d().galleryImg}),(0,s.jsx)("div",{className:d().galleryOverlay,children:(0,s.jsx)("span",{children:"Eco Products"})})]})]}),(0,s.jsx)(x,{animation:"fade-in",delay:500,children:(0,s.jsxs)("div",{className:d().galleryLink,children:[(0,s.jsx)(l(),{href:"/gallery",className:"button",children:"View Full Gallery"}),(0,s.jsx)(l(),{href:"/book-online",className:"button button--outline ml-4",children:"Book Your Experience"})]})})]}),(0,s.jsxs)("section",{className:d().contact,id:"contact",children:[(0,s.jsxs)(x,{animation:"fade-in",children:[(0,s.jsx)("h2",{className:d().sectionTitle,children:"Get In Touch"}),(0,s.jsx)("p",{className:d().sectionDescription,children:"Have questions or ready to book? Send us a message!"})]}),(0,s.jsxs)("div",{className:d().contactContainer,children:[(0,s.jsx)(x,{animation:"slide-right",className:d().contactInfo,children:(0,s.jsxs)("div",{className:d().contactCard,children:[(0,s.jsx)("h3",{children:"Contact Information"}),(0,s.jsx)("p",{children:"We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help."}),(0,s.jsxs)("div",{className:d().contactDetail,children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}),(0,s.jsx)("polyline",{points:"22,6 12,13 2,6"})]}),(0,s.jsx)("a",{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:d().contactDetail,children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),(0,s.jsx)("circle",{cx:"12",cy:"10",r:"3"})]}),(0,s.jsx)("span",{children:"Melbourne, Victoria"})]}),(0,s.jsxs)("div",{className:d().contactSocial,children:[(0,s.jsx)("a",{href:"https://www.instagram.com/oceansoulsparkles",target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)("img",{src:"/images/social/instagram-icon.png",alt:"Instagram"})}),(0,s.jsx)("a",{href:"https://www.facebook.com/OceanSoulSparkles/",target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)("img",{src:"/images/social/facebook-icon.png",alt:"Facebook"})})]})]})}),(0,s.jsx)(x,{animation:"slide-left",className:d().contactFormContainer,children:(0,s.jsxs)("form",{className:d().contactForm,children:[(0,s.jsxs)("div",{className:d().formRow,children:[(0,s.jsxs)("div",{className:d().formGroup,children:[(0,s.jsx)("label",{htmlFor:"name",children:"Name"}),(0,s.jsx)("input",{id:"name",type:"text",placeholder:"Your Name",className:d().formInput})]}),(0,s.jsxs)("div",{className:d().formGroup,children:[(0,s.jsx)("label",{htmlFor:"email",children:"Email"}),(0,s.jsx)("input",{id:"email",type:"email",placeholder:"Your Email",className:d().formInput})]})]}),(0,s.jsxs)("div",{className:d().formRow,children:[(0,s.jsxs)("div",{className:d().formGroup,children:[(0,s.jsx)("label",{htmlFor:"subject",children:"Subject"}),(0,s.jsx)("input",{id:"subject",type:"text",placeholder:"Subject",className:d().formInput})]}),(0,s.jsxs)("div",{className:d().formGroup,children:[(0,s.jsx)("label",{htmlFor:"phone",children:"Phone"}),(0,s.jsx)("input",{id:"phone",type:"tel",placeholder:"Your Phone",className:d().formInput})]})]}),(0,s.jsxs)("div",{className:d().formGroup,children:[(0,s.jsx)("label",{htmlFor:"message",children:"Message"}),(0,s.jsx)("textarea",{id:"message",placeholder:"Your Message",className:d().formTextarea})]}),(0,s.jsx)("button",{type:"submit",className:"button",children:"Send Message"})]})})]})]})]})]})}},4609:function(e){e.exports={main:"Home_main__VkIEL",services:"Home_services___t0jh",sectionTitle:"Home_sectionTitle__DKb2S",sectionDescription:"Home_sectionDescription__ISMyc",serviceGrid:"Home_serviceGrid__3z7nQ",serviceCard:"Home_serviceCard__DTeBb",serviceImage:"Home_serviceImage__nHeIF",serviceImg:"Home_serviceImg__HXRnZ",serviceLink:"Home_serviceLink__Mssch",servicesLink:"Home_servicesLink__oRWQZ",ecoFriendly:"Home_ecoFriendly__9VEiC",ecoContent:"Home_ecoContent__gEhaa",ecoTitle:"Home_ecoTitle__H1f_L",ecoDescription:"Home_ecoDescription__mlNyD",ecoImage:"Home_ecoImage__U09qR",ecoImg:"Home_ecoImg__lbnsU",gallery:"Home_gallery___pQTy",galleryGrid:"Home_galleryGrid__3QXtF",galleryItem:"Home_galleryItem__yfD8U",galleryImg:"Home_galleryImg__IjV3A",galleryOverlay:"Home_galleryOverlay__EBerz",galleryLink:"Home_galleryLink__9_Yvt",contact:"Home_contact__Z5CCv",contactContainer:"Home_contactContainer__jFhre",contactInfo:"Home_contactInfo__4uK4g",contactCard:"Home_contactCard__LMdtN",contactDetail:"Home_contactDetail__vXizZ",contactSocial:"Home_contactSocial__0osDO",contactFormContainer:"Home_contactFormContainer__Gr_qd",contactForm:"Home_contactForm__MHiMg",formRow:"Home_formRow__R1zVZ",formGroup:"Home_formGroup__KFkHd",formInput:"Home_formInput__wWVuf",formTextarea:"Home_formTextarea__VRzQn"}}},function(e){e.O(0,[409,497,888,774,179],function(){return e(e.s=5557)}),_N_E=e.O()}]);