{"version": 3, "sources": ["../../../src/lib/typescript/runTypeCheck.ts"], "names": ["runTypeCheck", "ts", "baseDir", "distDir", "tsConfigPath", "cacheDir", "isAppDirEnabled", "effectiveConfiguration", "getTypeScriptConfiguration", "fileNames", "length", "hasWarnings", "inputFilesCount", "totalFilesCount", "incremental", "requiredConfig", "getRequiredConfiguration", "options", "declarationMap", "emitDeclarationOnly", "noEmit", "program", "composite", "warn", "createIncrementalProgram", "rootNames", "tsBuildInfoFile", "path", "join", "createProgram", "result", "emit", "regexIgnoredFile", "allDiagnostics", "getPreEmitDiagnostics", "concat", "diagnostics", "filter", "d", "file", "test", "fileName", "firstError", "find", "category", "DiagnosticCategory", "Error", "Boolean", "process", "env", "__NEXT_TEST_MODE", "allErrors", "map", "getFormattedDiagnostic", "console", "error", "Promise", "resolve", "setTimeout", "CompileError", "warnings", "Warning", "getSourceFiles"], "mappings": ";;;;+BAmBsBA;;;eAAAA;;;6DAnBL;qCAIV;4CACoC;4CACF;8BAEZ;qBACR;;;;;;AAUd,eAAeA,aACpBC,EAA+B,EAC/BC,OAAe,EACfC,OAAe,EACfC,YAAoB,EACpBC,QAAiB,EACjBC,eAAyB;IAEzB,MAAMC,yBAAyB,MAAMC,IAAAA,sDAA0B,EAC7DP,IACAG;IAGF,IAAIG,uBAAuBE,SAAS,CAACC,MAAM,GAAG,GAAG;QAC/C,OAAO;YACLC,aAAa;YACbC,iBAAiB;YACjBC,iBAAiB;YACjBC,aAAa;QACf;IACF;IACA,MAAMC,iBAAiBC,IAAAA,oDAAwB,EAACf;IAEhD,MAAMgB,UAAU;QACd,GAAGF,cAAc;QACjB,GAAGR,uBAAuBU,OAAO;QACjCC,gBAAgB;QAChBC,qBAAqB;QACrBC,QAAQ;IACV;IAEA,IAAIC;IAGJ,IAAIP,cAAc;IAClB,IAAI,AAACG,CAAAA,QAAQH,WAAW,IAAIG,QAAQK,SAAS,AAAD,KAAMjB,UAAU;QAC1D,IAAIY,QAAQK,SAAS,EAAE;YACrBC,IAAAA,SAAI,EACF;QAEJ;QACAT,cAAc;QACdO,UAAUpB,GAAGuB,wBAAwB,CAAC;YACpCC,WAAWlB,uBAAuBE,SAAS;YAC3CQ,SAAS;gBACP,GAAGA,OAAO;gBACVK,WAAW;gBACXR,aAAa;gBACbY,iBAAiBC,aAAI,CAACC,IAAI,CAACvB,UAAU;YACvC;QACF;IACF,OAAO;QACLgB,UAAUpB,GAAG4B,aAAa,CAACtB,uBAAuBE,SAAS,EAAEQ;IAC/D;IAEA,MAAMa,SAAST,QAAQU,IAAI;IAE3B,qBAAqB;IACrB,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;IACzB,EAAE;IACF,WAAW;IACX,qBAAqB;IACrB,mBAAmB;IACnB,oBAAoB;IACpB,EAAE;IACF,MAAMC,mBACJ;IACF,MAAMC,iBAAiBhC,GACpBiC,qBAAqB,CAACb,SACtBc,MAAM,CAACL,OAAOM,WAAW,EACzBC,MAAM,CAAC,CAACC,IAAM,CAAEA,CAAAA,EAAEC,IAAI,IAAIP,iBAAiBQ,IAAI,CAACF,EAAEC,IAAI,CAACE,QAAQ,CAAA;IAElE,MAAMC,aACJT,eAAeU,IAAI,CACjB,CAACL,IAAMA,EAAEM,QAAQ,KAAKC,uCAAkB,CAACC,KAAK,IAAIC,QAAQT,EAAEC,IAAI,MAC7DN,eAAeU,IAAI,CAAC,CAACL,IAAMA,EAAEM,QAAQ,KAAKC,uCAAkB,CAACC,KAAK;IAEzE,0EAA0E;IAC1E,IAAIE,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIR,YAAY;YACd,MAAMS,YAAYlB,eACfI,MAAM,CAAC,CAACC,IAAMA,EAAEM,QAAQ,KAAKC,uCAAkB,CAACC,KAAK,EACrDM,GAAG,CACF,CAACd,IACC,iBACAe,IAAAA,2CAAsB,EAACpD,IAAIC,SAASC,SAASmC,GAAGhC;YAGtDgD,QAAQC,KAAK,CACX,kCACEJ,UAAUvB,IAAI,CAAC,UACf;YAGJ,kDAAkD;YAClD,MAAM,IAAI4B,QAAQ,CAACC,UAAYC,WAAWD,SAAS;QACrD;IACF;IAEA,IAAIf,YAAY;QACd,MAAM,IAAIiB,0BAAY,CACpBN,IAAAA,2CAAsB,EAACpD,IAAIC,SAASC,SAASuC,YAAYpC;IAE7D;IAEA,MAAMsD,WAAW3B,eACdI,MAAM,CAAC,CAACC,IAAMA,EAAEM,QAAQ,KAAKC,uCAAkB,CAACgB,OAAO,EACvDT,GAAG,CAAC,CAACd,IACJe,IAAAA,2CAAsB,EAACpD,IAAIC,SAASC,SAASmC,GAAGhC;IAGpD,OAAO;QACLK,aAAa;QACbiD;QACAhD,iBAAiBL,uBAAuBE,SAAS,CAACC,MAAM;QACxDG,iBAAiBQ,QAAQyC,cAAc,GAAGpD,MAAM;QAChDI;IACF;AACF"}