# Ocean Soul Sparkles - Public Website

This is the **public-facing website** for Ocean Soul Sparkles, designed to serve customers only. This subdomain contains **NO ADMIN FUNCTIONALITY** and is completely separated from administrative systems for maximum security.

## 🌐 Deployment

- **Production URL**: `https://www.oceansoulsparkles.com.au`
- **Staging URL**: `https://oceansoulsparkles-public-staging.vercel.app`

## 🔒 Security Features

### Admin Access Prevention
- All admin routes (`/admin/*`) are blocked at middleware level
- Admin API endpoints (`/api/admin/*`) return 404
- Staff and artist routes are completely inaccessible
- No admin environment variables or service keys exposed

### Public-Only Features
- Customer booking system
- Public services and products display
- Contact forms and customer support
- Gallery and portfolio viewing
- Online shop for eco-friendly products
- Customer account management (customers only)

## 🏗️ Architecture

### Technology Stack
- **Framework**: Next.js 14
- **Database**: Supabase (read-only public access)
- **Authentication**: Customer accounts only (no admin/staff)
- **Payments**: Square Web Payments SDK (public transactions only)
- **Deployment**: Vercel
- **Notifications**: OneSignal (customer notifications only)

### Project Structure
```
oceansoulsparkles-public/
├── pages/
│   ├── index.js              # Homepage
│   ├── about.js              # About page
│   ├── services.js           # Services listing
│   ├── gallery.js            # Portfolio gallery
│   ├── shop.js               # Product shop
│   ├── book-online.js        # Booking system
│   ├── contact.js            # Contact page
│   └── api/
│       ├── public/           # Public API endpoints only
│       ├── bookings/         # Customer booking APIs
│       ├── checkout/         # Payment processing
│       └── customer/         # Customer account management
├── components/
│   ├── Layout.js             # Public layout (no admin nav)
│   ├── BookingForm.js        # Customer booking form
│   ├── ProductCard.js        # Shop product display
│   └── CustomerAuth.js       # Customer login/signup
├── contexts/
│   └── CustomerContext.js    # Customer authentication only
├── lib/
│   ├── supabase.js           # Public Supabase client (no service key)
│   └── public-utils.js       # Public-only utilities
└── styles/                   # Public styling only
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Supabase account (public access only)
- Square account (for payments)

### Installation
```bash
# Clone the public subdomain
git clone <repository-url> oceansoulsparkles-public
cd oceansoulsparkles-public

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your public-only configuration

# Run development server
npm run dev
```

### Environment Variables
```bash
# Public Supabase Configuration (READ-ONLY)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au
NEXT_PUBLIC_ADMIN_ACCESS=false

# Security Configuration
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_DEBUG_AUTH=false
ENABLE_AUTH_BYPASS=false

# Square Payments (Public transactions only)
NEXT_PUBLIC_SQUARE_APPLICATION_ID=your-square-app-id
NEXT_PUBLIC_SQUARE_LOCATION_ID=your-location-id
NEXT_PUBLIC_SQUARE_ENVIRONMENT=production

# Customer Notifications
NEXT_PUBLIC_ONESIGNAL_APP_ID=your-onesignal-app-id
```

## 📋 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run export       # Export static files
```

## 🔐 Security Measures

### Middleware Protection
- Blocks all admin routes at request level
- Validates API endpoint access
- Prevents admin subdomain access
- Enforces CORS policies

### Environment Isolation
- No service role keys exposed
- Public-only Supabase access
- Customer-only authentication
- Restricted API permissions

### Content Security Policy
- Strict CSP headers for public content
- Limited external script sources
- Secure payment processing only
- No admin script execution

## 🎯 Public Features

### Customer Booking System
- Online service booking
- Event booking requests
- Availability checking
- Payment processing

### Product Shop
- Eco-friendly glitter products
- Face paint supplies
- Gift cards
- Secure checkout

### Customer Accounts
- Registration and login
- Booking history
- Profile management
- Order tracking

### Content Display
- Service portfolio
- Image gallery
- About information
- Contact details

## 🚫 Blocked Features

The following features are **completely blocked** in this public subdomain:

- ❌ Admin dashboard access
- ❌ Staff portal functionality
- ❌ Artist/braider management
- ❌ Business analytics
- ❌ Customer data management (admin view)
- ❌ Payment processing management
- ❌ Service/product administration
- ❌ User role management
- ❌ System settings
- ❌ Database administration

## 🔄 Data Flow

```
Customer → Public Website → Public APIs → Supabase (Read-Only) → Display Data
Customer → Booking Form → Booking API → Payment Processing → Confirmation
```

## 📞 Support

For technical issues with the public website:
- **Customer Support**: <EMAIL>
- **Technical Issues**: Contact development team

## 🔗 Related Projects

- **Admin Subdomain**: `admin.oceansoulsparkles.com.au` (separate repository)
- **Staff Subdomain**: `staff.oceansoulsparkles.com.au` (separate repository)

---

**Note**: This is a security-focused public subdomain with zero admin functionality. All administrative features are handled by separate, secured subdomains with IP restrictions and enhanced authentication.
