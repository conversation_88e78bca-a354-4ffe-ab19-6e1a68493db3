import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function HeroSection({
  title,
  subtitle,
  backgroundImage,
  ctaText,
  ctaLink,
  secondaryCtaText,
  secondaryCtaLink,
  height = '100vh'
}) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const heroStyle = {
    height,
    background: backgroundImage 
      ? `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url(${backgroundImage})`
      : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundAttachment: 'fixed',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden'
  };

  const contentStyle = {
    textAlign: 'center',
    color: 'white',
    maxWidth: '800px',
    padding: '0 20px',
    transform: isVisible ? 'translateY(0)' : 'translateY(50px)',
    opacity: isVisible ? 1 : 0,
    transition: 'all 1s ease-out'
  };

  const titleStyle = {
    fontSize: 'clamp(2.5rem, 5vw, 4rem)',
    fontWeight: '700',
    marginBottom: '24px',
    textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)',
    background: 'linear-gradient(135deg, #ffffff, #f0f0f0)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text'
  };

  const subtitleStyle = {
    fontSize: 'clamp(1.1rem, 2.5vw, 1.5rem)',
    marginBottom: '40px',
    textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)',
    lineHeight: '1.6',
    color: 'rgba(255, 255, 255, 0.95)'
  };

  const ctaContainerStyle = {
    display: 'flex',
    gap: '20px',
    justifyContent: 'center',
    flexWrap: 'wrap'
  };

  const primaryCtaStyle = {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '8px',
    padding: '16px 32px',
    background: 'linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%)',
    color: 'white',
    textDecoration: 'none',
    borderRadius: '50px',
    fontSize: '18px',
    fontWeight: '600',
    transition: 'all 0.3s ease',
    boxShadow: '0 4px 20px rgba(55, 136, 216, 0.4)',
    border: 'none',
    cursor: 'pointer'
  };

  const secondaryCtaStyle = {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '8px',
    padding: '16px 32px',
    background: 'rgba(255, 255, 255, 0.1)',
    color: 'white',
    textDecoration: 'none',
    borderRadius: '50px',
    fontSize: '18px',
    fontWeight: '600',
    transition: 'all 0.3s ease',
    border: '2px solid rgba(255, 255, 255, 0.3)',
    backdropFilter: 'blur(10px)'
  };

  // Floating sparkles animation
  const sparkles = Array.from({ length: 20 }, (_, i) => (
    <div
      key={i}
      style={{
        position: 'absolute',
        width: '4px',
        height: '4px',
        background: 'white',
        borderRadius: '50%',
        top: `${Math.random() * 100}%`,
        left: `${Math.random() * 100}%`,
        animation: `sparkle ${3 + Math.random() * 4}s linear infinite`,
        animationDelay: `${Math.random() * 3}s`,
        opacity: 0.7
      }}
    />
  ));

  return (
    <section style={heroStyle}>
      {/* Animated sparkles */}
      {sparkles}
      
      <div style={contentStyle}>
        <h1 style={titleStyle}>{title}</h1>
        <p style={subtitleStyle}>{subtitle}</p>
        
        <div style={ctaContainerStyle}>
          {ctaText && ctaLink && (
            <Link href={ctaLink} style={primaryCtaStyle}>
              {ctaText}
              <span style={{ fontSize: '20px' }}>✨</span>
            </Link>
          )}
          
          {secondaryCtaText && secondaryCtaLink && (
            <Link href={secondaryCtaLink} style={secondaryCtaStyle}>
              {secondaryCtaText}
              <span style={{ fontSize: '16px' }}>↓</span>
            </Link>
          )}
        </div>
      </div>

      <style jsx>{`
        @keyframes sparkle {
          0%, 100% {
            opacity: 0;
            transform: scale(0);
          }
          50% {
            opacity: 1;
            transform: scale(1);
          }
        }
        
        @media (max-width: 768px) {
          section {
            background-attachment: scroll !important;
          }
        }
      `}</style>
    </section>
  );
}
