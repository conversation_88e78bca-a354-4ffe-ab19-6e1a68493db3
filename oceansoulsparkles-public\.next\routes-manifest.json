{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/(.*)", "has": [{"type": "header", "key": "x-forwarded-proto", "value": "http"}], "destination": "https://www.oceansoulsparkles.com.au/$1", "statusCode": 308, "regex": "^(?!/_next)(?:/(.*))(?:/)?$"}, {"source": "/(.*)", "has": [{"type": "host", "value": "oceansoulsparkles.com.au"}], "destination": "https://www.oceansoulsparkles.com.au/$1", "statusCode": 308, "regex": "^(?!/_next)(?:/(.*))(?:/)?$"}, {"source": "/admin/:path*", "destination": "/404", "statusCode": 307, "regex": "^(?!/_next)/admin(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://www.oceansoulsparkles.com.au"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.onesignal.com https://js.squareup.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://ndlgbcsbidyhxbpqzgqp.supabase.co https://api.squareup.com https://pci-connect.squareup.com; frame-src 'self' https://js.squareup.com"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/admin/:path*", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow"}], "regex": "^/admin(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/book-online", "regex": "^/book\\-online(?:/)?$", "routeKeys": {}, "namedRegex": "^/book\\-online(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/shop", "regex": "^/shop(?:/)?$", "routeKeys": {}, "namedRegex": "^/shop(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": [{"source": "/.well-known/apple-developer-merchantid-domain-association", "destination": "/api/apple-pay/domain-association", "regex": "^/\\.well-known/apple-developer-merchantid-domain-association(?:/)?$"}]}