{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|manifest.json|sw.js|robots.txt|sitemap.xml).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|manifest.json|sw.js|robots.txt|sitemap.xml).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "f5flIGEbLRuVnhvdr_1sY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eYVj7tok02e/SL8Flsz02T9bkONUuw3xaVKTYC20BG8=", "__NEXT_PREVIEW_MODE_ID": "0add57c078dc1aecee90ae13281d8ce1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5361e2a759e5fc40d47c91b6315afb5e84c99a1baefc5ee87a1ec50ef871417c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "816b4615c5afe7715c046e6378234415b3af490000dae1be8344720db9a8b413"}}}, "functions": {}, "sortedMiddleware": ["/"]}