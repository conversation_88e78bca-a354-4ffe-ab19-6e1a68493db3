{"version": 3, "sources": ["../../../../src/server/typescript/rules/server-boundary.ts"], "names": ["isPromiseType", "type", "typeC<PERSON>cker", "typeReferenceType", "target", "test", "typeToString", "isFunctionReturningPromise", "node", "ts", "getTypeAtLocation", "signatures", "getSignaturesOfType", "SignatureKind", "Call", "isPromise", "length", "signature", "returnType", "getReturnType", "isUnion", "t", "types", "serverBoundary", "getSemanticDiagnosticsForExportDeclaration", "source", "getTs", "getType<PERSON><PERSON>cker", "diagnostics", "exportClause", "isNamedExports", "e", "elements", "push", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "INVALID_SERVER_ENTRY_RETURN", "messageText", "start", "getStart", "getWidth", "getSemanticDiagnosticsForExportVariableStatement", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "initializer", "isArrowFunction", "isFunctionDeclaration", "isFunctionExpression", "isCallExpression", "isIdentifier", "getSemanticDiagnosticsForFunctionExport"], "mappings": "AAAA,mFAAmF;;;;;+BA2JnF;;;eAAA;;;0BAzJ+B;uBACO;AAGtC,qCAAqC;AACrC,SAASA,cAAcC,IAAmB,EAAEC,WAAiC;IAC3E,MAAMC,oBAAoBF;IAC1B,IAAI,CAACE,kBAAkBC,MAAM,EAAE,OAAO;IAEtC,2CAA2C;IAC3C,IACE,CAAC,mBAAmBC,IAAI,CAACH,YAAYI,YAAY,CAACH,kBAAkBC,MAAM,IAC1E;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASG,2BACPC,IAAmB,EACnBN,WAAiC,EACjCO,EAAmB;IAEnB,MAAMR,OAAOC,YAAYQ,iBAAiB,CAACF;IAC3C,MAAMG,aAAaT,YAAYU,mBAAmB,CAChDX,MACAQ,GAAGI,aAAa,CAACC,IAAI;IAGvB,IAAIC,YAAY;IAChB,IAAIJ,WAAWK,MAAM,EAAE;QACrB,KAAK,MAAMC,aAAaN,WAAY;YAClC,MAAMO,aAAaD,UAAUE,aAAa;YAC1C,IAAID,WAAWE,OAAO,IAAI;gBACxB,KAAK,MAAMC,KAAKH,WAAWI,KAAK,CAAE;oBAChC,IAAI,CAACtB,cAAcqB,GAAGnB,cAAc;wBAClCa,YAAY;wBACZ;oBACF;gBACF;YACF,OAAO;gBACLA,YAAYf,cAAckB,YAAYhB;YACxC;QACF;IACF,OAAO;QACLa,YAAY;IACd;IAEA,OAAOA;AACT;AAEA,MAAMQ,iBAAiB;IACrBC,4CACEC,MAA2B,EAC3BjB,IAAgC;QAEhC,MAAMC,KAAKiB,IAAAA,YAAK;QAChB,MAAMxB,cAAcyB,IAAAA,qBAAc;QAClC,IAAI,CAACzB,aAAa,OAAO,EAAE;QAE3B,MAAM0B,cAAqC,EAAE;QAE7C,MAAMC,eAAerB,KAAKqB,YAAY;QACtC,IAAIA,gBAAgBpB,GAAGqB,cAAc,CAACD,eAAe;YACnD,KAAK,MAAME,KAAKF,aAAaG,QAAQ,CAAE;gBACrC,IAAI,CAACzB,2BAA2BwB,GAAG7B,aAAaO,KAAK;oBACnDmB,YAAYK,IAAI,CAAC;wBACfC,MAAMT;wBACNU,UAAU1B,GAAG2B,kBAAkB,CAACC,KAAK;wBACrCC,MAAMC,wBAAc,CAACC,2BAA2B;wBAChDC,aAAa,CAAC,sDAAsD,CAAC;wBACrEC,OAAOX,EAAEY,QAAQ;wBACjB3B,QAAQe,EAAEa,QAAQ;oBACpB;gBACF;YACF;QACF;QAEA,OAAOhB;IACT;IAEAiB,kDACEpB,MAA2B,EAC3BjB,IAAgC;QAEhC,MAAMC,KAAKiB,IAAAA,YAAK;QAEhB,MAAME,cAAqC,EAAE;QAE7C,IAAInB,GAAGqC,yBAAyB,CAACtC,KAAKuC,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAexC,KAAKuC,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,cAAcF,YAAYE,WAAW;gBAC3C,IACEA,eACCzC,CAAAA,GAAG0C,eAAe,CAACD,gBAClBzC,GAAG2C,qBAAqB,CAACF,gBACzBzC,GAAG4C,oBAAoB,CAACH,gBACxBzC,GAAG6C,gBAAgB,CAACJ,gBACpBzC,GAAG8C,YAAY,CAACL,YAAW,GAC7B;oBACAtB,YAAYK,IAAI,IACXV,eAAeiC,uCAAuC,CACvD/B,QACAyB;gBAGN,OAAO;oBACLtB,YAAYK,IAAI,CAAC;wBACfC,MAAMT;wBACNU,UAAU1B,GAAG2B,kBAAkB,CAACC,KAAK;wBACrCC,MAAMC,wBAAc,CAACC,2BAA2B;wBAChDC,aAAa,CAAC,sDAAsD,CAAC;wBACrEC,OAAOM,YAAYL,QAAQ;wBAC3B3B,QAAQgC,YAAYJ,QAAQ;oBAC9B;gBACF;YACF;QACF;QAEA,OAAOhB;IACT;IAEA4B,yCACE/B,MAA2B,EAC3BjB,IAKuB;QAEvB,MAAMC,KAAKiB,IAAAA,YAAK;QAChB,MAAMxB,cAAcyB,IAAAA,qBAAc;QAClC,IAAI,CAACzB,aAAa,OAAO,EAAE;QAE3B,MAAM0B,cAAqC,EAAE;QAE7C,IAAI,CAACrB,2BAA2BC,MAAMN,aAAaO,KAAK;YACtDmB,YAAYK,IAAI,CAAC;gBACfC,MAAMT;gBACNU,UAAU1B,GAAG2B,kBAAkB,CAACC,KAAK;gBACrCC,MAAMC,wBAAc,CAACC,2BAA2B;gBAChDC,aAAa,CAAC,mHAAmH,CAAC;gBAClIC,OAAOlC,KAAKmC,QAAQ;gBACpB3B,QAAQR,KAAKoC,QAAQ;YACvB;QACF;QAEA,OAAOhB;IACT;AACF;MAEA,WAAeL"}