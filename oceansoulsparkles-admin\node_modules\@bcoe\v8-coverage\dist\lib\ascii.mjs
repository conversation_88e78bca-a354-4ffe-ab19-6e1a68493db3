import { compareRangeCovs } from "./compare";
export function emitForest(trees) {
    return emitForestLines(trees).join("\n");
}
export function emitForestLines(trees) {
    const colMap = getColMap(trees);
    const header = emitOffsets(colMap);
    return [header, ...trees.map(tree => emitTree(tree, colMap).join("\n"))];
}
function getColMap(trees) {
    const eventSet = new Set();
    for (const tree of trees) {
        const stack = [tree];
        while (stack.length > 0) {
            const cur = stack.pop();
            eventSet.add(cur.start);
            eventSet.add(cur.end);
            for (const child of cur.children) {
                stack.push(child);
            }
        }
    }
    const events = [...eventSet];
    events.sort((a, b) => a - b);
    let maxDigits = 1;
    for (const event of events) {
        maxDigits = Math.max(maxDigits, event.toString(10).length);
    }
    const colWidth = maxDigits + 3;
    const colMap = new Map();
    for (const [i, event] of events.entries()) {
        colMap.set(event, i * colWidth);
    }
    return colMap;
}
function emitTree(tree, colMap) {
    const layers = [];
    let nextLayer = [tree];
    while (nextLayer.length > 0) {
        const layer = nextLayer;
        layers.push(layer);
        nextLayer = [];
        for (const node of layer) {
            for (const child of node.children) {
                nextLayer.push(child);
            }
        }
    }
    return layers.map(layer => emitTreeLayer(layer, colMap));
}
export function parseFunctionRanges(text, offsetMap) {
    const result = [];
    for (const line of text.split("\n")) {
        for (const range of parseTreeLayer(line, offsetMap)) {
            result.push(range);
        }
    }
    result.sort(compareRangeCovs);
    return result;
}
/**
 *
 * @param layer Sorted list of disjoint trees.
 * @param colMap
 */
function emitTreeLayer(layer, colMap) {
    const line = [];
    let curIdx = 0;
    for (const { start, end, count } of layer) {
        const startIdx = colMap.get(start);
        const endIdx = colMap.get(end);
        if (startIdx > curIdx) {
            line.push(" ".repeat(startIdx - curIdx));
        }
        line.push(emitRange(count, endIdx - startIdx));
        curIdx = endIdx;
    }
    return line.join("");
}
function parseTreeLayer(text, offsetMap) {
    const result = [];
    const regex = /\[(\d+)-*\)/gs;
    while (true) {
        const match = regex.exec(text);
        if (match === null) {
            break;
        }
        const startIdx = match.index;
        const endIdx = startIdx + match[0].length;
        const count = parseInt(match[1], 10);
        const startOffset = offsetMap.get(startIdx);
        const endOffset = offsetMap.get(endIdx);
        if (startOffset === undefined || endOffset === undefined) {
            throw new Error(`Invalid offsets for: ${JSON.stringify(text)}`);
        }
        result.push({ startOffset, endOffset, count });
    }
    return result;
}
function emitRange(count, len) {
    const rangeStart = `[${count.toString(10)}`;
    const rangeEnd = ")";
    const hyphensLen = len - (rangeStart.length + rangeEnd.length);
    const hyphens = "-".repeat(Math.max(0, hyphensLen));
    return `${rangeStart}${hyphens}${rangeEnd}`;
}
function emitOffsets(colMap) {
    let line = "";
    for (const [event, col] of colMap) {
        if (line.length < col) {
            line += " ".repeat(col - line.length);
        }
        line += event.toString(10);
    }
    return line;
}
export function parseOffsets(text) {
    const result = new Map();
    const regex = /\d+/gs;
    while (true) {
        const match = regex.exec(text);
        if (match === null) {
            break;
        }
        result.set(match.index, parseInt(match[0], 10));
    }
    return result;
}

//# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIl9zcmMvYXNjaWkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsT0FBTyxFQUFFLGdCQUFnQixFQUFFLE1BQU0sV0FBVyxDQUFDO0FBVTdDLE1BQU0sVUFBVSxVQUFVLENBQUMsS0FBdUM7SUFDaEUsT0FBTyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO0FBQzNDLENBQUM7QUFFRCxNQUFNLFVBQVUsZUFBZSxDQUFDLEtBQXVDO0lBQ3JFLE1BQU0sTUFBTSxHQUF3QixTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDckQsTUFBTSxNQUFNLEdBQVcsV0FBVyxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBQzNDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO0FBQzNFLENBQUM7QUFFRCxTQUFTLFNBQVMsQ0FBQyxLQUFrQztJQUNuRCxNQUFNLFFBQVEsR0FBZ0IsSUFBSSxHQUFHLEVBQUUsQ0FBQztJQUN4QyxLQUFLLE1BQU0sSUFBSSxJQUFJLEtBQUssRUFBRTtRQUN4QixNQUFNLEtBQUssR0FBd0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUMxQyxPQUFPLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQ3ZCLE1BQU0sR0FBRyxHQUFzQixLQUFLLENBQUMsR0FBRyxFQUFHLENBQUM7WUFDNUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDeEIsUUFBUSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDdEIsS0FBSyxNQUFNLEtBQUssSUFBSSxHQUFHLENBQUMsUUFBUSxFQUFFO2dCQUNoQyxLQUFLLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO2FBQ25CO1NBQ0Y7S0FDRjtJQUNELE1BQU0sTUFBTSxHQUFhLENBQUMsR0FBRyxRQUFRLENBQUMsQ0FBQztJQUN2QyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQzdCLElBQUksU0FBUyxHQUFXLENBQUMsQ0FBQztJQUMxQixLQUFLLE1BQU0sS0FBSyxJQUFJLE1BQU0sRUFBRTtRQUMxQixTQUFTLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsS0FBSyxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQztLQUM1RDtJQUNELE1BQU0sUUFBUSxHQUFXLFNBQVMsR0FBRyxDQUFDLENBQUM7SUFDdkMsTUFBTSxNQUFNLEdBQXdCLElBQUksR0FBRyxFQUFFLENBQUM7SUFDOUMsS0FBSyxNQUFNLENBQUMsQ0FBQyxFQUFFLEtBQUssQ0FBQyxJQUFJLE1BQU0sQ0FBQyxPQUFPLEVBQUUsRUFBRTtRQUN6QyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssRUFBRSxDQUFDLEdBQUcsUUFBUSxDQUFDLENBQUM7S0FDakM7SUFDRCxPQUFPLE1BQU0sQ0FBQztBQUNoQixDQUFDO0FBRUQsU0FBUyxRQUFRLENBQUMsSUFBdUIsRUFBRSxNQUEyQjtJQUNwRSxNQUFNLE1BQU0sR0FBMEIsRUFBRSxDQUFDO0lBQ3pDLElBQUksU0FBUyxHQUF3QixDQUFDLElBQUksQ0FBQyxDQUFDO0lBQzVDLE9BQU8sU0FBUyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7UUFDM0IsTUFBTSxLQUFLLEdBQXdCLFNBQVMsQ0FBQztRQUM3QyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ25CLFNBQVMsR0FBRyxFQUFFLENBQUM7UUFDZixLQUFLLE1BQU0sSUFBSSxJQUFJLEtBQUssRUFBRTtZQUN4QixLQUFLLE1BQU0sS0FBSyxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUU7Z0JBQ2pDLFNBQVMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7YUFDdkI7U0FDRjtLQUNGO0lBQ0QsT0FBTyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUMsYUFBYSxDQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyxDQUFDO0FBQzNELENBQUM7QUFFRCxNQUFNLFVBQVUsbUJBQW1CLENBQUMsSUFBWSxFQUFFLFNBQThCO0lBQzlFLE1BQU0sTUFBTSxHQUFlLEVBQUUsQ0FBQztJQUM5QixLQUFLLE1BQU0sSUFBSSxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFDbkMsS0FBSyxNQUFNLEtBQUssSUFBSSxjQUFjLENBQUMsSUFBSSxFQUFFLFNBQVMsQ0FBQyxFQUFFO1lBQ25ELE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7U0FDcEI7S0FDRjtJQUNELE1BQU0sQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztJQUM5QixPQUFPLE1BQU0sQ0FBQztBQUNoQixDQUFDO0FBRUQ7Ozs7R0FJRztBQUNILFNBQVMsYUFBYSxDQUFDLEtBQTBCLEVBQUUsTUFBMkI7SUFDNUUsTUFBTSxJQUFJLEdBQWEsRUFBRSxDQUFDO0lBQzFCLElBQUksTUFBTSxHQUFXLENBQUMsQ0FBQztJQUN2QixLQUFLLE1BQU0sRUFBQyxLQUFLLEVBQUUsR0FBRyxFQUFFLEtBQUssRUFBQyxJQUFJLEtBQUssRUFBRTtRQUN2QyxNQUFNLFFBQVEsR0FBVyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBRSxDQUFDO1FBQzVDLE1BQU0sTUFBTSxHQUFXLE1BQU0sQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFFLENBQUM7UUFDeEMsSUFBSSxRQUFRLEdBQUcsTUFBTSxFQUFFO1lBQ3JCLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxRQUFRLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQztTQUMxQztRQUNELElBQUksQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxNQUFNLEdBQUcsUUFBUSxDQUFDLENBQUMsQ0FBQztRQUMvQyxNQUFNLEdBQUcsTUFBTSxDQUFDO0tBQ2pCO0lBQ0QsT0FBTyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDO0FBQ3ZCLENBQUM7QUFFRCxTQUFTLGNBQWMsQ0FBQyxJQUFZLEVBQUUsU0FBOEI7SUFDbEUsTUFBTSxNQUFNLEdBQWUsRUFBRSxDQUFDO0lBQzlCLE1BQU0sS0FBSyxHQUFXLGVBQWUsQ0FBQztJQUN0QyxPQUFPLElBQUksRUFBRTtRQUNYLE1BQU0sS0FBSyxHQUE0QixLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3hELElBQUksS0FBSyxLQUFLLElBQUksRUFBRTtZQUNsQixNQUFNO1NBQ1A7UUFDRCxNQUFNLFFBQVEsR0FBVyxLQUFLLENBQUMsS0FBTSxDQUFDO1FBQ3RDLE1BQU0sTUFBTSxHQUFXLFFBQVEsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDO1FBQ2xELE1BQU0sS0FBSyxHQUFXLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDN0MsTUFBTSxXQUFXLEdBQXVCLFNBQVMsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUM7UUFDaEUsTUFBTSxTQUFTLEdBQXVCLFNBQVMsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUM7UUFDNUQsSUFBSSxXQUFXLEtBQUssU0FBUyxJQUFJLFNBQVMsS0FBSyxTQUFTLEVBQUU7WUFDeEQsTUFBTSxJQUFJLEtBQUssQ0FBQyx3QkFBd0IsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUM7U0FDakU7UUFDRCxNQUFNLENBQUMsSUFBSSxDQUFDLEVBQUMsV0FBVyxFQUFFLFNBQVMsRUFBRSxLQUFLLEVBQUMsQ0FBQyxDQUFDO0tBQzlDO0lBQ0QsT0FBTyxNQUFNLENBQUM7QUFDaEIsQ0FBQztBQUVELFNBQVMsU0FBUyxDQUFDLEtBQWEsRUFBRSxHQUFXO0lBQzNDLE1BQU0sVUFBVSxHQUFXLElBQUksS0FBSyxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDO0lBQ3BELE1BQU0sUUFBUSxHQUFXLEdBQUcsQ0FBQztJQUM3QixNQUFNLFVBQVUsR0FBVyxHQUFHLEdBQUcsQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQztJQUN2RSxNQUFNLE9BQU8sR0FBVyxHQUFHLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLFVBQVUsQ0FBQyxDQUFDLENBQUM7SUFDNUQsT0FBTyxHQUFHLFVBQVUsR0FBRyxPQUFPLEdBQUcsUUFBUSxFQUFFLENBQUM7QUFDOUMsQ0FBQztBQUVELFNBQVMsV0FBVyxDQUFDLE1BQTJCO0lBQzlDLElBQUksSUFBSSxHQUFXLEVBQUUsQ0FBQztJQUN0QixLQUFLLE1BQU0sQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDLElBQUksTUFBTSxFQUFFO1FBQ2pDLElBQUksSUFBSSxDQUFDLE1BQU0sR0FBRyxHQUFHLEVBQUU7WUFDckIsSUFBSSxJQUFJLEdBQUcsQ0FBQyxNQUFNLENBQUMsR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztTQUN2QztRQUNELElBQUksSUFBSSxLQUFLLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDO0tBQzVCO0lBQ0QsT0FBTyxJQUFJLENBQUM7QUFDZCxDQUFDO0FBRUQsTUFBTSxVQUFVLFlBQVksQ0FBQyxJQUFZO0lBQ3ZDLE1BQU0sTUFBTSxHQUF3QixJQUFJLEdBQUcsRUFBRSxDQUFDO0lBQzlDLE1BQU0sS0FBSyxHQUFXLE9BQU8sQ0FBQztJQUM5QixPQUFPLElBQUksRUFBRTtRQUNYLE1BQU0sS0FBSyxHQUEyQixLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3ZELElBQUksS0FBSyxLQUFLLElBQUksRUFBRTtZQUNsQixNQUFNO1NBQ1A7UUFDRCxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDO0tBQ2pEO0lBQ0QsT0FBTyxNQUFNLENBQUM7QUFDaEIsQ0FBQyIsImZpbGUiOiJhc2NpaS5qcyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbXBhcmVSYW5nZUNvdnMgfSBmcm9tIFwiLi9jb21wYXJlXCI7XG5pbXBvcnQgeyBSYW5nZUNvdiB9IGZyb20gXCIuL3R5cGVzXCI7XG5cbmludGVyZmFjZSBSZWFkb25seVJhbmdlVHJlZSB7XG4gIHJlYWRvbmx5IHN0YXJ0OiBudW1iZXI7XG4gIHJlYWRvbmx5IGVuZDogbnVtYmVyO1xuICByZWFkb25seSBjb3VudDogbnVtYmVyO1xuICByZWFkb25seSBjaGlsZHJlbjogUmVhZG9ubHlSYW5nZVRyZWVbXTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGVtaXRGb3Jlc3QodHJlZXM6IFJlYWRvbmx5QXJyYXk8UmVhZG9ubHlSYW5nZVRyZWU+KTogc3RyaW5nIHtcbiAgcmV0dXJuIGVtaXRGb3Jlc3RMaW5lcyh0cmVlcykuam9pbihcIlxcblwiKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGVtaXRGb3Jlc3RMaW5lcyh0cmVlczogUmVhZG9ubHlBcnJheTxSZWFkb25seVJhbmdlVHJlZT4pOiBzdHJpbmdbXSB7XG4gIGNvbnN0IGNvbE1hcDogTWFwPG51bWJlciwgbnVtYmVyPiA9IGdldENvbE1hcCh0cmVlcyk7XG4gIGNvbnN0IGhlYWRlcjogc3RyaW5nID0gZW1pdE9mZnNldHMoY29sTWFwKTtcbiAgcmV0dXJuIFtoZWFkZXIsIC4uLnRyZWVzLm1hcCh0cmVlID0+IGVtaXRUcmVlKHRyZWUsIGNvbE1hcCkuam9pbihcIlxcblwiKSldO1xufVxuXG5mdW5jdGlvbiBnZXRDb2xNYXAodHJlZXM6IEl0ZXJhYmxlPFJlYWRvbmx5UmFuZ2VUcmVlPik6IE1hcDxudW1iZXIsIG51bWJlcj4ge1xuICBjb25zdCBldmVudFNldDogU2V0PG51bWJlcj4gPSBuZXcgU2V0KCk7XG4gIGZvciAoY29uc3QgdHJlZSBvZiB0cmVlcykge1xuICAgIGNvbnN0IHN0YWNrOiBSZWFkb25seVJhbmdlVHJlZVtdID0gW3RyZWVdO1xuICAgIHdoaWxlIChzdGFjay5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zdCBjdXI6IFJlYWRvbmx5UmFuZ2VUcmVlID0gc3RhY2sucG9wKCkhO1xuICAgICAgZXZlbnRTZXQuYWRkKGN1ci5zdGFydCk7XG4gICAgICBldmVudFNldC5hZGQoY3VyLmVuZCk7XG4gICAgICBmb3IgKGNvbnN0IGNoaWxkIG9mIGN1ci5jaGlsZHJlbikge1xuICAgICAgICBzdGFjay5wdXNoKGNoaWxkKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgY29uc3QgZXZlbnRzOiBudW1iZXJbXSA9IFsuLi5ldmVudFNldF07XG4gIGV2ZW50cy5zb3J0KChhLCBiKSA9PiBhIC0gYik7XG4gIGxldCBtYXhEaWdpdHM6IG51bWJlciA9IDE7XG4gIGZvciAoY29uc3QgZXZlbnQgb2YgZXZlbnRzKSB7XG4gICAgbWF4RGlnaXRzID0gTWF0aC5tYXgobWF4RGlnaXRzLCBldmVudC50b1N0cmluZygxMCkubGVuZ3RoKTtcbiAgfVxuICBjb25zdCBjb2xXaWR0aDogbnVtYmVyID0gbWF4RGlnaXRzICsgMztcbiAgY29uc3QgY29sTWFwOiBNYXA8bnVtYmVyLCBudW1iZXI+ID0gbmV3IE1hcCgpO1xuICBmb3IgKGNvbnN0IFtpLCBldmVudF0gb2YgZXZlbnRzLmVudHJpZXMoKSkge1xuICAgIGNvbE1hcC5zZXQoZXZlbnQsIGkgKiBjb2xXaWR0aCk7XG4gIH1cbiAgcmV0dXJuIGNvbE1hcDtcbn1cblxuZnVuY3Rpb24gZW1pdFRyZWUodHJlZTogUmVhZG9ubHlSYW5nZVRyZWUsIGNvbE1hcDogTWFwPG51bWJlciwgbnVtYmVyPik6IHN0cmluZ1tdIHtcbiAgY29uc3QgbGF5ZXJzOiBSZWFkb25seVJhbmdlVHJlZVtdW10gPSBbXTtcbiAgbGV0IG5leHRMYXllcjogUmVhZG9ubHlSYW5nZVRyZWVbXSA9IFt0cmVlXTtcbiAgd2hpbGUgKG5leHRMYXllci5sZW5ndGggPiAwKSB7XG4gICAgY29uc3QgbGF5ZXI6IFJlYWRvbmx5UmFuZ2VUcmVlW10gPSBuZXh0TGF5ZXI7XG4gICAgbGF5ZXJzLnB1c2gobGF5ZXIpO1xuICAgIG5leHRMYXllciA9IFtdO1xuICAgIGZvciAoY29uc3Qgbm9kZSBvZiBsYXllcikge1xuICAgICAgZm9yIChjb25zdCBjaGlsZCBvZiBub2RlLmNoaWxkcmVuKSB7XG4gICAgICAgIG5leHRMYXllci5wdXNoKGNoaWxkKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIGxheWVycy5tYXAobGF5ZXIgPT4gZW1pdFRyZWVMYXllcihsYXllciwgY29sTWFwKSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZUZ1bmN0aW9uUmFuZ2VzKHRleHQ6IHN0cmluZywgb2Zmc2V0TWFwOiBNYXA8bnVtYmVyLCBudW1iZXI+KTogUmFuZ2VDb3ZbXSB7XG4gIGNvbnN0IHJlc3VsdDogUmFuZ2VDb3ZbXSA9IFtdO1xuICBmb3IgKGNvbnN0IGxpbmUgb2YgdGV4dC5zcGxpdChcIlxcblwiKSkge1xuICAgIGZvciAoY29uc3QgcmFuZ2Ugb2YgcGFyc2VUcmVlTGF5ZXIobGluZSwgb2Zmc2V0TWFwKSkge1xuICAgICAgcmVzdWx0LnB1c2gocmFuZ2UpO1xuICAgIH1cbiAgfVxuICByZXN1bHQuc29ydChjb21wYXJlUmFuZ2VDb3ZzKTtcbiAgcmV0dXJuIHJlc3VsdDtcbn1cblxuLyoqXG4gKlxuICogQHBhcmFtIGxheWVyIFNvcnRlZCBsaXN0IG9mIGRpc2pvaW50IHRyZWVzLlxuICogQHBhcmFtIGNvbE1hcFxuICovXG5mdW5jdGlvbiBlbWl0VHJlZUxheWVyKGxheWVyOiBSZWFkb25seVJhbmdlVHJlZVtdLCBjb2xNYXA6IE1hcDxudW1iZXIsIG51bWJlcj4pOiBzdHJpbmcge1xuICBjb25zdCBsaW5lOiBzdHJpbmdbXSA9IFtdO1xuICBsZXQgY3VySWR4OiBudW1iZXIgPSAwO1xuICBmb3IgKGNvbnN0IHtzdGFydCwgZW5kLCBjb3VudH0gb2YgbGF5ZXIpIHtcbiAgICBjb25zdCBzdGFydElkeDogbnVtYmVyID0gY29sTWFwLmdldChzdGFydCkhO1xuICAgIGNvbnN0IGVuZElkeDogbnVtYmVyID0gY29sTWFwLmdldChlbmQpITtcbiAgICBpZiAoc3RhcnRJZHggPiBjdXJJZHgpIHtcbiAgICAgIGxpbmUucHVzaChcIiBcIi5yZXBlYXQoc3RhcnRJZHggLSBjdXJJZHgpKTtcbiAgICB9XG4gICAgbGluZS5wdXNoKGVtaXRSYW5nZShjb3VudCwgZW5kSWR4IC0gc3RhcnRJZHgpKTtcbiAgICBjdXJJZHggPSBlbmRJZHg7XG4gIH1cbiAgcmV0dXJuIGxpbmUuam9pbihcIlwiKTtcbn1cblxuZnVuY3Rpb24gcGFyc2VUcmVlTGF5ZXIodGV4dDogc3RyaW5nLCBvZmZzZXRNYXA6IE1hcDxudW1iZXIsIG51bWJlcj4pOiBSYW5nZUNvdltdIHtcbiAgY29uc3QgcmVzdWx0OiBSYW5nZUNvdltdID0gW107XG4gIGNvbnN0IHJlZ2V4OiBSZWdFeHAgPSAvXFxbKFxcZCspLSpcXCkvZ3M7XG4gIHdoaWxlICh0cnVlKSB7XG4gICAgY29uc3QgbWF0Y2g6IFJlZ0V4cE1hdGNoQXJyYXkgfCBudWxsID0gcmVnZXguZXhlYyh0ZXh0KTtcbiAgICBpZiAobWF0Y2ggPT09IG51bGwpIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgICBjb25zdCBzdGFydElkeDogbnVtYmVyID0gbWF0Y2guaW5kZXghO1xuICAgIGNvbnN0IGVuZElkeDogbnVtYmVyID0gc3RhcnRJZHggKyBtYXRjaFswXS5sZW5ndGg7XG4gICAgY29uc3QgY291bnQ6IG51bWJlciA9IHBhcnNlSW50KG1hdGNoWzFdLCAxMCk7XG4gICAgY29uc3Qgc3RhcnRPZmZzZXQ6IG51bWJlciB8IHVuZGVmaW5lZCA9IG9mZnNldE1hcC5nZXQoc3RhcnRJZHgpO1xuICAgIGNvbnN0IGVuZE9mZnNldDogbnVtYmVyIHwgdW5kZWZpbmVkID0gb2Zmc2V0TWFwLmdldChlbmRJZHgpO1xuICAgIGlmIChzdGFydE9mZnNldCA9PT0gdW5kZWZpbmVkIHx8IGVuZE9mZnNldCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEludmFsaWQgb2Zmc2V0cyBmb3I6ICR7SlNPTi5zdHJpbmdpZnkodGV4dCl9YCk7XG4gICAgfVxuICAgIHJlc3VsdC5wdXNoKHtzdGFydE9mZnNldCwgZW5kT2Zmc2V0LCBjb3VudH0pO1xuICB9XG4gIHJldHVybiByZXN1bHQ7XG59XG5cbmZ1bmN0aW9uIGVtaXRSYW5nZShjb3VudDogbnVtYmVyLCBsZW46IG51bWJlcik6IHN0cmluZyB7XG4gIGNvbnN0IHJhbmdlU3RhcnQ6IHN0cmluZyA9IGBbJHtjb3VudC50b1N0cmluZygxMCl9YDtcbiAgY29uc3QgcmFuZ2VFbmQ6IHN0cmluZyA9IFwiKVwiO1xuICBjb25zdCBoeXBoZW5zTGVuOiBudW1iZXIgPSBsZW4gLSAocmFuZ2VTdGFydC5sZW5ndGggKyByYW5nZUVuZC5sZW5ndGgpO1xuICBjb25zdCBoeXBoZW5zOiBzdHJpbmcgPSBcIi1cIi5yZXBlYXQoTWF0aC5tYXgoMCwgaHlwaGVuc0xlbikpO1xuICByZXR1cm4gYCR7cmFuZ2VTdGFydH0ke2h5cGhlbnN9JHtyYW5nZUVuZH1gO1xufVxuXG5mdW5jdGlvbiBlbWl0T2Zmc2V0cyhjb2xNYXA6IE1hcDxudW1iZXIsIG51bWJlcj4pOiBzdHJpbmcge1xuICBsZXQgbGluZTogc3RyaW5nID0gXCJcIjtcbiAgZm9yIChjb25zdCBbZXZlbnQsIGNvbF0gb2YgY29sTWFwKSB7XG4gICAgaWYgKGxpbmUubGVuZ3RoIDwgY29sKSB7XG4gICAgICBsaW5lICs9IFwiIFwiLnJlcGVhdChjb2wgLSBsaW5lLmxlbmd0aCk7XG4gICAgfVxuICAgIGxpbmUgKz0gZXZlbnQudG9TdHJpbmcoMTApO1xuICB9XG4gIHJldHVybiBsaW5lO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gcGFyc2VPZmZzZXRzKHRleHQ6IHN0cmluZyk6IE1hcDxudW1iZXIsIG51bWJlcj4ge1xuICBjb25zdCByZXN1bHQ6IE1hcDxudW1iZXIsIG51bWJlcj4gPSBuZXcgTWFwKCk7XG4gIGNvbnN0IHJlZ2V4OiBSZWdFeHAgPSAvXFxkKy9ncztcbiAgd2hpbGUgKHRydWUpIHtcbiAgICBjb25zdCBtYXRjaDogUmVnRXhwRXhlY0FycmF5IHwgbnVsbCA9IHJlZ2V4LmV4ZWModGV4dCk7XG4gICAgaWYgKG1hdGNoID09PSBudWxsKSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgcmVzdWx0LnNldChtYXRjaC5pbmRleCwgcGFyc2VJbnQobWF0Y2hbMF0sIDEwKSk7XG4gIH1cbiAgcmV0dXJuIHJlc3VsdDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0=
