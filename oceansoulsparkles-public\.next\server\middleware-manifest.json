{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|manifest.json|sw.js|robots.txt|sitemap.xml).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|manifest.json|sw.js|robots.txt|sitemap.xml).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "XgPDk9_o5bPs6HOatNRcc", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NBCEcpz0q/fTZiA9RrXyvFYuFlw+smW4+90zKBWTLNs=", "__NEXT_PREVIEW_MODE_ID": "fe7beda44d76af21790e66e6d2fa29c0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "20d8ec701db47aab59f09d25cf11ad3e080d599555a4197f39b2739f12bb6cc8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "255b6a8a89b0bc86df22895dc273dc3068e11ae7ce8c372f81bd0abe96d6d0be"}}}, "functions": {}, "sortedMiddleware": ["/"]}