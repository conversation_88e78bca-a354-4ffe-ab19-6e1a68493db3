/* Layout Styles */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.headerContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.logo img {
  height: 50px;
  width: auto;
}

.nav {
  display: flex;
  align-items: center;
}

.nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 32px;
}

.nav a {
  color: #2c3e50;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease;
  position: relative;
}

.nav a:hover {
  color: #3788d8;
}

.nav li.active a {
  color: #3788d8;
}

.nav li.active a::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 2px;
  background: #3788d8;
}

.mobileToggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.mobileToggle span {
  width: 25px;
  height: 3px;
  background: #2c3e50;
  margin: 3px 0;
  transition: 0.3s;
}

.mobileToggle.active span:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.mobileToggle.active span:nth-child(2) {
  opacity: 0;
}

.mobileToggle.active span:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

.bookNowContainer {
  margin-left: 32px;
}

.bookNowButton {
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(55, 136, 216, 0.3);
}

.bookNowButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.4);
}

.footer {
  background: #2c3e50;
  color: white;
  margin-top: auto;
  position: relative;
}

.footerWave {
  position: absolute;
  top: -1px;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
}

.footerWave svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 60px;
}

.footerContent {
  padding: 60px 0 40px;
}

.footerGrid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 40px;
}

.footerBranding h3 {
  background: linear-gradient(135deg, #4ECDC4, #3788d8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 24px;
  margin-bottom: 16px;
}

.footerTagline {
  color: #adb5bd;
  margin-bottom: 24px;
  line-height: 1.6;
}

.footerSocial {
  display: flex;
  gap: 16px;
}

.footerSocialLink img {
  width: 32px;
  height: 32px;
  transition: transform 0.2s ease;
}

.footerSocialLink:hover img {
  transform: scale(1.1);
}

.footerLinks h3,
.footerContact h3 {
  color: white;
  margin-bottom: 20px;
}

.footerLinks ul {
  list-style: none;
  padding: 0;
}

.footerLinks li {
  margin-bottom: 8px;
}

.footerLink {
  color: #adb5bd;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footerLink:hover {
  color: #4ECDC4;
}

.footerContact p {
  color: #adb5bd;
  margin-bottom: 8px;
}

.footerContactLink {
  color: #4ECDC4;
  text-decoration: none;
}

.footerButton {
  display: inline-block;
  margin-top: 16px;
  padding: 10px 20px;
  border: 2px solid #4ECDC4;
  color: #4ECDC4;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.footerButton:hover {
  background: #4ECDC4;
  color: #2c3e50;
}

.footerPayments {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #495057;
}

.footerPayments h3 {
  margin-bottom: 16px;
  color: white;
}

.paymentLogos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 12px;
}

.securePaymentNote {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #adb5bd;
  font-size: 14px;
}

.footerBottom {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #495057;
  color: #adb5bd;
  font-size: 14px;
}

.scrollToTop {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: #3788d8;
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
  transition: all 0.2s ease;
  z-index: 1000;
}

.scrollToTop:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(55, 136, 216, 0.4);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .headerContainer {
    padding: 0 16px;
  }

  .nav {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
  }

  .nav.open {
    transform: translateY(0);
  }

  .nav ul {
    flex-direction: column;
    padding: 20px;
    gap: 16px;
  }

  .mobileToggle {
    display: flex;
  }

  .bookNowContainer {
    margin-left: 16px;
  }

  .footerGrid {
    grid-template-columns: 1fr;
    gap: 32px;
    text-align: center;
  }

  .footerSocial {
    justify-content: center;
  }
}
