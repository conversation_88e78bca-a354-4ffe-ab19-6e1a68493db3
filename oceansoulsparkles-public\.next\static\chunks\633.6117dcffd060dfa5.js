"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[633],{7633:function(e,t,n){n.r(t),n.d(t,{initializeAnalytics:function(){return o},setUserProperties:function(){return p},trackAddToCart:function(){return _},trackBooking:function(){return a},trackBookingComplete:function(){return l},trackContactForm:function(){return s},trackDownload:function(){return w},trackEngagement:function(){return d},trackEvent:function(){return c},trackExternalLink:function(){return f},trackPageView:function(){return r},trackProductView:function(){return g},trackScrollDepth:function(){return m},trackSearch:function(){return u}});var i=n(7061);async function o(){if(i.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID)try{let t=document.createElement("script");function e(){window.dataLayer.push(arguments)}t.async=!0,t.src="https://www.googletagmanager.com/gtag/js?id=".concat(i.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID),document.head.appendChild(t),window.dataLayer=window.dataLayer||[],window.gtag=e,e("js",new Date),e("config",i.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,{page_title:document.title,page_location:window.location.href,anonymize_ip:!0,allow_google_signals:!1,allow_ad_personalization_signals:!1,cookie_flags:"SameSite=Lax;Secure"}),console.log("[Analytics] Google Analytics initialized")}catch(e){console.error("[Analytics] Failed to initialize Google Analytics:",e)}}function r(e,t){if(window.gtag)try{window.gtag("config",i.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,{page_path:e,page_title:t}),console.log("[Analytics] Page view tracked:",{url:e,title:t})}catch(e){console.error("[Analytics] Error tracking page view:",e)}}function c(e,t,n,i){if(window.gtag)try{window.gtag("event",e,{event_category:t,event_label:n,value:i}),console.log("[Analytics] Event tracked:",{action:e,category:t,label:n,value:i})}catch(e){console.error("[Analytics] Error tracking event:",e)}}function a(e){c("booking_started","engagement","booking_form",1),e.service_name&&c("service_selected","booking",e.service_name,1)}function l(e){c("booking_completed","conversion",e.service_name,e.estimated_total),window.gtag("event","conversion",{send_to:i.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,value:e.estimated_total,currency:"AUD",transaction_id:e.booking_id})}function g(e){c("view_item","ecommerce",e.name,e.price),window.gtag("event","view_item",{currency:"AUD",value:e.price,items:[{item_id:e.id,item_name:e.name,item_category:e.category_name,price:e.price,quantity:1}]})}function _(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;c("add_to_cart","ecommerce",e.name,e.price*t),window.gtag("event","add_to_cart",{currency:"AUD",value:e.price*t,items:[{item_id:e.id,item_name:e.name,item_category:e.category_name,price:e.price,quantity:t}]})}function s(e){c("form_submit","engagement",e,1)}function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";c("search","engagement",e,1),window.gtag("event","search",{search_term:e,content_category:t})}function d(e,t){c(e,"engagement",t,1)}function m(e){(25===e||50===e||75===e||100===e)&&c("scroll","engagement","".concat(e,"%"),e)}function w(e,t){c("file_download","engagement",e,1),window.gtag("event","file_download",{file_name:e,file_extension:t})}function f(e,t){c("click","external_link",e,1),window.gtag("event","click",{link_url:e,link_text:t,outbound:!0})}function p(e){if(window.gtag)try{let t={customer_type:e.customer_type,preferred_service:e.preferred_service,location_region:e.location_region},n=Object.fromEntries(Object.entries(t).filter(e=>{let[t,n]=e;return void 0!==n}));window.gtag("set","user_properties",n),console.log("[Analytics] User properties set:",n)}catch(e){console.error("[Analytics] Error setting user properties:",e)}}}}]);