(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[616],{8588:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/signup",function(){return s(4894)}])},4894:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return _}});var r=s(5893),n=s(7294),t=s(1163),o=s(9008),i=s.n(o),l=s(1664),c=s.n(l),u=s(5497),d=s(1817),h=s(2920),m=s(589),p=s.n(m);function _(){let[e,a]=(0,n.useState)({email:"",password:"",confirmPassword:"",firstName:"",lastName:"",phone:""}),[s,o]=(0,n.useState)(!1),[l,m]=(0,n.useState)(!1),[_,x]=(0,n.useState)(!1),[f,b]=(0,n.useState)(!1),{signUp:N,isAuthenticated:j,loading:g}=(0,d.O)(),v=(0,t.useRouter)();(0,n.useEffect)(()=>{j&&!g&&v.replace("/")},[j,g,v]);let k=e=>{let{name:s,value:r}=e.target;a(e=>({...e,[s]:r}))},w=()=>{let{email:a,password:s,confirmPassword:r,firstName:n,lastName:t}=e;return a&&s&&r&&n&&t?s.length<8?(h.Am.error("Password must be at least 8 characters long"),!1):s!==r?(h.Am.error("Passwords do not match"),!1):!!f||(h.Am.error("Please accept the Terms of Service and Privacy Policy"),!1):(h.Am.error("Please fill in all required fields"),!1)},A=async a=>{if(a.preventDefault(),w()){o(!0);try{let a={first_name:e.firstName,last_name:e.lastName,phone:e.phone,full_name:"".concat(e.firstName," ").concat(e.lastName)};(await N(e.email,e.password,a)).success&&(h.Am.success("Account created successfully! Please check your email to verify your account."),setTimeout(()=>{v.push("/login")},2e3))}catch(e){console.error("Signup error:",e),h.Am.error("Account creation failed. Please try again.")}finally{o(!1)}}};return g?(0,r.jsx)(u.Z,{children:(0,r.jsx)("div",{className:p().authContainer,children:(0,r.jsx)("div",{className:p().authCard,children:(0,r.jsxs)("div",{className:p().loadingSpinner,children:[(0,r.jsx)("div",{className:p().spinner}),(0,r.jsx)("p",{children:"Loading..."})]})})})}):j?null:(0,r.jsxs)(u.Z,{children:[(0,r.jsxs)(i(),{children:[(0,r.jsx)("title",{children:"Create Account - Ocean Soul Sparkles"}),(0,r.jsx)("meta",{name:"description",content:"Create your Ocean Soul Sparkles customer account to book services and manage your orders."}),(0,r.jsx)("meta",{name:"robots",content:"noindex, nofollow"})]}),(0,r.jsxs)("div",{className:p().authContainer,children:[(0,r.jsxs)("div",{className:p().authCard,children:[(0,r.jsxs)("div",{className:p().authHeader,children:[(0,r.jsx)("h1",{children:"Create Your Account"}),(0,r.jsx)("p",{children:"Join Ocean Soul Sparkles to book services and track your orders"})]}),(0,r.jsxs)("form",{onSubmit:A,className:p().authForm,children:[(0,r.jsxs)("div",{className:p().formRow,children:[(0,r.jsxs)("div",{className:p().formGroup,children:[(0,r.jsx)("label",{htmlFor:"firstName",children:"First Name *"}),(0,r.jsx)("input",{id:"firstName",name:"firstName",type:"text",value:e.firstName,onChange:k,placeholder:"Enter your first name",required:!0,disabled:s,className:p().formInput})]}),(0,r.jsxs)("div",{className:p().formGroup,children:[(0,r.jsx)("label",{htmlFor:"lastName",children:"Last Name *"}),(0,r.jsx)("input",{id:"lastName",name:"lastName",type:"text",value:e.lastName,onChange:k,placeholder:"Enter your last name",required:!0,disabled:s,className:p().formInput})]})]}),(0,r.jsxs)("div",{className:p().formGroup,children:[(0,r.jsx)("label",{htmlFor:"email",children:"Email Address *"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",value:e.email,onChange:k,placeholder:"Enter your email",required:!0,disabled:s,className:p().formInput})]}),(0,r.jsxs)("div",{className:p().formGroup,children:[(0,r.jsx)("label",{htmlFor:"phone",children:"Phone Number"}),(0,r.jsx)("input",{id:"phone",name:"phone",type:"tel",value:e.phone,onChange:k,placeholder:"Enter your phone number",disabled:s,className:p().formInput})]}),(0,r.jsxs)("div",{className:p().formGroup,children:[(0,r.jsx)("label",{htmlFor:"password",children:"Password *"}),(0,r.jsxs)("div",{className:p().passwordInput,children:[(0,r.jsx)("input",{id:"password",name:"password",type:l?"text":"password",value:e.password,onChange:k,placeholder:"Create a password (min. 8 characters)",required:!0,disabled:s,className:p().formInput,minLength:8}),(0,r.jsx)("button",{type:"button",onClick:()=>m(!l),className:p().passwordToggle,disabled:s,children:l?"\uD83D\uDC41️":"\uD83D\uDC41️‍\uD83D\uDDE8️"})]})]}),(0,r.jsxs)("div",{className:p().formGroup,children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",children:"Confirm Password *"}),(0,r.jsxs)("div",{className:p().passwordInput,children:[(0,r.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:_?"text":"password",value:e.confirmPassword,onChange:k,placeholder:"Confirm your password",required:!0,disabled:s,className:p().formInput}),(0,r.jsx)("button",{type:"button",onClick:()=>x(!_),className:p().passwordToggle,disabled:s,children:_?"\uD83D\uDC41️":"\uD83D\uDC41️‍\uD83D\uDDE8️"})]})]}),(0,r.jsx)("div",{className:p().checkboxGroup,children:(0,r.jsxs)("label",{className:p().checkboxLabel,children:[(0,r.jsx)("input",{type:"checkbox",checked:f,onChange:e=>b(e.target.checked),disabled:s,className:p().checkbox}),(0,r.jsxs)("span",{className:p().checkboxText,children:["I agree to the"," ",(0,r.jsx)(c(),{href:"/policies#terms",className:p().authLink,target:"_blank",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(c(),{href:"/policies#privacy",className:p().authLink,target:"_blank",children:"Privacy Policy"})]})]})}),(0,r.jsx)("button",{type:"submit",disabled:s,className:"".concat(p().authButton," ").concat(s?p().loading:""),children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:p().buttonSpinner}),"Creating Account..."]}):"Create Account"})]}),(0,r.jsx)("div",{className:p().authLinks,children:(0,r.jsxs)("p",{children:["Already have an account?"," ",(0,r.jsx)(c(),{href:"/login",className:p().authLink,children:"Sign In"})]})}),(0,r.jsxs)("div",{className:p().guestOption,children:[(0,r.jsx)("div",{className:p().divider,children:(0,r.jsx)("span",{children:"or"})}),(0,r.jsxs)("p",{children:["Want to book without an account?"," ",(0,r.jsx)(c(),{href:"/book-online",className:p().authLink,children:"Continue as Guest"})]})]})]}),(0,r.jsx)("div",{className:p().authFooter,children:(0,r.jsx)("p",{children:"By creating an account, you agree to receive email updates about your bookings and our services. You can unsubscribe at any time."})})]})]})}},589:function(e){e.exports={authContainer:"Auth_authContainer__krRUD",authCard:"Auth_authCard__c52sr",authHeader:"Auth_authHeader__tImyy",authForm:"Auth_authForm__Q3DLf",formRow:"Auth_formRow__cgwUK",formGroup:"Auth_formGroup__9PGF_",formInput:"Auth_formInput__GBmIX",passwordInput:"Auth_passwordInput__Z_Gdm",passwordToggle:"Auth_passwordToggle__0CEK0",checkboxGroup:"Auth_checkboxGroup__GTN4W",checkboxLabel:"Auth_checkboxLabel__jHubw",checkbox:"Auth_checkbox__CWB76",checkboxText:"Auth_checkboxText__C8ZZD",authButton:"Auth_authButton__FY9nb",loading:"Auth_loading___YK33",buttonSpinner:"Auth_buttonSpinner__OxbRv",spin:"Auth_spin__3Kb1j",authLinks:"Auth_authLinks__wOm40",authLink:"Auth_authLink__3rvVg",guestOption:"Auth_guestOption__TtmNY",divider:"Auth_divider__bZzOZ",authFooter:"Auth_authFooter__Wv0sO",loadingSpinner:"Auth_loadingSpinner__UuqwH",spinner:"Auth_spinner__fRASl"}}},function(e){e.O(0,[409,497,888,774,179],function(){return e(e.s=8588)}),_N_E=e.O()}]);