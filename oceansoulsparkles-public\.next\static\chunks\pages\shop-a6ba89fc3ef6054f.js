(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[800],{9173:function(e,r,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shop",function(){return o(8107)}])},8107:function(e,r,o){"use strict";o.r(r),o.d(r,{default:function(){return m}});var s=o(5893),c=o(7294),t=o(9008),a=o.n(t),n=o(5497);o(1664);var d=o(2920),i=o(7506),l=o.n(i);function u(e){let{product:r}=e,[o,t]=(0,c.useState)(!1),[a,n]=(0,c.useState)(!1),i=async()=>{n(!0);try{await new Promise(e=>setTimeout(e,500)),d.Am.success("".concat(r.name," added to cart!"))}catch(e){console.error("Error adding to cart:",e),d.Am.error("Failed to add item to cart")}finally{n(!1)}},u=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),p=0===r.stock_quantity?{status:"out-of-stock",text:"Out of Stock",color:"#dc3545"}:r.stock_quantity<=5?{status:"low-stock",text:"Only ".concat(r.stock_quantity," left"),color:"#fd7e14"}:{status:"in-stock",text:"In Stock",color:"#28a745"};return(0,s.jsxs)("div",{className:l().productCard,children:[(0,s.jsxs)("div",{className:l().productImageContainer,children:[!o&&r.image_url?(0,s.jsx)("img",{src:r.image_url,alt:r.name,className:l().productImage,onError:()=>{t(!0)}}):(0,s.jsx)("div",{className:l().placeholderImage,children:(0,s.jsx)("span",{className:l().placeholderIcon,children:"\uD83C\uDFA8"})}),(0,s.jsx)("div",{className:"".concat(l().stockBadge," ").concat(l()[p.status]),style:{backgroundColor:p.color},children:p.text}),(0,s.jsx)("div",{className:l().productOverlay,children:(0,s.jsx)("button",{onClick:()=>{d.Am.info("Quick view feature coming soon!")},className:l().quickViewButton,"aria-label":"Quick view",children:"\uD83D\uDC41️"})})]}),(0,s.jsxs)("div",{className:l().productContent,children:[(0,s.jsxs)("div",{className:l().productHeader,children:[(0,s.jsx)("h3",{className:l().productName,children:r.name}),r.category_name&&(0,s.jsx)("span",{className:l().productCategory,children:r.category_name})]}),(0,s.jsx)("p",{className:l().productDescription,children:r.description}),r.features&&r.features.length>0&&(0,s.jsx)("div",{className:l().productFeatures,children:(0,s.jsx)("ul",{children:r.features.slice(0,2).map((e,r)=>(0,s.jsx)("li",{children:e},r))})}),r.ingredients&&(0,s.jsxs)("div",{className:l().productIngredients,children:[(0,s.jsx)("span",{className:l().ingredientsLabel,children:"Made with:"}),(0,s.jsx)("span",{className:l().ingredientsText,children:r.ingredients})]}),(0,s.jsxs)("div",{className:l().productFooter,children:[(0,s.jsxs)("div",{className:l().priceContainer,children:[(0,s.jsx)("span",{className:l().price,children:u(r.price)}),r.original_price&&r.original_price>r.price&&(0,s.jsx)("span",{className:l().originalPrice,children:u(r.original_price)})]}),(0,s.jsx)("div",{className:l().productActions,children:r.stock_quantity>0?(0,s.jsx)("button",{onClick:i,disabled:a,className:l().addToCartButton,children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:l().buttonSpinner}),"Adding..."]}):"Add to Cart"}):(0,s.jsx)("button",{disabled:!0,className:l().outOfStockButton,children:"Out of Stock"})})]}),r.is_eco_friendly&&(0,s.jsxs)("div",{className:l().ecoBadge,children:[(0,s.jsx)("span",{className:l().ecoIcon,children:"\uD83C\uDF3F"}),(0,s.jsx)("span",{children:"Eco-Friendly"})]})]})]})}var p=o(5531),_=o(5692),h=o.n(_);function m(){let[e,r]=(0,c.useState)([]),[o,t]=(0,c.useState)(!0),[i,l]=(0,c.useState)("all"),[_,m]=(0,c.useState)("");(0,c.useEffect)(()=>{g()},[i]);let g=async()=>{try{t(!0);let{data:e,error:o}=await p.v8.getProducts(i);if(o){console.error("Error fetching products:",o),d.Am.error("Unable to load products. Please try again.");return}r(e||[])}catch(e){console.error("Error fetching products:",e),d.Am.error("Unable to load products. Please try again.")}finally{t(!1)}},x=["all",...new Set(e.map(e=>e.category_name))],j=e.filter(e=>e.name.toLowerCase().includes(_.toLowerCase())||e.description.toLowerCase().includes(_.toLowerCase()));return(0,s.jsxs)(n.Z,{children:[(0,s.jsxs)(a(),{children:[(0,s.jsx)("title",{children:"Shop - Ocean Soul Sparkles"}),(0,s.jsx)("meta",{name:"description",content:"Shop eco-friendly glitter, face paints, and beauty products from Ocean Soul Sparkles. Biodegradable glitter made from eucalyptus trees."}),(0,s.jsx)("meta",{name:"keywords",content:"eco-friendly glitter, biodegradable glitter, face paint, beauty products, sustainable makeup, Melbourne"})]}),(0,s.jsxs)("div",{className:h().shopContainer,children:[(0,s.jsxs)("div",{className:h().shopHeader,children:[(0,s.jsx)("h1",{children:"Eco-Friendly Beauty Shop"}),(0,s.jsx)("p",{children:"Discover our range of sustainable, biodegradable beauty products"})]}),(0,s.jsxs)("div",{className:h().shopControls,children:[(0,s.jsxs)("div",{className:h().searchContainer,children:[(0,s.jsx)("input",{type:"text",placeholder:"Search products...",value:_,onChange:e=>m(e.target.value),className:h().searchInput}),(0,s.jsxs)("svg",{className:h().searchIcon,xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("circle",{cx:"11",cy:"11",r:"8"}),(0,s.jsx)("path",{d:"m21 21-4.35-4.35"})]})]}),x.length>1&&(0,s.jsx)("div",{className:h().categoryFilter,children:(0,s.jsx)("select",{value:i,onChange:e=>l(e.target.value),className:h().categorySelect,children:x.map(e=>(0,s.jsx)("option",{value:e,children:"all"===e?"All Categories":e},e))})})]}),(0,s.jsx)("div",{className:h().productsSection,children:o?(0,s.jsxs)("div",{className:h().loadingContainer,children:[(0,s.jsx)("div",{className:h().loadingSpinner}),(0,s.jsx)("p",{children:"Loading products..."})]}):j.length>0?(0,s.jsx)("div",{className:h().productsGrid,children:j.map(e=>(0,s.jsx)(u,{product:e},e.id))}):(0,s.jsxs)("div",{className:h().noProducts,children:[(0,s.jsx)("div",{className:h().noProductsIcon,children:"\uD83D\uDECD️"}),(0,s.jsx)("h3",{children:"No products found"}),(0,s.jsx)("p",{children:_?'No products match "'.concat(_,'"'):"all"===i?"No products are currently available.":'No products found in the "'.concat(i,'" category.')}),(_||"all"!==i)&&(0,s.jsx)("button",{onClick:()=>{m(""),l("all")},className:h().clearFiltersButton,children:"Clear Filters"})]})}),(0,s.jsx)("div",{className:h().ecoInfo,children:(0,s.jsxs)("div",{className:h().ecoInfoGrid,children:[(0,s.jsxs)("div",{className:h().ecoInfoCard,children:[(0,s.jsx)("div",{className:h().ecoIcon,children:"\uD83C\uDF3F"}),(0,s.jsx)("h3",{children:"100% Biodegradable"}),(0,s.jsx)("p",{children:"Our glitter is made from eucalyptus trees and breaks down naturally in just 6 weeks in soil."})]}),(0,s.jsxs)("div",{className:h().ecoInfoCard,children:[(0,s.jsx)("div",{className:h().ecoIcon,children:"\uD83D\uDC30"}),(0,s.jsx)("h3",{children:"Cruelty-Free & Vegan"}),(0,s.jsx)("p",{children:"All our products are never tested on animals and contain no animal-derived ingredients."})]}),(0,s.jsxs)("div",{className:h().ecoInfoCard,children:[(0,s.jsx)("div",{className:h().ecoIcon,children:"\uD83C\uDF0A"}),(0,s.jsx)("h3",{children:"Ocean-Safe"}),(0,s.jsx)("p",{children:"Our biodegradable products won't harm marine life or pollute waterways."})]}),(0,s.jsxs)("div",{className:h().ecoInfoCard,children:[(0,s.jsx)("div",{className:h().ecoIcon,children:"✨"}),(0,s.jsx)("h3",{children:"Professional Quality"}),(0,s.jsx)("p",{children:"Used by professional artists worldwide for stunning, long-lasting results."})]})]})}),(0,s.jsxs)("div",{className:h().shippingInfo,children:[(0,s.jsx)("h3",{children:"Shipping & Returns"}),(0,s.jsxs)("div",{className:h().shippingGrid,children:[(0,s.jsxs)("div",{className:h().shippingCard,children:[(0,s.jsx)("h4",{children:"\uD83D\uDE9A Fast Shipping"}),(0,s.jsx)("p",{children:"Free shipping on orders over $50. Express delivery available."})]}),(0,s.jsxs)("div",{className:h().shippingCard,children:[(0,s.jsx)("h4",{children:"↩️ Easy Returns"}),(0,s.jsx)("p",{children:"30-day return policy on unopened products. Customer satisfaction guaranteed."})]}),(0,s.jsxs)("div",{className:h().shippingCard,children:[(0,s.jsx)("h4",{children:"\uD83D\uDCB3 Secure Payments"}),(0,s.jsx)("p",{children:"All payments processed securely through Square. Major credit cards accepted."})]})]})]}),(0,s.jsxs)("div",{className:h().customOrders,children:[(0,s.jsx)("h3",{children:"Need Something Special?"}),(0,s.jsx)("p",{children:"Looking for custom colors or bulk orders? We'd love to help create something unique for you!"}),(0,s.jsxs)("div",{className:h().contactActions,children:[(0,s.jsx)("a",{href:"/contact",className:"button",children:"Contact Us"}),(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"button button--outline",children:"Email Us"})]})]})]})]})}},7506:function(e){e.exports={productCard:"ProductCard_productCard__WGmTM",productImageContainer:"ProductCard_productImageContainer__Ig_pO",productImage:"ProductCard_productImage__RSK1Q",placeholderImage:"ProductCard_placeholderImage__Q58sa",placeholderIcon:"ProductCard_placeholderIcon__ucVOn",stockBadge:"ProductCard_stockBadge__SgPRa",inStock:"ProductCard_inStock__pYgzF",lowStock:"ProductCard_lowStock__cLdxq",outOfStock:"ProductCard_outOfStock__VAbY2",productOverlay:"ProductCard_productOverlay__NQGkB",quickViewButton:"ProductCard_quickViewButton__ameRZ",productContent:"ProductCard_productContent__eR9ib",productHeader:"ProductCard_productHeader__kkZus",productName:"ProductCard_productName__dIl_5",productCategory:"ProductCard_productCategory__cpkzU",productDescription:"ProductCard_productDescription__Mk7Aa",productFeatures:"ProductCard_productFeatures__3AtO6",productIngredients:"ProductCard_productIngredients__MVAMq",ingredientsLabel:"ProductCard_ingredientsLabel__P7Igi",ingredientsText:"ProductCard_ingredientsText__gJD5J",productFooter:"ProductCard_productFooter__nNTpN",priceContainer:"ProductCard_priceContainer__7jIsN",price:"ProductCard_price__s3DmM",originalPrice:"ProductCard_originalPrice__t2iHf",productActions:"ProductCard_productActions__MhGSV",addToCartButton:"ProductCard_addToCartButton__OhWBn",outOfStockButton:"ProductCard_outOfStockButton__aUAWt",buttonSpinner:"ProductCard_buttonSpinner__BJVqF",spin:"ProductCard_spin__wwma7",ecoBadge:"ProductCard_ecoBadge__UKSQO",ecoIcon:"ProductCard_ecoIcon__nrb3l"}},5692:function(e){e.exports={shopContainer:"Shop_shopContainer__eDw1e",shopHeader:"Shop_shopHeader__8R236",shopControls:"Shop_shopControls__alTKZ",searchContainer:"Shop_searchContainer__fZIPe",searchInput:"Shop_searchInput__mptT6",searchIcon:"Shop_searchIcon__S6sG4",categoryFilter:"Shop_categoryFilter__9fx29",categorySelect:"Shop_categorySelect__SN_qC",productsSection:"Shop_productsSection__DUFcj",productsGrid:"Shop_productsGrid__0DYmk",loadingContainer:"Shop_loadingContainer__xQAt_",loadingSpinner:"Shop_loadingSpinner__UN0Ba",spin:"Shop_spin__VUCFI",noProducts:"Shop_noProducts__hvH_j",noProductsIcon:"Shop_noProductsIcon__1cMk8",clearFiltersButton:"Shop_clearFiltersButton__poJyb",ecoInfo:"Shop_ecoInfo__eHiMN",ecoInfoGrid:"Shop_ecoInfoGrid__Xyn7q",ecoInfoCard:"Shop_ecoInfoCard__itAic",ecoIcon:"Shop_ecoIcon__24OAt",shippingInfo:"Shop_shippingInfo___us5Y",shippingGrid:"Shop_shippingGrid__J31OI",shippingCard:"Shop_shippingCard__E9Un8",customOrders:"Shop_customOrders__9M_KV",contactActions:"Shop_contactActions__RUB63",button:"Shop_button__RkdNu"}}},function(e){e.O(0,[409,497,888,774,179],function(){return e(e.s=9173)}),_N_E=e.O()}]);