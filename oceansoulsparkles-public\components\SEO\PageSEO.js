import Head from 'next/head';
import { useRouter } from 'next/router';

export default function PageSEO({
  title,
  description,
  keywords,
  ogImage,
  ogType = 'website',
  twitterCard = 'summary_large_image',
  canonical,
  noindex = false,
  nofollow = false,
  structuredData
}) {
  const router = useRouter();
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.oceansoulsparkles.com.au';
  
  // Generate canonical URL
  const canonicalUrl = canonical || `${siteUrl}${router.asPath}`;
  
  // Default OG image
  const defaultOgImage = `${siteUrl}/images/og-default.jpg`;
  const ogImageUrl = ogImage || defaultOgImage;
  
  // Generate robots content
  const robotsContent = [];
  if (noindex) robotsContent.push('noindex');
  if (nofollow) robotsContent.push('nofollow');
  const robots = robotsContent.length > 0 ? robotsContent.join(', ') : 'index, follow';

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      <meta name="robots" content={robots} />
      <link rel="canonical" href={canonicalUrl} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content={ogImageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Ocean Soul Sparkles" />
      <meta property="og:locale" content="en_AU" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImageUrl} />
      <meta name="twitter:site" content="@oceansoulsparkles" />
      <meta name="twitter:creator" content="@oceansoulsparkles" />

      {/* Additional Meta Tags */}
      <meta name="author" content="Ocean Soul Sparkles" />
      <meta name="copyright" content="Ocean Soul Sparkles" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="web" />
      <meta name="rating" content="general" />

      {/* Geographic Meta Tags */}
      <meta name="geo.region" content="AU-VIC" />
      <meta name="geo.placename" content="Melbourne" />
      <meta name="geo.position" content="-37.8136;144.9631" />
      <meta name="ICBM" content="-37.8136, 144.9631" />

      {/* Business Meta Tags */}
      <meta name="business:contact_data:street_address" content="Melbourne" />
      <meta name="business:contact_data:locality" content="Melbourne" />
      <meta name="business:contact_data:region" content="Victoria" />
      <meta name="business:contact_data:postal_code" content="3000" />
      <meta name="business:contact_data:country_name" content="Australia" />
      <meta name="business:contact_data:email" content="<EMAIL>" />

      {/* Mobile Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
      <meta name="format-detection" content="telephone=yes" />
      <meta name="format-detection" content="address=yes" />

      {/* Apple Meta Tags */}
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Ocean Soul Sparkles" />

      {/* Microsoft Meta Tags */}
      <meta name="msapplication-TileColor" content="#3788d8" />
      <meta name="msapplication-config" content="/browserconfig.xml" />

      {/* Theme Color */}
      <meta name="theme-color" content="#3788d8" />
      <meta name="msapplication-navbutton-color" content="#3788d8" />
      <meta name="apple-mobile-web-app-status-bar-style" content="#3788d8" />

      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://ndlgbcsbidyhxbpqzgqp.supabase.co" />

      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="https://js.squareup.com" />
      <link rel="dns-prefetch" href="https://cdn.onesignal.com" />

      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}

      {/* Alternate Languages (if applicable) */}
      <link rel="alternate" hrefLang="en-au" href={canonicalUrl} />
      <link rel="alternate" hrefLang="en" href={canonicalUrl} />
      <link rel="alternate" hrefLang="x-default" href={canonicalUrl} />
    </Head>
  );
}
