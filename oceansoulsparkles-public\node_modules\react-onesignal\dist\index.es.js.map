{"version": 3, "file": "index.es.js", "sources": ["../index.ts"], "sourcesContent": ["const ONESIGNAL_SDK_ID = 'onesignal-sdk';\nconst ONE_SIGNAL_SCRIPT_SRC = 'https://cdn.onesignal.com/sdks/OneSignalSDK.js';\nconst reactOneSignalFunctionQueue = [];\n\n// true if the script is successfully loaded from CDN.\nlet isOneSignalInitialized = false;\n// true if the script fails to load from CDN. A separate flag is necessary\n// to disambiguate between a CDN load failure and a delayed call to\n// OneSignal#init.\nlet isOneSignalScriptFailed = false;\n\nconst doesOneSignalExist = () => {\n  if (window[\"OneSignal\"]) {\n    return true;\n  }\n  return false;\n}\n\nconst handleOnLoad = (resolve: () => void, options: IInitObject) => {\n  isOneSignalInitialized = true;\n\n  // OneSignal is assumed to be loaded correctly because this method\n  // is called after the script is successfully loaded by CDN, but\n  // just in case.\n  window[\"OneSignal\"] = window[\"OneSignal\"] || []\n\n  window[\"OneSignal\"].push(() => {\n    window[\"OneSignal\"].init(options);\n  });\n\n  window[\"OneSignal\"].push(() => {\n    processQueuedOneSignalFunctions();\n    resolve();\n  });\n}\n\nconst handleOnError = (resolve: () => void) => {\n  isOneSignalScriptFailed = true;\n  // Ensure that any unresolved functions are cleared from the queue,\n  // even in the event of a CDN load failure.\n  processQueuedOneSignalFunctions();\n  resolve();\n}\n\nconst processQueuedOneSignalFunctions = () => {\n  reactOneSignalFunctionQueue.forEach(element => {\n    const { name, args, promiseResolver } = element;\n\n    if (!!promiseResolver) {\n      OneSignalReact[name](...args).then(result => {\n        promiseResolver(result);\n      });\n    } else {\n      OneSignalReact[name](...args);\n    }\n  });\n}\n\nconst init = (options: IInitObject) => new Promise<void>(resolve => {\n  if (isOneSignalInitialized) {\n    resolve();\n    return;\n  }\n\n  if (!options || !options.appId) {\n    throw new Error('You need to provide your OneSignal appId.');\n  }\n  if (!document) {\n    resolve();\n    return;\n  }\n\n  const script = document.createElement('script');\n  script.id = ONESIGNAL_SDK_ID;\n  script.src = ONE_SIGNAL_SCRIPT_SRC;\n  script.async = true;\n\n  script.onload = () => {\n    handleOnLoad(resolve, options);\n  };\n\n  // Always resolve whether or not the script is successfully initialized.\n  // This is important for users who may block cdn.onesignal.com w/ adblock.\n  script.onerror = () => {\n    handleOnError(resolve);\n  }\n\n  document.head.appendChild(script);\n});\n\ntype Action<T> = (item: T) => void;\ninterface AutoPromptOptions { force?: boolean; forceSlidedownOverNative?: boolean; slidedownPromptOptions?: IOneSignalAutoPromptOptions; }\ninterface RegisterOptions { modalPrompt?: boolean; httpPermissionRequest?: boolean; slidedown?: boolean; autoAccept?: boolean }\ninterface SetSMSOptions { identifierAuthHash?: string; }\ninterface SetEmailOptions { identifierAuthHash?: string; emailAuthHash?: string; }\ninterface TagsObject<T> { [key: string]: T; }\ninterface IOneSignalAutoPromptOptions { force?: boolean; forceSlidedownOverNative?: boolean; isInUpdateMode?: boolean; categoryOptions?: IOneSignalCategories; }\ninterface IOneSignalCategories { positiveUpdateButton: string; negativeUpdateButton: string; savingButtonText: string; errorButtonText: string; updateMessage: string; tags: IOneSignalTagCategory[]; }\ninterface IOneSignalTagCategory { tag: string; label: string; checked?: boolean; }\n\ninterface IInitObject {\n  appId: string;\n  subdomainName?: string;\n  requiresUserPrivacyConsent?: boolean;\n  promptOptions?: object;\n  welcomeNotification?: object;\n  notifyButton?: object;\n  persistNotification?: boolean;\n  webhooks?: object;\n  autoResubscribe?: boolean;\n  autoRegister?: boolean;\n  notificationClickHandlerMatch?: string;\n  notificationClickHandlerAction?: string;\n  serviceWorkerParam?: { scope: string };\n  serviceWorkerPath?: string;\n  serviceWorkerUpdaterPath?: string;\n  path?: string;\n  allowLocalhostAsSecureOrigin?: boolean;\n  [key: string]: any;\n}\n\ninterface IOneSignal {\n\tinit(options: IInitObject): Promise<void>\n\ton(event: string, listener: (eventData?: any) => void): void\n\toff(event: string, listener: (eventData?: any) => void): void\n\tonce(event: string, listener: (eventData?: any) => void): void\n\tisPushNotificationsEnabled(callback?: Action<boolean>): Promise<boolean>\n\tshowHttpPrompt(options?: AutoPromptOptions): Promise<void>\n\tregisterForPushNotifications(options?: RegisterOptions): Promise<void>\n\tsetDefaultNotificationUrl(url: string): Promise<void>\n\tsetDefaultTitle(title: string): Promise<void>\n\tgetTags(callback?: Action<any>): Promise<void>\n\tsendTag(key: string, value: any, callback?: Action<Object>): Promise<Object | null>\n\tsendTags(tags: TagsObject<any>, callback?: Action<Object>): Promise<Object | null>\n\tdeleteTag(tag: string): Promise<Array<string>>\n\tdeleteTags(tags: Array<string>, callback?: Action<Array<string>>): Promise<Array<string>>\n\taddListenerForNotificationOpened(callback?: Action<Notification>): Promise<void>\n\tsetSubscription(newSubscription: boolean): Promise<void>\n\tshowHttpPermissionRequest(options?: AutoPromptOptions): Promise<any>\n\tshowNativePrompt(): Promise<void>\n\tshowSlidedownPrompt(options?: AutoPromptOptions): Promise<void>\n\tshowCategorySlidedown(options?: AutoPromptOptions): Promise<void>\n\tshowSmsSlidedown(options?: AutoPromptOptions): Promise<void>\n\tshowEmailSlidedown(options?: AutoPromptOptions): Promise<void>\n\tshowSmsAndEmailSlidedown(options?: AutoPromptOptions): Promise<void>\n\tgetNotificationPermission(onComplete?: Action<NotificationPermission>): Promise<NotificationPermission>\n\tgetUserId(callback?: Action<string | undefined | null>): Promise<string | undefined | null>\n\tgetSubscription(callback?: Action<boolean>): Promise<boolean>\n\tsetEmail(email: string, options?: SetEmailOptions): Promise<string|null>\n\tsetSMSNumber(smsNumber: string, options?: SetSMSOptions): Promise<string | null>\n\tlogoutEmail(): Promise<void>\n\tlogoutSMS(): Promise<void>\n\tsetExternalUserId(externalUserId: string | undefined | null, authHash?: string): Promise<void>\n\tremoveExternalUserId(): Promise<void>\n\tgetExternalUserId(): Promise<string | undefined | null>\n\tprovideUserConsent(consent: boolean): Promise<void>\n\tgetEmailId(callback?: Action<string | undefined>): Promise<string | null | undefined>\n\tgetSMSId(callback?: Action<string | undefined>): Promise<string | null | undefined>\n\tsendOutcome(outcomeName: string, outcomeWeight?: number | undefined): Promise<void>\n\t[index: string]: Function;\n}\n\n\n\n  function on(event: string, listener: (eventData?: any) => void): void {\n    if (!doesOneSignalExist()) {\n      reactOneSignalFunctionQueue.push({\n        name: 'on',\n        args: arguments,\n      });\n      return;\n    }\n\n    window[\"OneSignal\"].push(() => {\n      window[\"OneSignal\"].on(event, listener)\n    });\n  }\n\n  function off(event: string, listener: (eventData?: any) => void): void {\n    if (!doesOneSignalExist()) {\n      reactOneSignalFunctionQueue.push({\n        name: 'off',\n        args: arguments,\n      });\n      return;\n    }\n\n    window[\"OneSignal\"].push(() => {\n      window[\"OneSignal\"].off(event, listener)\n    });\n  }\n\n  function once(event: string, listener: (eventData?: any) => void): void {\n    if (!doesOneSignalExist()) {\n      reactOneSignalFunctionQueue.push({\n        name: 'once',\n        args: arguments,\n      });\n      return;\n    }\n\n    window[\"OneSignal\"].push(() => {\n      window[\"OneSignal\"].once(event, listener)\n    });\n  }\n\n  function isPushNotificationsEnabled(callback?: Action<boolean>): Promise<boolean> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'isPushNotificationsEnabled',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].isPushNotificationsEnabled(callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function showHttpPrompt(options?: AutoPromptOptions): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'showHttpPrompt',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].showHttpPrompt(options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function registerForPushNotifications(options?: RegisterOptions): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'registerForPushNotifications',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].registerForPushNotifications(options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function setDefaultNotificationUrl(url: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'setDefaultNotificationUrl',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].setDefaultNotificationUrl(url)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function setDefaultTitle(title: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'setDefaultTitle',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].setDefaultTitle(title)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function getTags(callback?: Action<any>): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'getTags',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].getTags(callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function sendTag(key: string, value: any, callback?: Action<Object>): Promise<Object | null> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'sendTag',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].sendTag(key, value, callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function sendTags(tags: TagsObject<any>, callback?: Action<Object>): Promise<Object | null> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'sendTags',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].sendTags(tags, callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function deleteTag(tag: string): Promise<Array<string>> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'deleteTag',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].deleteTag(tag)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function deleteTags(tags: Array<string>, callback?: Action<Array<string>>): Promise<Array<string>> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'deleteTags',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].deleteTags(tags, callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function addListenerForNotificationOpened(callback?: Action<Notification>): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'addListenerForNotificationOpened',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].addListenerForNotificationOpened(callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function setSubscription(newSubscription: boolean): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'setSubscription',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].setSubscription(newSubscription)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function showHttpPermissionRequest(options?: AutoPromptOptions): Promise<any> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'showHttpPermissionRequest',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].showHttpPermissionRequest(options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function showNativePrompt(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'showNativePrompt',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].showNativePrompt()\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function showSlidedownPrompt(options?: AutoPromptOptions): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'showSlidedownPrompt',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].showSlidedownPrompt(options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function showCategorySlidedown(options?: AutoPromptOptions): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'showCategorySlidedown',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].showCategorySlidedown(options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function showSmsSlidedown(options?: AutoPromptOptions): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'showSmsSlidedown',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].showSmsSlidedown(options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function showEmailSlidedown(options?: AutoPromptOptions): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'showEmailSlidedown',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].showEmailSlidedown(options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function showSmsAndEmailSlidedown(options?: AutoPromptOptions): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'showSmsAndEmailSlidedown',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].showSmsAndEmailSlidedown(options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function getNotificationPermission(onComplete?: Action<NotificationPermission>): Promise<NotificationPermission> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'getNotificationPermission',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].getNotificationPermission(onComplete)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function getUserId(callback?: Action<string | undefined | null>): Promise<string | undefined | null> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'getUserId',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].getUserId(callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function getSubscription(callback?: Action<boolean>): Promise<boolean> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'getSubscription',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].getSubscription(callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function setEmail(email: string, options?: SetEmailOptions): Promise<string|null> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'setEmail',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].setEmail(email, options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function setSMSNumber(smsNumber: string, options?: SetSMSOptions): Promise<string | null> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'setSMSNumber',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].setSMSNumber(smsNumber, options)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function logoutEmail(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'logoutEmail',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].logoutEmail()\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function logoutSMS(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'logoutSMS',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].logoutSMS()\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function setExternalUserId(externalUserId: string | undefined | null, authHash?: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'setExternalUserId',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].setExternalUserId(externalUserId, authHash)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function removeExternalUserId(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'removeExternalUserId',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].removeExternalUserId()\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function getExternalUserId(): Promise<string | undefined | null> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'getExternalUserId',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].getExternalUserId()\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function provideUserConsent(consent: boolean): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'provideUserConsent',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].provideUserConsent(consent)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function getEmailId(callback?: Action<string | undefined>): Promise<string | null | undefined> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'getEmailId',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].getEmailId(callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function getSMSId(callback?: Action<string | undefined>): Promise<string | null | undefined> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'getSMSId',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].getSMSId(callback)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  function sendOutcome(outcomeName: string, outcomeWeight?: number | undefined): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (isOneSignalScriptFailed) {\n        resolve();\n        return;\n      }\n\n      if (!doesOneSignalExist()) {\n        reactOneSignalFunctionQueue.push({\n          name: 'sendOutcome',\n          args: arguments,\n          promiseResolver: resolve,\n        });\n        return;\n      }\n\n      try {\n        window[\"OneSignal\"].push(() => {\n          window[\"OneSignal\"].sendOutcome(outcomeName, outcomeWeight)\n            .then((value: any) => resolve(value))\n            .catch((error: any) => reject(error));\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\nconst OneSignalReact: IOneSignal = {\n\tinit,\n\ton,\n\toff,\n\tonce,\n\tisPushNotificationsEnabled,\n\tshowHttpPrompt,\n\tregisterForPushNotifications,\n\tsetDefaultNotificationUrl,\n\tsetDefaultTitle,\n\tgetTags,\n\tsendTag,\n\tsendTags,\n\tdeleteTag,\n\tdeleteTags,\n\taddListenerForNotificationOpened,\n\tsetSubscription,\n\tshowHttpPermissionRequest,\n\tshowNativePrompt,\n\tshowSlidedownPrompt,\n\tshowCategorySlidedown,\n\tshowSmsSlidedown,\n\tshowEmailSlidedown,\n\tshowSmsAndEmailSlidedown,\n\tgetNotificationPermission,\n\tgetUserId,\n\tgetSubscription,\n\tsetEmail,\n\tsetSMSNumber,\n\tlogoutEmail,\n\tlogoutSMS,\n\tsetExternalUserId,\n\tremoveExternalUserId,\n\tgetExternalUserId,\n\tprovideUserConsent,\n\tgetEmailId,\n\tgetSMSId,\n\tsendOutcome,\n};\nexport default OneSignalReact\n"], "names": [], "mappings": "AAAA,MAAM,gBAAgB,GAAG,eAAe,CAAC;AACzC,MAAM,qBAAqB,GAAG,gDAAgD,CAAC;AAC/E,MAAM,2BAA2B,GAAG,EAAE,CAAC;AAEvC;AACA,IAAI,sBAAsB,GAAG,KAAK,CAAC;AACnC;AACA;AACA;AACA,IAAI,uBAAuB,GAAG,KAAK,CAAC;AAEpC,MAAM,kBAAkB,GAAG;IACzB,IAAI,MAAM,CAAC,WAAW,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,CAAC,OAAmB,EAAE,OAAoB;IAC7D,sBAAsB,GAAG,IAAI,CAAC;;;;IAK9B,MAAM,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAA;IAE/C,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;QACvB,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACnC,CAAC,CAAC;IAEH,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;QACvB,+BAA+B,EAAE,CAAC;QAClC,OAAO,EAAE,CAAC;KACX,CAAC,CAAC;AACL,CAAC,CAAA;AAED,MAAM,aAAa,GAAG,CAAC,OAAmB;IACxC,uBAAuB,GAAG,IAAI,CAAC;;;IAG/B,+BAA+B,EAAE,CAAC;IAClC,OAAO,EAAE,CAAC;AACZ,CAAC,CAAA;AAED,MAAM,+BAA+B,GAAG;IACtC,2BAA2B,CAAC,OAAO,CAAC,OAAO;QACzC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;QAEhD,IAAI,CAAC,CAAC,eAAe,EAAE;YACrB,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM;gBACvC,eAAe,CAAC,MAAM,CAAC,CAAC;aACzB,CAAC,CAAC;SACJ;aAAM;YACL,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;SAC/B;KACF,CAAC,CAAC;AACL,CAAC,CAAA;AAED,MAAM,IAAI,GAAG,CAAC,OAAoB,KAAK,IAAI,OAAO,CAAO,OAAO;IAC9D,IAAI,sBAAsB,EAAE;QAC1B,OAAO,EAAE,CAAC;QACV,OAAO;KACR;IAED,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IACD,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,EAAE,CAAC;QACV,OAAO;KACR;IAED,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAChD,MAAM,CAAC,EAAE,GAAG,gBAAgB,CAAC;IAC7B,MAAM,CAAC,GAAG,GAAG,qBAAqB,CAAC;IACnC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;IAEpB,MAAM,CAAC,MAAM,GAAG;QACd,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAChC,CAAC;;;IAIF,MAAM,CAAC,OAAO,GAAG;QACf,aAAa,CAAC,OAAO,CAAC,CAAC;KACxB,CAAA;IAED,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC;AA4ED,SAAS,EAAE,CAAC,KAAa,EAAE,QAAmC;IAC5D,IAAI,CAAC,kBAAkB,EAAE,EAAE;QACzB,2BAA2B,CAAC,IAAI,CAAC;YAC/B,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;QACH,OAAO;KACR;IAED,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;QACvB,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;KACxC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,GAAG,CAAC,KAAa,EAAE,QAAmC;IAC7D,IAAI,CAAC,kBAAkB,EAAE,EAAE;QACzB,2BAA2B,CAAC,IAAI,CAAC;YAC/B,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;QACH,OAAO;KACR;IAED,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;QACvB,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;KACzC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,IAAI,CAAC,KAAa,EAAE,QAAmC;IAC9D,IAAI,CAAC,kBAAkB,EAAE,EAAE;QACzB,2BAA2B,CAAC,IAAI,CAAC;YAC/B,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;QACH,OAAO;KACR;IAED,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;QACvB,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;KAC1C,CAAC,CAAC;AACL,CAAC;AAED,SAAS,0BAA0B,CAAC,QAA0B;IAC5D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,4BAA4B;gBAClC,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,0BAA0B,CAAC,QAAQ,CAAC;qBACrD,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,cAAc,CAAC,OAA2B;IACjD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC;qBACxC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,4BAA4B,CAAC,OAAyB;IAC7D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,8BAA8B;gBACpC,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,4BAA4B,CAAC,OAAO,CAAC;qBACtD,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,yBAAyB,CAAC,GAAW;IAC5C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,2BAA2B;gBACjC,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,yBAAyB,CAAC,GAAG,CAAC;qBAC/C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe,CAAC,KAAa;IACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC;qBACvC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,OAAO,CAAC,QAAsB;IACrC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;qBAClC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,OAAO,CAAC,GAAW,EAAE,KAAU,EAAE,QAAyB;IACjE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC;qBAC9C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,QAAQ,CAAC,IAAqB,EAAE,QAAyB;IAChE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC;qBACzC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,SAAS,CAAC,GAAW;IAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC;qBAC/B,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,UAAU,CAAC,IAAmB,EAAE,QAAgC;IACvE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC;qBAC3C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,gCAAgC,CAAC,QAA+B;IACvE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,kCAAkC;gBACxC,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,gCAAgC,CAAC,QAAQ,CAAC;qBAC3D,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe,CAAC,eAAwB;IAC/C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC;qBACjD,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,yBAAyB,CAAC,OAA2B;IAC5D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,2BAA2B;gBACjC,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,yBAAyB,CAAC,OAAO,CAAC;qBACnD,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,gBAAgB;IACvB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,gBAAgB,EAAE;qBACnC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,OAA2B;IACtD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC;qBAC7C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,qBAAqB,CAAC,OAA2B;IACxD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,uBAAuB;gBAC7B,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,qBAAqB,CAAC,OAAO,CAAC;qBAC/C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,gBAAgB,CAAC,OAA2B;IACnD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC;qBAC1C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,OAA2B;IACrD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;qBAC5C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,wBAAwB,CAAC,OAA2B;IAC3D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,0BAA0B;gBAChC,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,wBAAwB,CAAC,OAAO,CAAC;qBAClD,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,yBAAyB,CAAC,UAA2C;IAC5E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,2BAA2B;gBACjC,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,yBAAyB,CAAC,UAAU,CAAC;qBACtD,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,SAAS,CAAC,QAA4C;IAC7D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC;qBACpC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe,CAAC,QAA0B;IACjD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC;qBAC1C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,QAAQ,CAAC,KAAa,EAAE,OAAyB;IACxD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC;qBACzC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,YAAY,CAAC,SAAiB,EAAE,OAAuB;IAC9D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC;qBACjD,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,WAAW;IAClB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE;qBAC9B,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,SAAS;IAChB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE;qBAC5B,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,cAAyC,EAAE,QAAiB;IACrF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,iBAAiB,CAAC,cAAc,EAAE,QAAQ,CAAC;qBAC5D,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,oBAAoB;IAC3B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,oBAAoB,EAAE;qBACvC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB;IACxB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,iBAAiB,EAAE;qBACpC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAgB;IAC1C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;qBAC5C,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,UAAU,CAAC,QAAqC;IACvD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC;qBACrC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,QAAQ,CAAC,QAAqC;IACrD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;qBACnC,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,WAAW,CAAC,WAAmB,EAAE,aAAkC;IAC1E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM;QACjC,IAAI,uBAAuB,EAAE;YAC3B,OAAO,EAAE,CAAC;YACV,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACzB,2BAA2B,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,SAAS;gBACf,eAAe,EAAE,OAAO;aACzB,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE,aAAa,CAAC;qBACxD,IAAI,CAAC,CAAC,KAAU,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;qBACpC,KAAK,CAAC,CAAC,KAAU,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;KACF,CAAC,CAAC;AACL,CAAC;AAEH,MAAM,cAAc,GAAe;IAClC,IAAI;IACJ,EAAE;IACF,GAAG;IACH,IAAI;IACJ,0BAA0B;IAC1B,cAAc;IACd,4BAA4B;IAC5B,yBAAyB;IACzB,eAAe;IACf,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,gCAAgC;IAChC,eAAe;IACf,yBAAyB;IACzB,gBAAgB;IAChB,mBAAmB;IACnB,qBAAqB;IACrB,gBAAgB;IAChB,kBAAkB;IAClB,wBAAwB;IACxB,yBAAyB;IACzB,SAAS;IACT,eAAe;IACf,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,SAAS;IACT,iBAAiB;IACjB,oBAAoB;IACpB,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,QAAQ;IACR,WAAW;CACX;;;;"}