"use strict";(()=>{var e={};e.id=506,e.ids=[506],e.modules={145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6249:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},6756:(e,t,r)=>{r.r(t),r.d(t,{config:()=>u,default:()=>l,routeModule:()=>d});var a={};r.r(a),r.d(a,{default:()=>n});var i=r(1802),c=r(7153),s=r(6249),o=r(395);async function n(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});let{category:r,featured:a}=e.query;try{console.log("[Public API] Fetching products for public shop");let{data:e,error:i}=await o.v8.getProducts(r);if(i)return console.error("[Public API] Error fetching products:",i),console.log("[Public API] Using fallback products data"),t.status(200).json({products:[{id:"fallback-1",name:"Biodegradable Glitter Set",description:"Eco-friendly glitter made from eucalyptus trees",price:25,category_name:"Glitter",status:"active",image_url:"/images/products/biodegradable-glitter.jpg",stock_quantity:50},{id:"fallback-2",name:"Face Paint Kit",description:"Professional face paint kit with brushes",price:45,category_name:"Face Paint",status:"active",image_url:"/images/products/face-paint-kit.jpg",stock_quantity:25},{id:"fallback-3",name:"Hair Chalk Set",description:"Temporary hair color chalk for festivals",price:15,category_name:"Hair",status:"active",image_url:"/images/products/hair-chalk.jpg",stock_quantity:30}],fallback:!0,message:"Using cached product data"});let c=e.filter(e=>e.stock_quantity>0).map(e=>({id:e.id,name:e.name,description:e.description,price:e.price,category_name:e.category_name,image_url:e.image_url,stock_quantity:e.stock_quantity,features:e.features,ingredients:e.ingredients}));return"true"===a&&c.splice(6),t.setHeader("Cache-Control","public, s-maxage=600, stale-while-revalidate=1200"),console.log(`[Public API] Successfully fetched ${c.length} products`),t.status(200).json({products:c,count:c.length,category:r||"all",fallback:!1})}catch(e){return console.error("[Public API] Unexpected error fetching products:",e),t.status(500).json({error:"Unable to fetch products",products:[],fallback:!0})}}let l=(0,s.l)(a,"default"),u=(0,s.l)(a,"config"),d=new i.PagesAPIRouteModule({definition:{kind:c.x.PAGES_API,page:"/api/public/products",pathname:"/api/public/products",bundlePath:"",filename:""},userland:a})},395:(e,t,r)=>{r.d(t,{v8:()=>l,OQ:()=>n});let a=require("@supabase/supabase-js"),i="https://ndlgbcsbidyhxbpqzgqp.supabase.co",c="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI";if(!i||!c)throw Error("Missing required Supabase environment variables for public client");let s={"X-Client-Info":"ocean-soul-sparkles-public@1.0.0","X-Client-Type":"public-website"},o=(e,t={})=>Promise.race([fetch(e,t),new Promise((e,t)=>setTimeout(()=>t(Error("Request timeout")),1e4))]),n=function(){try{console.log("[Public Supabase] Creating public client instance");let e=(0,a.createClient)(i,c,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!1,storageKey:"oss_public_auth_token",storage:void 0,cookieOptions:{path:"/",sameSite:"Lax",secure:!0}},global:{headers:s,fetch:o},realtime:{params:{eventsPerSecond:1}}});return console.log("[Public Supabase] Client created successfully"),e}catch(e){throw console.error("[Public Supabase] Error creating client:",e),e}}(),l={async getServices(){try{let{data:e,error:t}=await n.from("services").select("*").eq("status","active").eq("visible_on_public",!0).gte("duration",120).lte("duration",360).order("name");return{data:e,error:t}}catch(e){return console.error("[Public Data] Error fetching services:",e),{data:null,error:e}}},async getProducts(e=null){try{let t=n.from("products").select("*").eq("status","active");e&&"all"!==e&&(t=t.eq("category_name",e));let{data:r,error:a}=await t.order("name");return{data:r,error:a}}catch(e){return console.error("[Public Data] Error fetching products:",e),{data:null,error:e}}}}},7153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},1802:(e,t,r)=>{e.exports=r(145)}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=6756);module.exports=r})();