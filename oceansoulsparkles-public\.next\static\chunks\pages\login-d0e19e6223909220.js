(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[459],{6429:function(e,n,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/login",function(){return s(1632)}])},1632:function(e,n,s){"use strict";s.r(n),s.d(n,{default:function(){return m}});var t=s(5893),a=s(7294),r=s(1163),i=s(9008),o=s.n(i),u=s(1664),l=s.n(u),c=s(5497),h=s(1817),d=s(2920),_=s(589),p=s.n(_);function m(){let[e,n]=(0,a.useState)(""),[s,i]=(0,a.useState)(""),[u,_]=(0,a.useState)(!1),[m,x]=(0,a.useState)(!1),{signIn:j,isAuthenticated:f,loading:g}=(0,h.O)(),b=(0,r.useRouter)();(0,a.useEffect)(()=>{if(f&&!g){let e=b.query.redirect||"/";b.replace(e)}},[f,g,b]);let N=async n=>{if(n.preventDefault(),!e||!s){d.Am.error("Please fill in all fields");return}_(!0);try{if((await j(e,s)).success){let e=b.query.redirect||"/";b.push(e)}}catch(e){console.error("Login error:",e),d.Am.error("Login failed. Please try again.")}finally{_(!1)}};return g?(0,t.jsx)(c.Z,{children:(0,t.jsx)("div",{className:p().authContainer,children:(0,t.jsx)("div",{className:p().authCard,children:(0,t.jsxs)("div",{className:p().loadingSpinner,children:[(0,t.jsx)("div",{className:p().spinner}),(0,t.jsx)("p",{children:"Loading..."})]})})})}):f?null:(0,t.jsxs)(c.Z,{children:[(0,t.jsxs)(o(),{children:[(0,t.jsx)("title",{children:"Customer Login - Ocean Soul Sparkles"}),(0,t.jsx)("meta",{name:"description",content:"Sign in to your Ocean Soul Sparkles customer account to manage bookings and view order history."}),(0,t.jsx)("meta",{name:"robots",content:"noindex, nofollow"})]}),(0,t.jsxs)("div",{className:p().authContainer,children:[(0,t.jsxs)("div",{className:p().authCard,children:[(0,t.jsxs)("div",{className:p().authHeader,children:[(0,t.jsx)("h1",{children:"Welcome Back"}),(0,t.jsx)("p",{children:"Sign in to your customer account"})]}),(0,t.jsxs)("form",{onSubmit:N,className:p().authForm,children:[(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"email",children:"Email Address"}),(0,t.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>n(e.target.value),placeholder:"Enter your email",required:!0,disabled:u,className:p().formInput})]}),(0,t.jsxs)("div",{className:p().formGroup,children:[(0,t.jsx)("label",{htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:p().passwordInput,children:[(0,t.jsx)("input",{id:"password",type:m?"text":"password",value:s,onChange:e=>i(e.target.value),placeholder:"Enter your password",required:!0,disabled:u,className:p().formInput}),(0,t.jsx)("button",{type:"button",onClick:()=>x(!m),className:p().passwordToggle,disabled:u,children:m?"\uD83D\uDC41️":"\uD83D\uDC41️‍\uD83D\uDDE8️"})]})]}),(0,t.jsx)("button",{type:"submit",disabled:u,className:"".concat(p().authButton," ").concat(u?p().loading:""),children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:p().buttonSpinner}),"Signing In..."]}):"Sign In"})]}),(0,t.jsxs)("div",{className:p().authLinks,children:[(0,t.jsxs)("p",{children:["Don't have an account?"," ",(0,t.jsx)(l(),{href:"/signup",className:p().authLink,children:"Create Account"})]}),(0,t.jsx)("p",{children:(0,t.jsx)(l(),{href:"/forgot-password",className:p().authLink,children:"Forgot your password?"})})]}),(0,t.jsxs)("div",{className:p().guestOption,children:[(0,t.jsx)("div",{className:p().divider,children:(0,t.jsx)("span",{children:"or"})}),(0,t.jsxs)("p",{children:["Want to book without an account?"," ",(0,t.jsx)(l(),{href:"/book-online",className:p().authLink,children:"Continue as Guest"})]})]})]}),(0,t.jsx)("div",{className:p().authFooter,children:(0,t.jsxs)("p",{children:["By signing in, you agree to our"," ",(0,t.jsx)(l(),{href:"/policies#terms",className:p().authLink,children:"Terms of Service"})," ","and"," ",(0,t.jsx)(l(),{href:"/policies#privacy",className:p().authLink,children:"Privacy Policy"})]})})]})]})}},589:function(e){e.exports={authContainer:"Auth_authContainer__krRUD",authCard:"Auth_authCard__c52sr",authHeader:"Auth_authHeader__tImyy",authForm:"Auth_authForm__Q3DLf",formRow:"Auth_formRow__cgwUK",formGroup:"Auth_formGroup__9PGF_",formInput:"Auth_formInput__GBmIX",passwordInput:"Auth_passwordInput__Z_Gdm",passwordToggle:"Auth_passwordToggle__0CEK0",checkboxGroup:"Auth_checkboxGroup__GTN4W",checkboxLabel:"Auth_checkboxLabel__jHubw",checkbox:"Auth_checkbox__CWB76",checkboxText:"Auth_checkboxText__C8ZZD",authButton:"Auth_authButton__FY9nb",loading:"Auth_loading___YK33",buttonSpinner:"Auth_buttonSpinner__OxbRv",spin:"Auth_spin__3Kb1j",authLinks:"Auth_authLinks__wOm40",authLink:"Auth_authLink__3rvVg",guestOption:"Auth_guestOption__TtmNY",divider:"Auth_divider__bZzOZ",authFooter:"Auth_authFooter__Wv0sO",loadingSpinner:"Auth_loadingSpinner__UuqwH",spinner:"Auth_spinner__fRASl"}}},function(e){e.O(0,[409,497,888,774,179],function(){return e(e.s=6429)}),_N_E=e.O()}]);