{"version": 3, "sources": ["../../../src/telemetry/events/plugins.ts"], "names": ["eventNextPlugins", "EVENT_PLUGIN_PRESENT", "dir", "packageJsonPath", "findUp", "cwd", "dependencies", "devDependencies", "require", "deps", "Object", "keys", "reduce", "events", "plugin", "version", "push", "eventName", "payload", "packageName", "packageVersion", "_"], "mappings": ";;;;+BA<PERSON>sBA;;;eAAAA;;;+DAXH;;;;;;AAEnB,MAAMC,uBAAuB;AAStB,eAAeD,iBACpBE,GAAW;IAEX,IAAI;QACF,MAAMC,kBAAkB,MAAMC,IAAAA,eAAM,EAAC,gBAAgB;YAAEC,KAAKH;QAAI;QAChE,IAAI,CAACC,iBAAiB;YACpB,OAAO,EAAE;QACX;QAEA,MAAM,EAAEG,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGC,QAAQL;QAE5D,MAAMM,OAAO;YAAE,GAAGF,eAAe;YAAE,GAAGD,YAAY;QAAC;QAEnD,OAAOI,OAAOC,IAAI,CAACF,MAAMG,MAAM,CAC7B,CAACC,QAA4BC;YAC3B,MAAMC,UAAUN,IAAI,CAACK,OAAO;YAC5B,uCAAuC;YACvC,IAAI,CAACC,SAAS;gBACZ,OAAOF;YACT;YAEAA,OAAOG,IAAI,CAAC;gBACVC,WAAWhB;gBACXiB,SAAS;oBACPC,aAAaL;oBACbM,gBAAgBL;gBAClB;YACF;YAEA,OAAOF;QACT,GACA,EAAE;IAEN,EAAE,OAAOQ,GAAG;QACV,OAAO,EAAE;IACX;AACF"}