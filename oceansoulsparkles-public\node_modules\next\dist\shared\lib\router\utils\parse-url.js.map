{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/parse-url.ts"], "names": ["parseUrl", "url", "startsWith", "parseRelativeUrl", "parsedURL", "URL", "hash", "hostname", "href", "pathname", "port", "protocol", "query", "searchParamsToUrlQuery", "searchParams", "search"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;6BAduB;kCACN;AAa1B,SAASA,SAASC,GAAW;IAClC,IAAIA,IAAIC,UAAU,CAAC,MAAM;QACvB,OAAOC,IAAAA,kCAAgB,EAACF;IAC1B;IAEA,MAAMG,YAAY,IAAIC,IAAIJ;IAC1B,OAAO;QACLK,MAAMF,UAAUE,IAAI;QACpBC,UAAUH,UAAUG,QAAQ;QAC5BC,MAAMJ,UAAUI,IAAI;QACpBC,UAAUL,UAAUK,QAAQ;QAC5BC,MAAMN,UAAUM,IAAI;QACpBC,UAAUP,UAAUO,QAAQ;QAC5BC,OAAOC,IAAAA,mCAAsB,EAACT,UAAUU,YAAY;QACpDC,QAAQX,UAAUW,MAAM;IAC1B;AACF"}