#!/usr/bin/env node

/**
 * Environment Variables Check Script for Admin Subdomain
 * Validates that all required environment variables are set before build
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local if it exists
const envLocalPath = path.join(__dirname, '..', '.env.local');
if (fs.existsSync(envLocalPath)) {
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  const envLines = envContent.split('\n');

  for (const line of envLines) {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#') && trimmedLine.includes('=')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      const value = valueParts.join('=');
      if (key && value && !process.env[key]) {
        process.env[key] = value;
      }
    }
  }
}

// Required environment variables for admin subdomain
const REQUIRED_ENV_VARS = {
  // Core Configuration
  'NEXT_PUBLIC_ADMIN_SUBDOMAIN': 'Should be set to "true"',
  'NEXT_PUBLIC_SITE_URL': 'Admin subdomain URL (https://admin.oceansoulsparkles.com.au)',
  'NEXT_PUBLIC_PUBLIC_SITE_URL': 'Public subdomain URL (https://www.oceansoulsparkles.com.au)',

  // Supabase Configuration
  'NEXT_PUBLIC_SUPABASE_URL': 'Supabase project URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY': 'Supabase anonymous key',
  'SUPABASE_SERVICE_ROLE_KEY': 'Supabase service role key (server-side only)',

  // Authentication & Security
  'NEXTAUTH_URL': 'NextAuth URL for admin subdomain',
  'NEXTAUTH_SECRET': 'NextAuth secret key',
  'JWT_SECRET': 'JWT secret for admin tokens',
  'ENCRYPTION_KEY': '32-character encryption key',

  // Square Payment Integration
  'NEXT_PUBLIC_SQUARE_APPLICATION_ID': 'Square production application ID',
  'NEXT_PUBLIC_SQUARE_LOCATION_ID': 'Square production location ID',
  'SQUARE_ACCESS_TOKEN': 'Square production access token'
};

// Optional but recommended environment variables
const OPTIONAL_ENV_VARS = {
  // Multi-Factor Authentication
  'MFA_ISSUER': 'MFA issuer name',
  'MFA_ENCRYPTION_KEY': 'MFA encryption key',

  // Email Services
  'SMTP_HOST': 'SMTP server host',
  'SMTP_USER': 'SMTP username',
  'SMTP_PASS': 'SMTP password',

  // Analytics & Monitoring
  'NEXT_PUBLIC_GOOGLE_ANALYTICS_ID': 'Google Analytics ID for admin',
  'SENTRY_DSN': 'Sentry DSN for error tracking',

  // OneSignal Notifications
  'NEXT_PUBLIC_ONESIGNAL_APP_ID': 'OneSignal app ID for admin',
  'ONESIGNAL_REST_API_KEY': 'OneSignal REST API key'
};

// Security-sensitive variables that should never be exposed to client
const SERVER_ONLY_VARS = [
  'SUPABASE_SERVICE_ROLE_KEY',
  'JWT_SECRET',
  'ENCRYPTION_KEY',
  'SQUARE_ACCESS_TOKEN',
  'SMTP_PASS',
  'ONESIGNAL_REST_API_KEY',
  'SENTRY_DSN'
];

function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables for admin subdomain...\n');

  let hasErrors = false;
  let hasWarnings = false;

  // Check required variables
  console.log('📋 Required Environment Variables:');
  for (const [varName, description] of Object.entries(REQUIRED_ENV_VARS)) {
    const value = process.env[varName];
    
    if (!value) {
      console.log(`❌ ${varName}: MISSING - ${description}`);
      hasErrors = true;
    } else {
      // Mask sensitive values
      const displayValue = SERVER_ONLY_VARS.includes(varName) 
        ? '***HIDDEN***' 
        : value.length > 50 
          ? value.substring(0, 20) + '...' 
          : value;
      console.log(`✅ ${varName}: ${displayValue}`);
    }
  }

  console.log('\n📋 Optional Environment Variables:');
  for (const [varName, description] of Object.entries(OPTIONAL_ENV_VARS)) {
    const value = process.env[varName];
    
    if (!value) {
      console.log(`⚠️  ${varName}: NOT SET - ${description}`);
      hasWarnings = true;
    } else {
      const displayValue = SERVER_ONLY_VARS.includes(varName) 
        ? '***HIDDEN***' 
        : value.length > 50 
          ? value.substring(0, 20) + '...' 
          : value;
      console.log(`✅ ${varName}: ${displayValue}`);
    }
  }

  // Security checks
  console.log('\n🔒 Security Checks:');
  
  // Check if admin subdomain flag is set
  if (process.env.NEXT_PUBLIC_ADMIN_SUBDOMAIN !== 'true') {
    console.log('❌ NEXT_PUBLIC_ADMIN_SUBDOMAIN must be set to "true"');
    hasErrors = true;
  } else {
    console.log('✅ Admin subdomain flag is correctly set');
  }

  // Check for accidentally exposed server-only variables
  const exposedServerVars = SERVER_ONLY_VARS.filter(varName => 
    varName.startsWith('NEXT_PUBLIC_') && process.env[varName]
  );
  
  if (exposedServerVars.length > 0) {
    console.log('❌ Server-only variables exposed to client:');
    exposedServerVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    hasErrors = true;
  } else {
    console.log('✅ No server-only variables exposed to client');
  }

  // Check JWT secret strength
  const jwtSecret = process.env.JWT_SECRET;
  if (jwtSecret && jwtSecret.length < 32) {
    console.log('⚠️  JWT_SECRET should be at least 32 characters long');
    hasWarnings = true;
  } else if (jwtSecret) {
    console.log('✅ JWT_SECRET has adequate length');
  }

  // Check encryption key format
  const encryptionKey = process.env.ENCRYPTION_KEY;
  if (encryptionKey && encryptionKey.length !== 32) {
    console.log('❌ ENCRYPTION_KEY must be exactly 32 characters long');
    hasErrors = true;
  } else if (encryptionKey) {
    console.log('✅ ENCRYPTION_KEY has correct length');
  }

  // Environment-specific checks
  const nodeEnv = process.env.NODE_ENV;
  console.log(`\n🌍 Environment: ${nodeEnv || 'development'}`);
  
  if (nodeEnv === 'production') {
    // Production-specific checks
    const productionChecks = [
      'NEXTAUTH_SECRET',
      'JWT_SECRET',
      'ENCRYPTION_KEY'
    ];
    
    for (const varName of productionChecks) {
      if (!process.env[varName]) {
        console.log(`❌ ${varName} is required in production`);
        hasErrors = true;
      }
    }

    // Check for development flags in production
    if (process.env.NEXT_PUBLIC_DEBUG_MODE === 'true') {
      console.log('⚠️  DEBUG_MODE is enabled in production');
      hasWarnings = true;
    }

    if (process.env.DEV_BYPASS_AUTH === 'true') {
      console.log('❌ DEV_BYPASS_AUTH is enabled in production - SECURITY RISK!');
      hasErrors = true;
    }
  }

  // Summary
  console.log('\n📊 Summary:');
  if (hasErrors) {
    console.log('❌ Environment check FAILED - Missing required variables or security issues');
    console.log('   Please fix the errors above before building the admin subdomain.');
    process.exit(1);
  } else if (hasWarnings) {
    console.log('⚠️  Environment check PASSED with warnings');
    console.log('   Consider setting the optional variables for full functionality.');
  } else {
    console.log('✅ Environment check PASSED - All required variables are set');
  }

  console.log('\n🚀 Ready to build admin subdomain!');
}

// Check if .env.example exists and compare
function checkEnvExample() {
  const envExamplePath = path.join(__dirname, '..', '.env.example');
  
  if (fs.existsSync(envExamplePath)) {
    console.log('\n📄 Checking against .env.example...');
    
    try {
      const envExample = fs.readFileSync(envExamplePath, 'utf8');
      const exampleVars = envExample
        .split('\n')
        .filter(line => line.includes('=') && !line.startsWith('#'))
        .map(line => line.split('=')[0].trim());

      const missingFromEnv = exampleVars.filter(varName => !process.env[varName]);
      
      if (missingFromEnv.length > 0) {
        console.log('⚠️  Variables in .env.example but not set:');
        missingFromEnv.forEach(varName => {
          console.log(`   - ${varName}`);
        });
      } else {
        console.log('✅ All variables from .env.example are set');
      }
    } catch (error) {
      console.log('⚠️  Could not read .env.example file');
    }
  }
}

// Main execution
if (require.main === module) {
  checkEnvironmentVariables();
  checkEnvExample();
}
