{"version": 2, "name": "oceansoulsparkles-public", "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/(.*)", "dest": "/$1"}], "env": {"NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "NEXT_PUBLIC_SITE_URL": "@next_public_site_url", "NEXT_PUBLIC_ADMIN_ACCESS": "false", "NEXT_PUBLIC_DEV_MODE": "false", "NEXT_PUBLIC_DEBUG_AUTH": "false", "ENABLE_AUTH_BYPASS": "false"}, "functions": {"pages/api/**/*.js": {"maxDuration": 10}}, "headers": [{"source": "/admin/(.*)", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow"}]}, {"source": "/api/admin/(.*)", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow"}]}], "redirects": [{"source": "/admin/:path*", "destination": "/404", "permanent": false}, {"source": "/staff/:path*", "destination": "/404", "permanent": false}, {"source": "/artist/:path*", "destination": "/404", "permanent": false}, {"source": "/apply/:path*", "destination": "/404", "permanent": false}], "rewrites": [{"source": "/.well-known/apple-developer-merchantid-domain-association", "destination": "/api/apple-pay/domain-association"}]}