/**
 * Public Analytics Integration
 * Customer-focused analytics for the public subdomain
 */

/**
 * Initialize Google Analytics for customer tracking
 */
export async function initializeAnalytics() {
  if (typeof window === 'undefined' || !process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID) {
    return;
  }

  try {
    // Load Google Analytics
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      window.dataLayer.push(arguments);
    }
    window.gtag = gtag;

    gtag('js', new Date());
    gtag('config', process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID, {
      page_title: document.title,
      page_location: window.location.href,
      anonymize_ip: true,
      allow_google_signals: false,
      allow_ad_personalization_signals: false,
      cookie_flags: 'SameSite=Lax;Secure'
    });

    console.log('[Analytics] Google Analytics initialized');
  } catch (error) {
    console.error('[Analytics] Failed to initialize Google Analytics:', error);
  }
}

/**
 * Track page views
 */
export function trackPageView(url, title) {
  if (typeof window === 'undefined' || !window.gtag) {
    return;
  }

  try {
    window.gtag('config', process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID, {
      page_path: url,
      page_title: title,
    });

    console.log('[Analytics] Page view tracked:', { url, title });
  } catch (error) {
    console.error('[Analytics] Error tracking page view:', error);
  }
}

/**
 * Track custom events
 */
export function trackEvent(action, category, label, value) {
  if (typeof window === 'undefined' || !window.gtag) {
    return;
  }

  try {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });

    console.log('[Analytics] Event tracked:', { action, category, label, value });
  } catch (error) {
    console.error('[Analytics] Error tracking event:', error);
  }
}

/**
 * Track booking events
 */
export function trackBooking(bookingData) {
  trackEvent('booking_started', 'engagement', 'booking_form', 1);
  
  if (bookingData.service_name) {
    trackEvent('service_selected', 'booking', bookingData.service_name, 1);
  }
}

/**
 * Track booking completion
 */
export function trackBookingComplete(bookingData) {
  trackEvent('booking_completed', 'conversion', bookingData.service_name, bookingData.estimated_total);
  
  // Track as conversion
  window.gtag('event', 'conversion', {
    send_to: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
    value: bookingData.estimated_total,
    currency: 'AUD',
    transaction_id: bookingData.booking_id
  });
}

/**
 * Track product views
 */
export function trackProductView(product) {
  trackEvent('view_item', 'ecommerce', product.name, product.price);
  
  window.gtag('event', 'view_item', {
    currency: 'AUD',
    value: product.price,
    items: [{
      item_id: product.id,
      item_name: product.name,
      item_category: product.category_name,
      price: product.price,
      quantity: 1
    }]
  });
}

/**
 * Track add to cart
 */
export function trackAddToCart(product, quantity = 1) {
  trackEvent('add_to_cart', 'ecommerce', product.name, product.price * quantity);
  
  window.gtag('event', 'add_to_cart', {
    currency: 'AUD',
    value: product.price * quantity,
    items: [{
      item_id: product.id,
      item_name: product.name,
      item_category: product.category_name,
      price: product.price,
      quantity: quantity
    }]
  });
}

/**
 * Track contact form submissions
 */
export function trackContactForm(formType) {
  trackEvent('form_submit', 'engagement', formType, 1);
}

/**
 * Track search queries
 */
export function trackSearch(searchTerm, category = 'all') {
  trackEvent('search', 'engagement', searchTerm, 1);
  
  window.gtag('event', 'search', {
    search_term: searchTerm,
    content_category: category
  });
}

/**
 * Track user engagement
 */
export function trackEngagement(action, element) {
  trackEvent(action, 'engagement', element, 1);
}

/**
 * Track scroll depth
 */
export function trackScrollDepth(percentage) {
  if (percentage === 25 || percentage === 50 || percentage === 75 || percentage === 100) {
    trackEvent('scroll', 'engagement', `${percentage}%`, percentage);
  }
}

/**
 * Track file downloads
 */
export function trackDownload(fileName, fileType) {
  trackEvent('file_download', 'engagement', fileName, 1);
  
  window.gtag('event', 'file_download', {
    file_name: fileName,
    file_extension: fileType
  });
}

/**
 * Track external link clicks
 */
export function trackExternalLink(url, linkText) {
  trackEvent('click', 'external_link', url, 1);
  
  window.gtag('event', 'click', {
    link_url: url,
    link_text: linkText,
    outbound: true
  });
}

/**
 * Set user properties (privacy-compliant)
 */
export function setUserProperties(properties) {
  if (typeof window === 'undefined' || !window.gtag) {
    return;
  }

  try {
    // Only set privacy-compliant properties
    const allowedProperties = {
      customer_type: properties.customer_type,
      preferred_service: properties.preferred_service,
      location_region: properties.location_region
    };

    // Filter out undefined values
    const filteredProperties = Object.fromEntries(
      Object.entries(allowedProperties).filter(([_, value]) => value !== undefined)
    );

    window.gtag('set', 'user_properties', filteredProperties);
    console.log('[Analytics] User properties set:', filteredProperties);
  } catch (error) {
    console.error('[Analytics] Error setting user properties:', error);
  }
}
