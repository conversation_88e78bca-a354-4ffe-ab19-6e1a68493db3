import { useState, useEffect, useRef, Children } from 'react';

export default function StaggeredList({ 
  children, 
  staggerDelay = 100,
  baseDelay = 0,
  className = '',
  threshold = 0.1 
}) {
  const [visibleItems, setVisibleItems] = useState(new Set());
  const [hasStarted, setHasStarted] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasStarted) {
          setHasStarted(true);
          
          // Start the staggered animation
          const childrenArray = Children.toArray(children);
          childrenArray.forEach((_, index) => {
            setTimeout(() => {
              setVisibleItems(prev => new Set([...prev, index]));
            }, baseDelay + (index * staggerDelay));
          });
        }
      },
      { threshold }
    );

    const currentContainer = containerRef.current;
    if (currentContainer) {
      observer.observe(currentContainer);
    }

    return () => {
      if (currentContainer) {
        observer.unobserve(currentContainer);
      }
    };
  }, [children, staggerDelay, baseDelay, threshold, hasStarted]);

  const getItemStyle = (index) => ({
    opacity: visibleItems.has(index) ? 1 : 0,
    transform: visibleItems.has(index) ? 'translateY(0)' : 'translateY(30px)',
    transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
  });

  return (
    <div ref={containerRef} className={className}>
      {Children.map(children, (child, index) => (
        <div key={index} style={getItemStyle(index)}>
          {child}
        </div>
      ))}
    </div>
  );
}
