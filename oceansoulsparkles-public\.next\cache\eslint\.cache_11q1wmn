[{"C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\bookings\\create.js": "1", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\public\\products.js": "2", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\public\\services.js": "3", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\book-online.js": "4", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\index.js": "5", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\login.js": "6", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\shop.js": "7", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\signup.js": "8", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\_app.js": "9", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\BookingForm.js": "10", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ErrorBoundary.js": "11", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\Layout.js": "12", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ProductCard.js": "13", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\PWAProvider.js": "14", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ServiceSelector.js": "15", "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\lib\\supabase.js": "16"}, {"size": 7017, "mtime": 1749877003628, "results": "17", "hashOfConfig": "18"}, {"size": 3418, "mtime": 1749872170035, "results": "19", "hashOfConfig": "18"}, {"size": 2969, "mtime": 1749872146152, "results": "20", "hashOfConfig": "18"}, {"size": 8176, "mtime": 1749876914430, "results": "21", "hashOfConfig": "18"}, {"size": 14019, "mtime": 1749872069376, "results": "22", "hashOfConfig": "18"}, {"size": 5671, "mtime": 1749876825945, "results": "23", "hashOfConfig": "18"}, {"size": 7634, "mtime": 1749877628943, "results": "24", "hashOfConfig": "18"}, {"size": 10254, "mtime": 1749876856824, "results": "25", "hashOfConfig": "18"}, {"size": 5375, "mtime": 1749872103556, "results": "26", "hashOfConfig": "18"}, {"size": 14279, "mtime": 1749876974749, "results": "27", "hashOfConfig": "18"}, {"size": 5815, "mtime": 1749876781712, "results": "28", "hashOfConfig": "18"}, {"size": 9089, "mtime": 1749872263375, "results": "29", "hashOfConfig": "18"}, {"size": 5145, "mtime": 1749877651064, "results": "30", "hashOfConfig": "18"}, {"size": 6035, "mtime": 1749876804739, "results": "31", "hashOfConfig": "18"}, {"size": 4968, "mtime": 1749876933372, "results": "32", "hashOfConfig": "18"}, {"size": 5805, "mtime": 1749872004100, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1axaeq4", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\bookings\\create.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\public\\products.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\api\\public\\services.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\book-online.js", ["82", "83", "84", "85"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\index.js", ["86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\login.js", ["104"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\shop.js", ["105", "106", "107"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\signup.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\pages\\_app.js", ["108", "109", "110"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\BookingForm.js", ["111"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ErrorBoundary.js", ["112", "113"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\Layout.js", ["114", "115", "116", "117", "118", "119"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ProductCard.js", ["120"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\PWAProvider.js", [], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\components\\ServiceSelector.js", ["121"], [], "C:\\Users\\<USER>\\Downloads\\website-editor-package\\home\\ubuntu\\website-editor\\website-ocean-soul-sparkles\\oceansoulsparkles-public\\lib\\supabase.js", [], [], {"ruleId": "122", "severity": 1, "message": "123", "line": 93, "column": 42, "nodeType": "124", "messageId": "125", "suggestions": "126"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 112, "column": 24, "nodeType": "124", "messageId": "125", "suggestions": "127"}, {"ruleId": "128", "severity": 1, "message": "129", "line": 165, "column": 17, "nodeType": "130", "endLine": 165, "endColumn": 70}, {"ruleId": "128", "severity": 1, "message": "131", "line": 166, "column": 17, "nodeType": "130", "endLine": 166, "endColumn": 66}, {"ruleId": "132", "severity": 1, "message": "133", "line": 63, "column": 17, "nodeType": "130", "endLine": 63, "endColumn": 129}, {"ruleId": "132", "severity": 1, "message": "133", "line": 76, "column": 17, "nodeType": "130", "endLine": 76, "endColumn": 112}, {"ruleId": "132", "severity": 1, "message": "133", "line": 89, "column": 17, "nodeType": "130", "endLine": 89, "endColumn": 125}, {"ruleId": "122", "severity": 1, "message": "123", "line": 115, "column": 23, "nodeType": "124", "messageId": "125", "suggestions": "134"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 119, "column": 54, "nodeType": "124", "messageId": "125", "suggestions": "135"}, {"ruleId": "132", "severity": 1, "message": "133", "line": 127, "column": 13, "nodeType": "130", "endLine": 127, "endColumn": 136}, {"ruleId": "132", "severity": 1, "message": "133", "line": 139, "column": 15, "nodeType": "130", "endLine": 139, "endColumn": 122}, {"ruleId": "132", "severity": 1, "message": "133", "line": 145, "column": 15, "nodeType": "130", "endLine": 145, "endColumn": 122}, {"ruleId": "132", "severity": 1, "message": "133", "line": 151, "column": 15, "nodeType": "130", "endLine": 151, "endColumn": 122}, {"ruleId": "132", "severity": 1, "message": "133", "line": 157, "column": 15, "nodeType": "130", "endLine": 157, "endColumn": 122}, {"ruleId": "132", "severity": 1, "message": "133", "line": 163, "column": 15, "nodeType": "130", "endLine": 163, "endColumn": 122}, {"ruleId": "132", "severity": 1, "message": "133", "line": 169, "column": 15, "nodeType": "130", "endLine": 169, "endColumn": 122}, {"ruleId": "132", "severity": 1, "message": "133", "line": 175, "column": 15, "nodeType": "130", "endLine": 175, "endColumn": 122}, {"ruleId": "132", "severity": 1, "message": "133", "line": 181, "column": 15, "nodeType": "130", "endLine": 181, "endColumn": 135}, {"ruleId": "122", "severity": 1, "message": "123", "line": 210, "column": 22, "nodeType": "124", "messageId": "125", "suggestions": "136"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 210, "column": 128, "nodeType": "124", "messageId": "125", "suggestions": "137"}, {"ruleId": "132", "severity": 1, "message": "133", "line": 230, "column": 21, "nodeType": "130", "endLine": 230, "endColumn": 84}, {"ruleId": "132", "severity": 1, "message": "133", "line": 233, "column": 21, "nodeType": "130", "endLine": 233, "endColumn": 82}, {"ruleId": "122", "severity": 1, "message": "123", "line": 145, "column": 18, "nodeType": "124", "messageId": "125", "suggestions": "138"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 18, "column": 6, "nodeType": "141", "endLine": 18, "endColumn": 24, "suggestions": "142"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 152, "column": 48, "nodeType": "124", "messageId": "125", "suggestions": "143"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 184, "column": 58, "nodeType": "124", "messageId": "125", "suggestions": "144"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 128, "column": 24, "nodeType": "124", "messageId": "125", "suggestions": "145"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 128, "column": 45, "nodeType": "124", "messageId": "125", "suggestions": "146"}, {"ruleId": "128", "severity": 1, "message": "147", "line": 129, "column": 9, "nodeType": "130", "endLine": 129, "endColumn": 74}, {"ruleId": "122", "severity": 1, "message": "123", "line": 385, "column": 15, "nodeType": "124", "messageId": "125", "suggestions": "148"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 98, "column": 15, "nodeType": "124", "messageId": "125", "suggestions": "149"}, {"ruleId": "128", "severity": 1, "message": "147", "line": 121, "column": 13, "nodeType": "130", "endLine": 134, "endColumn": 14}, {"ruleId": "132", "severity": 1, "message": "133", "line": 96, "column": 13, "nodeType": "130", "endLine": 96, "endColumn": 75}, {"ruleId": "122", "severity": 1, "message": "150", "line": 149, "column": 47, "nodeType": "124", "messageId": "125", "suggestions": "151"}, {"ruleId": "122", "severity": 1, "message": "150", "line": 149, "column": 125, "nodeType": "124", "messageId": "125", "suggestions": "152"}, {"ruleId": "132", "severity": 1, "message": "133", "line": 153, "column": 19, "nodeType": "130", "endLine": 153, "endColumn": 82}, {"ruleId": "132", "severity": 1, "message": "133", "line": 156, "column": 19, "nodeType": "130", "endLine": 156, "endColumn": 80}, {"ruleId": "132", "severity": 1, "message": "133", "line": 204, "column": 13, "nodeType": "130", "endLine": 204, "endColumn": 126}, {"ruleId": "132", "severity": 1, "message": "133", "line": 59, "column": 11, "nodeType": "130", "endLine": 64, "endColumn": 13}, {"ruleId": "132", "severity": 1, "message": "133", "line": 64, "column": 17, "nodeType": "130", "endLine": 70, "endColumn": 19}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["153", "154", "155", "156"], ["157", "158", "159", "160"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/signup/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "Do not use an `<a>` element to navigate to `/login/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", ["161", "162", "163", "164"], ["165", "166", "167", "168"], ["169", "170", "171", "172"], ["173", "174", "175", "176"], ["177", "178", "179", "180"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["181"], ["182", "183", "184", "185"], ["186", "187", "188", "189"], ["190", "191", "192", "193"], ["194", "195", "196", "197"], "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", ["198", "199", "200", "201"], ["202", "203", "204", "205"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["206", "207", "208", "209"], ["210", "211", "212", "213"], {"messageId": "214", "data": "215", "fix": "216", "desc": "217"}, {"messageId": "214", "data": "218", "fix": "219", "desc": "220"}, {"messageId": "214", "data": "221", "fix": "222", "desc": "223"}, {"messageId": "214", "data": "224", "fix": "225", "desc": "226"}, {"messageId": "214", "data": "227", "fix": "228", "desc": "217"}, {"messageId": "214", "data": "229", "fix": "230", "desc": "220"}, {"messageId": "214", "data": "231", "fix": "232", "desc": "223"}, {"messageId": "214", "data": "233", "fix": "234", "desc": "226"}, {"messageId": "214", "data": "235", "fix": "236", "desc": "217"}, {"messageId": "214", "data": "237", "fix": "238", "desc": "220"}, {"messageId": "214", "data": "239", "fix": "240", "desc": "223"}, {"messageId": "214", "data": "241", "fix": "242", "desc": "226"}, {"messageId": "214", "data": "243", "fix": "244", "desc": "217"}, {"messageId": "214", "data": "245", "fix": "246", "desc": "220"}, {"messageId": "214", "data": "247", "fix": "248", "desc": "223"}, {"messageId": "214", "data": "249", "fix": "250", "desc": "226"}, {"messageId": "214", "data": "251", "fix": "252", "desc": "217"}, {"messageId": "214", "data": "253", "fix": "254", "desc": "220"}, {"messageId": "214", "data": "255", "fix": "256", "desc": "223"}, {"messageId": "214", "data": "257", "fix": "258", "desc": "226"}, {"messageId": "214", "data": "259", "fix": "260", "desc": "217"}, {"messageId": "214", "data": "261", "fix": "262", "desc": "220"}, {"messageId": "214", "data": "263", "fix": "264", "desc": "223"}, {"messageId": "214", "data": "265", "fix": "266", "desc": "226"}, {"messageId": "214", "data": "267", "fix": "268", "desc": "217"}, {"messageId": "214", "data": "269", "fix": "270", "desc": "220"}, {"messageId": "214", "data": "271", "fix": "272", "desc": "223"}, {"messageId": "214", "data": "273", "fix": "274", "desc": "226"}, {"desc": "275", "fix": "276"}, {"messageId": "214", "data": "277", "fix": "278", "desc": "217"}, {"messageId": "214", "data": "279", "fix": "280", "desc": "220"}, {"messageId": "214", "data": "281", "fix": "282", "desc": "223"}, {"messageId": "214", "data": "283", "fix": "284", "desc": "226"}, {"messageId": "214", "data": "285", "fix": "286", "desc": "217"}, {"messageId": "214", "data": "287", "fix": "288", "desc": "220"}, {"messageId": "214", "data": "289", "fix": "290", "desc": "223"}, {"messageId": "214", "data": "291", "fix": "292", "desc": "226"}, {"messageId": "214", "data": "293", "fix": "294", "desc": "217"}, {"messageId": "214", "data": "295", "fix": "296", "desc": "220"}, {"messageId": "214", "data": "297", "fix": "298", "desc": "223"}, {"messageId": "214", "data": "299", "fix": "300", "desc": "226"}, {"messageId": "214", "data": "301", "fix": "302", "desc": "217"}, {"messageId": "214", "data": "303", "fix": "304", "desc": "220"}, {"messageId": "214", "data": "305", "fix": "306", "desc": "223"}, {"messageId": "214", "data": "307", "fix": "308", "desc": "226"}, {"messageId": "214", "data": "309", "fix": "310", "desc": "217"}, {"messageId": "214", "data": "311", "fix": "312", "desc": "220"}, {"messageId": "214", "data": "313", "fix": "314", "desc": "223"}, {"messageId": "214", "data": "315", "fix": "316", "desc": "226"}, {"messageId": "214", "data": "317", "fix": "318", "desc": "217"}, {"messageId": "214", "data": "319", "fix": "320", "desc": "220"}, {"messageId": "214", "data": "321", "fix": "322", "desc": "223"}, {"messageId": "214", "data": "323", "fix": "324", "desc": "226"}, {"messageId": "214", "data": "325", "fix": "326", "desc": "327"}, {"messageId": "214", "data": "328", "fix": "329", "desc": "330"}, {"messageId": "214", "data": "331", "fix": "332", "desc": "333"}, {"messageId": "214", "data": "334", "fix": "335", "desc": "336"}, {"messageId": "214", "data": "337", "fix": "338", "desc": "327"}, {"messageId": "214", "data": "339", "fix": "340", "desc": "330"}, {"messageId": "214", "data": "341", "fix": "342", "desc": "333"}, {"messageId": "214", "data": "343", "fix": "344", "desc": "336"}, "replaceWithAlt", {"alt": "345"}, {"range": "346", "text": "347"}, "Replace with `&apos;`.", {"alt": "348"}, {"range": "349", "text": "350"}, "Replace with `&lsquo;`.", {"alt": "351"}, {"range": "352", "text": "353"}, "Replace with `&#39;`.", {"alt": "354"}, {"range": "355", "text": "356"}, "Replace with `&rsquo;`.", {"alt": "345"}, {"range": "357", "text": "358"}, {"alt": "348"}, {"range": "359", "text": "360"}, {"alt": "351"}, {"range": "361", "text": "362"}, {"alt": "354"}, {"range": "363", "text": "364"}, {"alt": "345"}, {"range": "365", "text": "366"}, {"alt": "348"}, {"range": "367", "text": "368"}, {"alt": "351"}, {"range": "369", "text": "370"}, {"alt": "354"}, {"range": "371", "text": "372"}, {"alt": "345"}, {"range": "373", "text": "374"}, {"alt": "348"}, {"range": "375", "text": "376"}, {"alt": "351"}, {"range": "377", "text": "378"}, {"alt": "354"}, {"range": "379", "text": "380"}, {"alt": "345"}, {"range": "381", "text": "382"}, {"alt": "348"}, {"range": "383", "text": "384"}, {"alt": "351"}, {"range": "385", "text": "386"}, {"alt": "354"}, {"range": "387", "text": "388"}, {"alt": "345"}, {"range": "389", "text": "390"}, {"alt": "348"}, {"range": "391", "text": "392"}, {"alt": "351"}, {"range": "393", "text": "394"}, {"alt": "354"}, {"range": "395", "text": "396"}, {"alt": "345"}, {"range": "397", "text": "398"}, {"alt": "348"}, {"range": "399", "text": "400"}, {"alt": "351"}, {"range": "401", "text": "402"}, {"alt": "354"}, {"range": "403", "text": "404"}, "Update the dependencies array to be: [fetchProducts, selectedCategory]", {"range": "405", "text": "406"}, {"alt": "345"}, {"range": "407", "text": "408"}, {"alt": "348"}, {"range": "409", "text": "410"}, {"alt": "351"}, {"range": "411", "text": "412"}, {"alt": "354"}, {"range": "413", "text": "414"}, {"alt": "345"}, {"range": "415", "text": "416"}, {"alt": "348"}, {"range": "417", "text": "418"}, {"alt": "351"}, {"range": "419", "text": "420"}, {"alt": "354"}, {"range": "421", "text": "422"}, {"alt": "345"}, {"range": "423", "text": "424"}, {"alt": "348"}, {"range": "425", "text": "426"}, {"alt": "351"}, {"range": "427", "text": "428"}, {"alt": "354"}, {"range": "429", "text": "430"}, {"alt": "345"}, {"range": "431", "text": "432"}, {"alt": "348"}, {"range": "433", "text": "434"}, {"alt": "351"}, {"range": "435", "text": "436"}, {"alt": "354"}, {"range": "437", "text": "438"}, {"alt": "345"}, {"range": "439", "text": "440"}, {"alt": "348"}, {"range": "441", "text": "442"}, {"alt": "351"}, {"range": "443", "text": "444"}, {"alt": "354"}, {"range": "445", "text": "446"}, {"alt": "345"}, {"range": "447", "text": "448"}, {"alt": "348"}, {"range": "449", "text": "450"}, {"alt": "351"}, {"range": "451", "text": "452"}, {"alt": "354"}, {"range": "453", "text": "454"}, {"alt": "455"}, {"range": "456", "text": "457"}, "Replace with `&quot;`.", {"alt": "458"}, {"range": "459", "text": "460"}, "Replace with `&ldquo;`.", {"alt": "461"}, {"range": "462", "text": "463"}, "Replace with `&#34;`.", {"alt": "464"}, {"range": "465", "text": "466"}, "Replace with `&rdquo;`.", {"alt": "455"}, {"range": "467", "text": "468"}, {"alt": "458"}, {"range": "469", "text": "470"}, {"alt": "461"}, {"range": "471", "text": "472"}, {"alt": "464"}, {"range": "473", "text": "474"}, "&apos;", [3409, 3473], "Select the service you&apos;d like to book from our available options", "&lsquo;", [3409, 3473], "Select the service you&lsquo;d like to book from our available options", "&#39;", [3409, 3473], "Select the service you&#39;d like to book from our available options", "&rsquo;", [3409, 3473], "Select the service you&rsquo;d like to book from our available options", [4113, 4198], "We&apos;re currently updating our services. Please check back soon or contact us directly.", [4113, 4198], "We&lsquo;re currently updating our services. Please check back soon or contact us directly.", [4113, 4198], "We&#39;re currently updating our services. Please check back soon or contact us directly.", [4113, 4198], "We&rsquo;re currently updating our services. Please check back soon or contact us directly.", [5599, 5856], "\n              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,\n              and that&apos;s why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!\n            ", [5599, 5856], "\n              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,\n              and that&lsquo;s why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!\n            ", [5599, 5856], "\n              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,\n              and that&#39;s why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!\n            ", [5599, 5856], "\n              We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do,\n              and that&rsquo;s why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!\n            ", [5910, 6237], "\n              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,\n              ensuring it leaves no harmful trace. We&apos;re proud to be as friendly to the planet as we are to our customers,\n              and to provide a way for both you and the earth to shine!\n            ", [5910, 6237], "\n              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,\n              ensuring it leaves no harmful trace. We&lsquo;re proud to be as friendly to the planet as we are to our customers,\n              and to provide a way for both you and the earth to shine!\n            ", [5910, 6237], "\n              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,\n              ensuring it leaves no harmful trace. We&#39;re proud to be as friendly to the planet as we are to our customers,\n              and to provide a way for both you and the earth to shine!\n            ", [5910, 6237], "\n              Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water,\n              ensuring it leaves no harmful trace. We&rsquo;re proud to be as friendly to the planet as we are to our customers,\n              and to provide a way for both you and the earth to shine!\n            ", [10532, 10657], "We&apos;d love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.", [10532, 10657], "We&lsquo;d love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.", [10532, 10657], "We&#39;d love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.", [10532, 10657], "We&rsquo;d love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help.", [10532, 10657], "We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we&apos;re here to help.", [10532, 10657], "We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we&lsquo;re here to help.", [10532, 10657], "We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we&#39;re here to help.", [10532, 10657], "We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we&rsquo;re here to help.", [4473, 4510], "\n              Don&apos;t have an account?", [4473, 4510], "\n              Don&lsquo;t have an account?", [4473, 4510], "\n              Don&#39;t have an account?", [4473, 4510], "\n              Don&rsquo;t have an account?", [636, 654], "[fetchProducts, selected<PERSON><PERSON><PERSON><PERSON>]", [5858, 5929], "Our biodegradable products won&apos;t harm marine life or pollute waterways.", [5858, 5929], "Our biodegradable products won&lsquo;t harm marine life or pollute waterways.", [5858, 5929], "Our biodegradable products won&#39;t harm marine life or pollute waterways.", [5858, 5929], "Our biodegradable products won&rsquo;t harm marine life or pollute waterways.", [7201, 7293], "Looking for custom colors or bulk orders? We&apos;d love to help create something unique for you!", [7201, 7293], "Looking for custom colors or bulk orders? We&lsquo;d love to help create something unique for you!", [7201, 7293], "Looking for custom colors or bulk orders? We&#39;d love to help create something unique for you!", [7201, 7293], "Looking for custom colors or bulk orders? We&rsquo;d love to help create something unique for you!", [4840, 4882], "The page you&apos;re looking for doesn't exist.", [4840, 4882], "The page you&lsquo;re looking for doesn't exist.", [4840, 4882], "The page you&#39;re looking for doesn't exist.", [4840, 4882], "The page you&rsquo;re looking for doesn't exist.", [4840, 4882], "The page you're looking for doesn&apos;t exist.", [4840, 4882], "The page you're looking for doesn&lsquo;t exist.", [4840, 4882], "The page you're looking for doesn&#39;t exist.", [4840, 4882], "The page you're looking for doesn&rsquo;t exist.", [12721, 12898], "\n            *Final pricing may vary based on location, duration, and specific requirements. \n            We&apos;ll provide a detailed quote after reviewing your request.\n          ", [12721, 12898], "\n            *Final pricing may vary based on location, duration, and specific requirements. \n            We&lsquo;ll provide a detailed quote after reviewing your request.\n          ", [12721, 12898], "\n            *Final pricing may vary based on location, duration, and specific requirements. \n            We&#39;ll provide a detailed quote after reviewing your request.\n          ", [12721, 12898], "\n            *Final pricing may vary based on location, duration, and specific requirements. \n            We&rsquo;ll provide a detailed quote after reviewing your request.\n          ", [2860, 2991], "\n            We&apos;re sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\n          ", [2860, 2991], "\n            We&lsquo;re sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\n          ", [2860, 2991], "\n            We&#39;re sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\n          ", [2860, 2991], "\n            We&rsquo;re sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\n          ", "&quot;", [5089, 5168], "&quot;Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!\"", "&ldquo;", [5089, 5168], "&ldquo;Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!\"", "&#34;", [5089, 5168], "&#34;Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!\"", "&rdquo;", [5089, 5168], "&rdquo;Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!\"", [5089, 5168], "\"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!&quot;", [5089, 5168], "\"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!&ldquo;", [5089, 5168], "\"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!&#34;", [5089, 5168], "\"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!&rdquo;"]