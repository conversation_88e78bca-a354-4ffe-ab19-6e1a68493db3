{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-page-config.ts"], "names": ["types", "BabelTypes", "STRING_LITERAL_DROP_BUNDLE", "CONFIG_KEY", "replaceBundle", "path", "t", "parentPath", "replaceWith", "program", "variableDeclaration", "variableDeclarator", "identifier", "stringLiteral", "Date", "now", "errorMessage", "state", "details", "pageName", "filename", "split", "cwd", "pop", "nextPageConfig", "visitor", "Program", "enter", "traverse", "ExportDeclaration", "exportPath", "exportState", "isExportNamedDeclaration", "node", "specifiers", "some", "specifier", "isIdentifier", "exported", "name", "value", "isStringLiteral", "source", "Error", "ExportNamedDeclaration", "bundleDropped", "declaration", "length", "config", "declarations", "scope", "getBinding", "filter", "Boolean", "local", "isImportSpecifier", "id", "init", "isTSAsExpression", "expression", "isObjectExpression", "got", "type", "prop", "properties", "isSpreadElement", "key", "isObjectProperty", "isBooleanLiteral", "amp", "file", "opts", "caller", "isDev"], "mappings": "AAAA,SAASA,SAASC,UAAU,QAAQ,gCAA+B;AAQnE,SAASC,0BAA0B,QAAQ,gCAA+B;AAE1E,MAAMC,aAAa;AAEnB,qEAAqE;AACrE,SAASC,cAAcC,IAAS,EAAEC,CAAoB;IACpDD,KAAKE,UAAU,CAACC,WAAW,CACzBF,EAAEG,OAAO,CACP;QACEH,EAAEI,mBAAmB,CAAC,SAAS;YAC7BJ,EAAEK,kBAAkB,CAClBL,EAAEM,UAAU,CAACV,6BACbI,EAAEO,aAAa,CAAC,CAAC,EAAEX,2BAA2B,CAAC,EAAEY,KAAKC,GAAG,GAAG,CAAC;SAEhE;KACF,EACD,EAAE;AAGR;AAEA,SAASC,aAAaC,KAAU,EAAEC,OAAe;IAC/C,MAAMC,WACJ,AAACF,CAAAA,MAAMG,QAAQ,IAAI,EAAC,EAAGC,KAAK,CAACJ,MAAMK,GAAG,IAAI,IAAIC,GAAG,MAAM;IACzD,OAAO,CAAC,kCAAkC,EAAEL,QAAQ,SAAS,EAAEC,SAAS,2DAA2D,CAAC;AACtI;AAMA,kDAAkD;AAClD,eAAe,SAASK,eAAe,EACrCxB,OAAOM,CAAC,EAGT;IACC,OAAO;QACLmB,SAAS;YACPC,SAAS;gBACPC,OAAMtB,IAAI,EAAEY,KAAK;oBACfZ,KAAKuB,QAAQ,CACX;wBACEC,mBAAkBC,UAAU,EAAEC,WAAW;gCAGrCD;4BAFF,IACE7B,WAAW+B,wBAAwB,CAACF,WAAWG,IAAI,OACnDH,8BAAAA,WAAWG,IAAI,CAACC,UAAU,qBAA1BJ,4BAA4BK,IAAI,CAAC,CAACC;gCAChC,OACE,AAAC9B,CAAAA,EAAE+B,YAAY,CAACD,UAAUE,QAAQ,IAC9BF,UAAUE,QAAQ,CAACC,IAAI,GACvBH,UAAUE,QAAQ,CAACE,KAAK,AAAD,MAAOrC;4BAEtC,OACAF,WAAWwC,eAAe,CACxB,AAACX,WAAWG,IAAI,CACbS,MAAM,GAEX;gCACA,MAAM,IAAIC,MACR3B,aACEe,aACA;4BAGN;wBACF;wBACAa,wBACEd,UAAuD,EACvDC,WAAgB;gCAaZD,8BAGFA;4BAdF,IACEC,YAAYc,aAAa,IACxB,CAACf,WAAWG,IAAI,CAACa,WAAW,IAC3BhB,WAAWG,IAAI,CAACC,UAAU,CAACa,MAAM,KAAK,GACxC;gCACA;4BACF;4BAEA,MAAMC,SAAqB,CAAC;4BAC5B,MAAMC,eAAgD;mCAChD,EACFnB,+BAAAA,WAAWG,IAAI,CACZa,WAAW,qBAFZ,AACFhB,6BAECmB,YAAY,KAAI,EAAE;iCACrBnB,+BAAAA,WAAWoB,KAAK,CAACC,UAAU,CAAChD,gCAA5B2B,6BAAyCzB,IAAI,CAC1C4B,IAAI;6BACR,CAACmB,MAAM,CAACC;4BAET,KAAK,MAAMjB,aAAaN,WAAWG,IAAI,CAACC,UAAU,CAAE;gCAClD,IACE,AAAC5B,CAAAA,EAAE+B,YAAY,CAACD,UAAUE,QAAQ,IAC9BF,UAAUE,QAAQ,CAACC,IAAI,GACvBH,UAAUE,QAAQ,CAACE,KAAK,AAAD,MAAOrC,YAClC;oCACA,6BAA6B;oCAC7B,IAAIF,WAAWwC,eAAe,CAACX,WAAWG,IAAI,CAACS,MAAM,GAAG;wCACtD,MAAM,IAAIC,MACR3B,aACEe,aACA,CAAC,8BAA8B,CAAC;oCAGpC,4BAA4B;oCAC5B,6BAA6B;oCAC/B,OAAO,IACL9B,WAAWoC,YAAY,CACrB,AAACD,UAAyCkB,KAAK,GAEjD;4CAGIxB;wCAFJ,IACE7B,WAAWsD,iBAAiB,EAC1BzB,gCAAAA,WAAWoB,KAAK,CAACC,UAAU,CACzB,AAACf,UAAyCkB,KAAK,CAACf,IAAI,sBADtDT,8BAEGzB,IAAI,CAAC4B,IAAI,GAEd;4CACA,MAAM,IAAIU,MACR3B,aACEe,aACA,CAAC,8BAA8B,CAAC;wCAGtC;oCACF;gCACF;4BACF;4BAEA,KAAK,MAAMe,eAAeG,aAAc;gCACtC,IACE,CAAChD,WAAWoC,YAAY,CAACS,YAAYU,EAAE,EAAE;oCACvCjB,MAAMpC;gCACR,IACA;oCACA;gCACF;gCAEA,IAAI,EAAEsD,IAAI,EAAE,GAAGX;gCACf,IAAI7C,WAAWyD,gBAAgB,CAACD,OAAO;oCACrCA,OAAOA,KAAKE,UAAU;gCACxB;gCAEA,IAAI,CAAC1D,WAAW2D,kBAAkB,CAACH,OAAO;oCACxC,MAAMI,MAAMJ,OAAOA,KAAKK,IAAI,GAAG;oCAC/B,MAAM,IAAInB,MACR3B,aACEe,aACA,CAAC,wBAAwB,EAAE8B,IAAI,CAAC;gCAGtC;gCAEA,KAAK,MAAME,QAAQN,KAAKO,UAAU,CAAE;oCAClC,IAAI/D,WAAWgE,eAAe,CAACF,OAAO;wCACpC,MAAM,IAAIpB,MACR3B,aACEe,aACA,CAAC,8BAA8B,CAAC;oCAGtC;oCACA,MAAM,EAAEQ,IAAI,EAAE,GAAGwB,KAAKG,GAAG;oCACzB,IAAIjE,WAAWoC,YAAY,CAAC0B,KAAKG,GAAG,EAAE;wCAAE3B,MAAM;oCAAM,IAAI;wCACtD,IAAI,CAACtC,WAAWkE,gBAAgB,CAACJ,OAAO;4CACtC,MAAM,IAAIpB,MACR3B,aACEe,aACA,CAAC,kBAAkB,EAAEQ,KAAK,CAAC,CAAC;wCAGlC;wCACA,IACE,CAACtC,WAAWmE,gBAAgB,CAACL,KAAKvB,KAAK,KACvC,CAACvC,WAAWwC,eAAe,CAACsB,KAAKvB,KAAK,GACtC;4CACA,MAAM,IAAIG,MACR3B,aACEe,aACA,CAAC,mBAAmB,EAAEQ,KAAK,CAAC,CAAC;wCAGnC;wCACAS,OAAOqB,GAAG,GAAGN,KAAKvB,KAAK,CAACA,KAAK;oCAC/B;gCACF;4BACF;4BAEA,IAAIQ,OAAOqB,GAAG,KAAK,MAAM;oCAClBtC,wBAAAA;gCAAL,IAAI,GAACA,oBAAAA,YAAYuC,IAAI,sBAAhBvC,yBAAAA,kBAAkBwC,IAAI,qBAAtBxC,uBAAwByC,MAAM,CAACC,KAAK,GAAE;oCACzC,uDAAuD;oCACvD,wDAAwD;oCACxDrE,cAAc0B,YAAYxB;gCAC5B;gCACAyB,YAAYc,aAAa,GAAG;gCAC5B;4BACF;wBACF;oBACF,GACA5B;gBAEJ;YACF;QACF;IACF;AACF"}