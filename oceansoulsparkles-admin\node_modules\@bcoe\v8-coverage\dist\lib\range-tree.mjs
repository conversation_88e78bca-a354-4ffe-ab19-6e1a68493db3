export class RangeTree {
    constructor(start, end, delta, children) {
        this.start = start;
        this.end = end;
        this.delta = delta;
        this.children = children;
    }
    /**
     * @precodition `ranges` are well-formed and pre-order sorted
     */
    static fromSortedRanges(ranges) {
        let root;
        // Stack of parent trees and parent counts.
        const stack = [];
        for (const range of ranges) {
            const node = new RangeTree(range.startOffset, range.endOffset, range.count, []);
            if (root === undefined) {
                root = node;
                stack.push([node, range.count]);
                continue;
            }
            let parent;
            let parentCount;
            while (true) {
                [parent, parentCount] = stack[stack.length - 1];
                // assert: `top !== undefined` (the ranges are sorted)
                if (range.startOffset < parent.end) {
                    break;
                }
                else {
                    stack.pop();
                }
            }
            node.delta -= parentCount;
            parent.children.push(node);
            stack.push([node, range.count]);
        }
        return root;
    }
    normalize() {
        const children = [];
        let curEnd;
        let head;
        const tail = [];
        for (const child of this.children) {
            if (head === undefined) {
                head = child;
            }
            else if (child.delta === head.delta && child.start === curEnd) {
                tail.push(child);
            }
            else {
                endChain();
                head = child;
            }
            curEnd = child.end;
        }
        if (head !== undefined) {
            endChain();
        }
        if (children.length === 1) {
            const child = children[0];
            if (child.start === this.start && child.end === this.end) {
                this.delta += child.delta;
                this.children = child.children;
                // `.lazyCount` is zero for both (both are after normalization)
                return;
            }
        }
        this.children = children;
        function endChain() {
            if (tail.length !== 0) {
                head.end = tail[tail.length - 1].end;
                for (const tailTree of tail) {
                    for (const subChild of tailTree.children) {
                        subChild.delta += tailTree.delta - head.delta;
                        head.children.push(subChild);
                    }
                }
                tail.length = 0;
            }
            head.normalize();
            children.push(head);
        }
    }
    /**
     * @precondition `tree.start < value && value < tree.end`
     * @return RangeTree Right part
     */
    split(value) {
        let leftChildLen = this.children.length;
        let mid;
        // TODO(perf): Binary search (check overhead)
        for (let i = 0; i < this.children.length; i++) {
            const child = this.children[i];
            if (child.start < value && value < child.end) {
                mid = child.split(value);
                leftChildLen = i + 1;
                break;
            }
            else if (child.start >= value) {
                leftChildLen = i;
                break;
            }
        }
        const rightLen = this.children.length - leftChildLen;
        const rightChildren = this.children.splice(leftChildLen, rightLen);
        if (mid !== undefined) {
            rightChildren.unshift(mid);
        }
        const result = new RangeTree(value, this.end, this.delta, rightChildren);
        this.end = value;
        return result;
    }
    /**
     * Get the range coverages corresponding to the tree.
     *
     * The ranges are pre-order sorted.
     */
    toRanges() {
        const ranges = [];
        // Stack of parent trees and counts.
        const stack = [[this, 0]];
        while (stack.length > 0) {
            const [cur, parentCount] = stack.pop();
            const count = parentCount + cur.delta;
            ranges.push({ startOffset: cur.start, endOffset: cur.end, count });
            for (let i = cur.children.length - 1; i >= 0; i--) {
                stack.push([cur.children[i], count]);
            }
        }
        return ranges;
    }
}

//# sourceMappingURL=data:application/json;charset=utf8;base64,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
