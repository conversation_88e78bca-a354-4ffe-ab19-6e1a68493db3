"use strict";(()=>{var e={};e.id=800,e.ids=[800,660],e.modules={4019:(e,o,r)=>{r.a(e,async(e,s)=>{try{r.r(o),r.d(o,{config:()=>f,default:()=>O,getServerSideProps:()=>N,getStaticPaths:()=>m,getStaticProps:()=>h,reportWebVitals:()=>j,routeModule:()=>p,unstable_getServerProps:()=>U,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>E,unstable_getStaticPaths:()=>_,unstable_getStaticProps:()=>D});var t=r(7093),n=r(5244),c=r(1323),a=r(2899),l=r.n(a),d=r(3414),i=r(9434),u=e([d,i]);[d,i]=u.then?(await u)():u;let O=(0,c.l)(i,"default"),h=(0,c.l)(i,"getStaticProps"),m=(0,c.l)(i,"getStaticPaths"),N=(0,c.l)(i,"getServerSideProps"),f=(0,c.l)(i,"config"),j=(0,c.l)(i,"reportWebVitals"),D=(0,c.l)(i,"unstable_getStaticProps"),_=(0,c.l)(i,"unstable_getStaticPaths"),E=(0,c.l)(i,"unstable_getStaticParams"),U=(0,c.l)(i,"unstable_getServerProps"),v=(0,c.l)(i,"unstable_getServerSideProps"),p=new t.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/shop",pathname:"/shop",bundlePath:"",filename:""},components:{App:d.default,Document:l()},userland:i});s()}catch(e){s(e)}})},1398:(e,o,r)=>{r.d(o,{Z:()=>i});var s=r(997),t=r(968),n=r.n(t),c=r(1664),a=r.n(c),l=r(6689);!function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}();var d=r(1163);function i({children:e}){let[o,r]=(0,l.useState)(!1),[t,c]=(0,l.useState)(!1),[i,u]=(0,l.useState)(!1),O=(0,d.useRouter)(),h=[{label:"Home",href:"/"},{label:"About Us",href:"/about"},{label:"Services",href:"/services"},{label:"Gallery",href:"/gallery"},{label:"Shop",href:"/shop"},{label:"Gift Card",href:"/gift-card"},{label:"Contact",href:"/contact"}],{asPath:m}=O,N=`https://www.oceansoulsparkles.com.au${m}`,f=(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(n(),{children:s.jsx("link",{rel:"canonical",href:N})}),s.jsx(Object(function(){var e=Error("Cannot find module './GoogleVerification'");throw e.code="MODULE_NOT_FOUND",e}()),{}),s.jsx(Object(function(){var e=Error("Cannot find module './StructuredData/SchemaManager'");throw e.code="MODULE_NOT_FOUND",e}()),{}),s.jsx("header",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${i?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,children:(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(a(),{href:"/",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/images/bannerlogo.PNG",alt:"Ocean Soul Sparkles"})}),(0,s.jsxs)("button",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${t?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,onClick:()=>{c(!t)},"aria-label":"Toggle Navigation",children:[s.jsx("span",{}),s.jsx("span",{}),s.jsx("span",{})]}),s.jsx("nav",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${t?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,children:s.jsx("ul",{children:h.map((e,o)=>s.jsx("li",{className:O.pathname===e.href?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):"",children:s.jsx(a(),{href:e.href,children:e.label})},o))})}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx(Object(function(){var e=Error("Cannot find module './SparkleButton'");throw e.code="MODULE_NOT_FOUND",e}()),{href:"/book-online",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Book Now"})})]})}),e,s.jsx(Object(function(){var e=Error("Cannot find module './NotificationPrompt'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,s.jsxs)("footer",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 320",preserveAspectRatio:"none",children:s.jsx("path",{fill:"#4ECDC4",fillOpacity:"0.2",d:"M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"})})}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(a(),{href:"/",children:s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"OceanSoulSparkles"})}),s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{className:"accent-text",children:'"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!"'})}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("a",{href:"https://www.instagram.com/oceansoulsparkles",target:"_blank",rel:"noopener noreferrer",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/images/social/instagram-icon.png",alt:"Instagram"})}),s.jsx("a",{href:"https://www.facebook.com/OceanSoulSparkles/",target:"_blank",rel:"noopener noreferrer",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/images/social/facebook-icon.png",alt:"Facebook"})})]})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("h3",{children:"Quick Links"}),(0,s.jsxs)("ul",{children:[h.map((e,o)=>s.jsx("li",{children:s.jsx(a(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.label})},o)),s.jsx("li",{children:s.jsx(a(),{href:"/book-online",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Book Online"})}),s.jsx("li",{children:s.jsx(a(),{href:"/book-events",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Events Booking"})}),s.jsx("li",{children:s.jsx(a(),{href:"/policies#return-policy",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Return & Refund Policy"})}),s.jsx("li",{children:s.jsx(a(),{href:"/policies#shipping-info",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Shipping Information"})})]})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("h3",{children:"Contact Us"}),s.jsx("p",{children:s.jsx("a",{href:"mailto:<EMAIL>",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"<EMAIL>"})}),s.jsx("p",{children:"Melbourne, Victoria"}),s.jsx(a(),{href:"/contact",className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} button button--outline`,children:"Get in Touch"})]})]})}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("h3",{children:"Payment Methods"}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),style:{justifyContent:"center"},children:s.jsx("img",{src:"/images/logos/square.png",alt:"Square Payments",width:150,height:40,style:{margin:"0 auto"}})}),(0,s.jsxs)("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z",fill:"#4CAF50"})}),s.jsx("span",{children:"Secure payment processing"})]})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," by OceanSoulSparkles. All rights reserved."]}),s.jsx("p",{children:"Proudly created with Next.js"})]}),o&&s.jsx("button",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Scroll to top",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:s.jsx("polyline",{points:"18 15 12 9 6 15"})})})]})]});return s.jsx(Object(function(){var e=Error("Cannot find module './OneSignalProvider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:f})}(function(){var e=Error("Cannot find module './SparkleButton'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module './StructuredData/SchemaManager'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './NotificationPrompt'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './OneSignalProvider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './GoogleVerification'");throw e.code="MODULE_NOT_FOUND",e}()},9815:(e,o,r)=>{r.a(e,async(e,s)=>{try{r.d(o,{Z:()=>l});var t=r(997),n=r(6689);r(1664);var c=r(3590);!function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}();var a=e([c]);function l({product:e}){let[o,r]=(0,n.useState)(!1),[s,a]=(0,n.useState)(!1),l=async()=>{a(!0);try{await new Promise(e=>setTimeout(e,500)),c.toast.success(`${e.name} added to cart!`)}catch(e){console.error("Error adding to cart:",e),c.toast.error("Failed to add item to cart")}finally{a(!1)}},d=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),i=0===e.stock_quantity?{status:"out-of-stock",text:"Out of Stock",color:"#dc3545"}:e.stock_quantity<=5?{status:"low-stock",text:`Only ${e.stock_quantity} left`,color:"#fd7e14"}:{status:"in-stock",text:"In Stock",color:"#28a745"};return(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[!o&&e.image_url?t.jsx("img",{src:e.image_url,alt:e.name,className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),onError:()=>{r(!0)}}):t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("span",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"\uD83C\uDFA8"})}),t.jsx("div",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}())[i.status]}`,style:{backgroundColor:i.color},children:i.text}),t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("button",{onClick:()=>{c.toast.info("Quick view feature coming soon!")},className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),"aria-label":"Quick view",children:"\uD83D\uDC41️"})})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h3",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.name}),e.category_name&&t.jsx("span",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.category_name})]}),t.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.description}),e.features&&e.features.length>0&&t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("ul",{children:e.features.slice(0,2).map((e,o)=>t.jsx("li",{children:e},o))})}),e.ingredients&&(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("span",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Made with:"}),t.jsx("span",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.ingredients})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("span",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:d(e.price)}),e.original_price&&e.original_price>e.price&&t.jsx("span",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:d(e.original_price)})]}),t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.stock_quantity>0?t.jsx("button",{onClick:l,disabled:s,className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s?(0,t.jsxs)(t.Fragment,{children:[t.jsx("span",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),"Adding..."]}):"Add to Cart"}):t.jsx("button",{disabled:!0,className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Out of Stock"})})]}),e.is_eco_friendly&&(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("span",{className:Object(function(){var e=Error("Cannot find module '@/styles/ProductCard.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"\uD83C\uDF3F"}),t.jsx("span",{children:"Eco-Friendly"})]})]})]})}c=(a.then?(await a)():a)[0],s()}catch(e){s(e)}})},9434:(e,o,r)=>{r.a(e,async(e,s)=>{try{r.r(o),r.d(o,{default:()=>O});var t=r(997),n=r(6689),c=r(968),a=r.n(c),l=r(1398),d=r(9815);r(2732);var i=r(3590);!function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}();var u=e([d,i]);function O(){let[e,o]=(0,n.useState)([]),[r,s]=(0,n.useState)(!0),[c,i]=(0,n.useState)("all"),[u,O]=(0,n.useState)(""),h=["all",...new Set(e.map(e=>e.category_name))],m=e.filter(e=>e.name.toLowerCase().includes(u.toLowerCase())||e.description.toLowerCase().includes(u.toLowerCase()));return(0,t.jsxs)(l.Z,{children:[(0,t.jsxs)(a(),{children:[t.jsx("title",{children:"Shop - Ocean Soul Sparkles"}),t.jsx("meta",{name:"description",content:"Shop eco-friendly glitter, face paints, and beauty products from Ocean Soul Sparkles. Biodegradable glitter made from eucalyptus trees."}),t.jsx("meta",{name:"keywords",content:"eco-friendly glitter, biodegradable glitter, face paint, beauty products, sustainable makeup, Melbourne"})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h1",{children:"Eco-Friendly Beauty Shop"}),t.jsx("p",{children:"Discover our range of sustainable, biodegradable beauty products"})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("input",{type:"text",placeholder:"Search products...",value:u,onChange:e=>O(e.target.value),className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),(0,t.jsxs)("svg",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[t.jsx("circle",{cx:"11",cy:"11",r:"8"}),t.jsx("path",{d:"m21 21-4.35-4.35"})]})]}),h.length>1&&t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("select",{value:c,onChange:e=>i(e.target.value),className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:h.map(e=>t.jsx("option",{value:e,children:"all"===e?"All Categories":e},e))})})]}),t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:r?(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),t.jsx("p",{children:"Loading products..."})]}):m.length>0?t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:m.map(e=>t.jsx(d.Z,{product:e},e.id))}):(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"\uD83D\uDECD️"}),t.jsx("h3",{children:"No products found"}),t.jsx("p",{children:u?`No products match "${u}"`:"all"===c?"No products are currently available.":`No products found in the "${c}" category.`}),(u||"all"!==c)&&t.jsx("button",{onClick:()=>{O(""),i("all")},className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Clear Filters"})]})}),t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"\uD83C\uDF3F"}),t.jsx("h3",{children:"100% Biodegradable"}),t.jsx("p",{children:"Our glitter is made from eucalyptus trees and breaks down naturally in just 6 weeks in soil."})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"\uD83D\uDC30"}),t.jsx("h3",{children:"Cruelty-Free & Vegan"}),t.jsx("p",{children:"All our products are never tested on animals and contain no animal-derived ingredients."})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"\uD83C\uDF0A"}),t.jsx("h3",{children:"Ocean-Safe"}),t.jsx("p",{children:"Our biodegradable products won't harm marine life or pollute waterways."})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"✨"}),t.jsx("h3",{children:"Professional Quality"}),t.jsx("p",{children:"Used by professional artists worldwide for stunning, long-lasting results."})]})]})}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h3",{children:"Shipping & Returns"}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h4",{children:"\uD83D\uDE9A Fast Shipping"}),t.jsx("p",{children:"Free shipping on orders over $50. Express delivery available."})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h4",{children:"↩️ Easy Returns"}),t.jsx("p",{children:"30-day return policy on unopened products. Customer satisfaction guaranteed."})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h4",{children:"\uD83D\uDCB3 Secure Payments"}),t.jsx("p",{children:"All payments processed securely through Square. Major credit cards accepted."})]})]})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h3",{children:"Need Something Special?"}),t.jsx("p",{children:"Looking for custom colors or bulk orders? We'd love to help create something unique for you!"}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Shop.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("a",{href:"/contact",className:"button",children:"Contact Us"}),t.jsx("a",{href:"mailto:<EMAIL>",className:"button button--outline",children:"Email Us"})]})]})]})]})}[d,i]=u.then?(await u)():u,s()}catch(e){s(e)}})},2885:e=>{e.exports=require("@supabase/supabase-js")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},3590:e=>{e.exports=import("react-toastify")}};var o=require("../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),s=o.X(0,[899,65,414],()=>r(4019));module.exports=s})();