import Link from 'next/link';
import { useState } from 'react';

export default function SparkleButton({ 
  href, 
  children, 
  className = '', 
  onClick,
  disabled = false,
  type = 'button'
}) {
  const [isHovered, setIsHovered] = useState(false);

  const buttonStyle = {
    position: 'relative',
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    padding: '12px 24px',
    background: 'linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%)',
    color: 'white',
    border: 'none',
    borderRadius: '25px',
    fontSize: '16px',
    fontWeight: '600',
    textDecoration: 'none',
    cursor: disabled ? 'not-allowed' : 'pointer',
    transition: 'all 0.3s ease',
    overflow: 'hidden',
    boxShadow: '0 4px 15px rgba(55, 136, 216, 0.3)',
    opacity: disabled ? 0.6 : 1,
    transform: isHovered && !disabled ? 'translateY(-2px)' : 'translateY(0)',
    ...(isHovered && !disabled && {
      boxShadow: '0 6px 20px rgba(55, 136, 216, 0.4)',
    })
  };

  const sparkleStyle = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: '100%',
    height: '100%',
    background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',
    borderRadius: '50%',
    transform: 'translate(-50%, -50%) scale(0)',
    transition: 'transform 0.6s ease',
    ...(isHovered && !disabled && {
      transform: 'translate(-50%, -50%) scale(2)',
    })
  };

  const content = (
    <span 
      style={buttonStyle}
      className={className}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
      disabled={disabled}
      type={type}
    >
      <span style={sparkleStyle}></span>
      <span style={{ position: 'relative', zIndex: 1 }}>
        {children}
      </span>
      {isHovered && !disabled && (
        <span style={{ 
          position: 'relative', 
          zIndex: 1,
          marginLeft: '4px',
          animation: 'sparkle 0.6s ease-in-out'
        }}>
          ✨
        </span>
      )}
      <style jsx>{`
        @keyframes sparkle {
          0% { opacity: 0; transform: scale(0.5); }
          50% { opacity: 1; transform: scale(1.2); }
          100% { opacity: 0; transform: scale(1); }
        }
      `}</style>
    </span>
  );

  if (href && !disabled) {
    return (
      <Link href={href} style={{ textDecoration: 'none' }}>
        {content}
      </Link>
    );
  }

  return content;
}
