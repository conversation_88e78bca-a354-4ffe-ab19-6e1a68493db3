"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[74],{5074:function(n,e,r){r.r(e);let i=[],t=!1,o=!1,s=()=>!!window.OneSignal,a=(n,e)=>{t=!0,window.OneSignal=window.OneSignal||[],window.OneSignal.push(()=>{window.OneSignal.init(e)}),window.OneSignal.push(()=>{w(),n()})},u=n=>{o=!0,w(),n()},w=()=>{i.forEach(n=>{let{name:e,args:r,promiseResolver:i}=n;i?h[e](...r).then(n=>{i(n)}):h[e](...r)})},h={init:n=>new Promise(e=>{if(t){e();return}if(!n||!n.appId)throw Error("You need to provide your OneSignal appId.");if(!document){e();return}let r=document.createElement("script");r.id="onesignal-sdk",r.src="https://cdn.onesignal.com/sdks/OneSignalSDK.js",r.async=!0,r.onload=()=>{a(e,n)},r.onerror=()=>{u(e)},document.head.appendChild(r)}),on:function(n,e){if(!s()){i.push({name:"on",args:arguments});return}window.OneSignal.push(()=>{window.OneSignal.on(n,e)})},off:function(n,e){if(!s()){i.push({name:"off",args:arguments});return}window.OneSignal.push(()=>{window.OneSignal.off(n,e)})},once:function(n,e){if(!s()){i.push({name:"once",args:arguments});return}window.OneSignal.push(()=>{window.OneSignal.once(n,e)})},isPushNotificationsEnabled:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"isPushNotificationsEnabled",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.isPushNotificationsEnabled(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},showHttpPrompt:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"showHttpPrompt",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.showHttpPrompt(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},registerForPushNotifications:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"registerForPushNotifications",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.registerForPushNotifications(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},setDefaultNotificationUrl:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"setDefaultNotificationUrl",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.setDefaultNotificationUrl(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},setDefaultTitle:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"setDefaultTitle",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.setDefaultTitle(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},getTags:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"getTags",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.getTags(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},sendTag:function(n,e,r){return new Promise((t,a)=>{if(o){t();return}if(!s()){i.push({name:"sendTag",args:arguments,promiseResolver:t});return}try{window.OneSignal.push(()=>{window.OneSignal.sendTag(n,e,r).then(n=>t(n)).catch(n=>a(n))})}catch(n){a(n)}})},sendTags:function(n,e){return new Promise((r,t)=>{if(o){r();return}if(!s()){i.push({name:"sendTags",args:arguments,promiseResolver:r});return}try{window.OneSignal.push(()=>{window.OneSignal.sendTags(n,e).then(n=>r(n)).catch(n=>t(n))})}catch(n){t(n)}})},deleteTag:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"deleteTag",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.deleteTag(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},deleteTags:function(n,e){return new Promise((r,t)=>{if(o){r();return}if(!s()){i.push({name:"deleteTags",args:arguments,promiseResolver:r});return}try{window.OneSignal.push(()=>{window.OneSignal.deleteTags(n,e).then(n=>r(n)).catch(n=>t(n))})}catch(n){t(n)}})},addListenerForNotificationOpened:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"addListenerForNotificationOpened",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.addListenerForNotificationOpened(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},setSubscription:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"setSubscription",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.setSubscription(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},showHttpPermissionRequest:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"showHttpPermissionRequest",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.showHttpPermissionRequest(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},showNativePrompt:function(){return new Promise((n,e)=>{if(o){n();return}if(!s()){i.push({name:"showNativePrompt",args:arguments,promiseResolver:n});return}try{window.OneSignal.push(()=>{window.OneSignal.showNativePrompt().then(e=>n(e)).catch(n=>e(n))})}catch(n){e(n)}})},showSlidedownPrompt:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"showSlidedownPrompt",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.showSlidedownPrompt(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},showCategorySlidedown:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"showCategorySlidedown",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.showCategorySlidedown(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},showSmsSlidedown:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"showSmsSlidedown",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.showSmsSlidedown(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},showEmailSlidedown:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"showEmailSlidedown",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.showEmailSlidedown(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},showSmsAndEmailSlidedown:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"showSmsAndEmailSlidedown",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.showSmsAndEmailSlidedown(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},getNotificationPermission:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"getNotificationPermission",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.getNotificationPermission(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},getUserId:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"getUserId",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.getUserId(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},getSubscription:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"getSubscription",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.getSubscription(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},setEmail:function(n,e){return new Promise((r,t)=>{if(o){r();return}if(!s()){i.push({name:"setEmail",args:arguments,promiseResolver:r});return}try{window.OneSignal.push(()=>{window.OneSignal.setEmail(n,e).then(n=>r(n)).catch(n=>t(n))})}catch(n){t(n)}})},setSMSNumber:function(n,e){return new Promise((r,t)=>{if(o){r();return}if(!s()){i.push({name:"setSMSNumber",args:arguments,promiseResolver:r});return}try{window.OneSignal.push(()=>{window.OneSignal.setSMSNumber(n,e).then(n=>r(n)).catch(n=>t(n))})}catch(n){t(n)}})},logoutEmail:function(){return new Promise((n,e)=>{if(o){n();return}if(!s()){i.push({name:"logoutEmail",args:arguments,promiseResolver:n});return}try{window.OneSignal.push(()=>{window.OneSignal.logoutEmail().then(e=>n(e)).catch(n=>e(n))})}catch(n){e(n)}})},logoutSMS:function(){return new Promise((n,e)=>{if(o){n();return}if(!s()){i.push({name:"logoutSMS",args:arguments,promiseResolver:n});return}try{window.OneSignal.push(()=>{window.OneSignal.logoutSMS().then(e=>n(e)).catch(n=>e(n))})}catch(n){e(n)}})},setExternalUserId:function(n,e){return new Promise((r,t)=>{if(o){r();return}if(!s()){i.push({name:"setExternalUserId",args:arguments,promiseResolver:r});return}try{window.OneSignal.push(()=>{window.OneSignal.setExternalUserId(n,e).then(n=>r(n)).catch(n=>t(n))})}catch(n){t(n)}})},removeExternalUserId:function(){return new Promise((n,e)=>{if(o){n();return}if(!s()){i.push({name:"removeExternalUserId",args:arguments,promiseResolver:n});return}try{window.OneSignal.push(()=>{window.OneSignal.removeExternalUserId().then(e=>n(e)).catch(n=>e(n))})}catch(n){e(n)}})},getExternalUserId:function(){return new Promise((n,e)=>{if(o){n();return}if(!s()){i.push({name:"getExternalUserId",args:arguments,promiseResolver:n});return}try{window.OneSignal.push(()=>{window.OneSignal.getExternalUserId().then(e=>n(e)).catch(n=>e(n))})}catch(n){e(n)}})},provideUserConsent:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"provideUserConsent",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.provideUserConsent(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},getEmailId:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"getEmailId",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.getEmailId(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},getSMSId:function(n){return new Promise((e,r)=>{if(o){e();return}if(!s()){i.push({name:"getSMSId",args:arguments,promiseResolver:e});return}try{window.OneSignal.push(()=>{window.OneSignal.getSMSId(n).then(n=>e(n)).catch(n=>r(n))})}catch(n){r(n)}})},sendOutcome:function(n,e){return new Promise((r,t)=>{if(o){r();return}if(!s()){i.push({name:"sendOutcome",args:arguments,promiseResolver:r});return}try{window.OneSignal.push(()=>{window.OneSignal.sendOutcome(n,e).then(n=>r(n)).catch(n=>t(n))})}catch(n){t(n)}})}};e.default=h}}]);