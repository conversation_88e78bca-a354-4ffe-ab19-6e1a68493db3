/* Service Selector Styles */
.serviceSelectorContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.categoryFilter {
  margin-bottom: 40px;
  text-align: center;
}

.categoryFilter h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.categoryButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.categoryButton {
  padding: 10px 20px;
  border: 2px solid #e9ecef;
  background: white;
  color: #6c757d;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.categoryButton:hover {
  border-color: #3788d8;
  color: #3788d8;
}

.categoryButton.active {
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  border-color: #3788d8;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 32px;
  margin-bottom: 40px;
}

.serviceCard {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f8f9fa;
}

.serviceCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.serviceImage {
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.serviceImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.serviceCard:hover .serviceImage img {
  transform: scale(1.05);
}

.serviceContent {
  padding: 24px;
}

.serviceHeader {
  margin-bottom: 16px;
}

.serviceName {
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.serviceCategory {
  background: linear-gradient(135deg, #3788d8, #2c6cb7);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.serviceDescription {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 20px;
  font-size: 14px;
}

.serviceDetails {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.serviceDetail {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.detailIcon {
  font-size: 16px;
}

.serviceFeatures {
  margin-bottom: 16px;
}

.serviceFeatures h4 {
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.serviceFeatures ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.serviceFeatures li {
  color: #6c757d;
  font-size: 13px;
  margin-bottom: 4px;
  padding-left: 16px;
  position: relative;
}

.serviceFeatures li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

.suitableFor {
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3788d8;
}

.suitableLabel {
  font-weight: 600;
  color: #2c3e50;
  font-size: 13px;
  display: block;
  margin-bottom: 4px;
}

.suitableText {
  color: #6c757d;
  font-size: 13px;
}

.selectButton {
  width: 100%;
  padding: 12px 24px;
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selectButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #3788d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.noServices {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.noServicesIcon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.noServices h3 {
  color: #495057;
  margin-bottom: 12px;
}

.noServices p {
  margin-bottom: 24px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.showAllButton {
  padding: 10px 20px;
  background: transparent;
  color: #3788d8;
  border: 2px solid #3788d8;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.showAllButton:hover {
  background: #3788d8;
  color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .serviceSelectorContainer {
    padding: 16px;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .categoryButtons {
    gap: 8px;
  }

  .categoryButton {
    padding: 8px 16px;
    font-size: 13px;
  }

  .serviceDetails {
    flex-direction: column;
    gap: 8px;
  }

  .serviceCard {
    margin: 0 8px;
  }
}

@media (max-width: 480px) {
  .serviceContent {
    padding: 20px;
  }

  .serviceName {
    font-size: 1.2rem;
  }

  .categoryButtons {
    flex-direction: column;
    align-items: center;
  }

  .categoryButton {
    width: 200px;
  }
}
