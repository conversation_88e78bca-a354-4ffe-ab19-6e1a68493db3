{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/path-match.ts"], "names": ["getPathMatch", "path", "options", "keys", "regexp", "pathToRegexp", "delimiter", "sensitive", "strict", "matcher", "regexpToFunction", "regexModifier", "RegExp", "source", "flags", "pathname", "params", "match", "removeUnnamedP<PERSON>ms", "key", "name"], "mappings": ";;;;+BAqCgBA;;;eAAAA;;;8BApCa;AAoCtB,SAASA,aAAaC,IAAY,EAAEC,OAAiB;IAC1D,MAAMC,OAAc,EAAE;IACtB,MAAMC,SAASC,IAAAA,0BAAY,EAACJ,MAAME,MAAM;QACtCG,WAAW;QACXC,WACE,QAAOL,2BAAAA,QAASK,SAAS,MAAK,YAAYL,QAAQK,SAAS,GAAG;QAChEC,MAAM,EAAEN,2BAAAA,QAASM,MAAM;IACzB;IAEA,MAAMC,UAAUC,IAAAA,8BAAgB,EAC9BR,CAAAA,2BAAAA,QAASS,aAAa,IAClB,IAAIC,OAAOV,QAAQS,aAAa,CAACP,OAAOS,MAAM,GAAGT,OAAOU,KAAK,IAC7DV,QACJD;IAGF;;;;;GAKC,GACD,OAAO,CAACY,UAAUC;QAChB,+CAA+C;QAC/C,IAAI,OAAOD,aAAa,UAAU,OAAO;QAEzC,MAAME,QAAQR,QAAQM;QAEtB,sDAAsD;QACtD,IAAI,CAACE,OAAO,OAAO;QAEnB;;;;KAIC,GACD,IAAIf,2BAAAA,QAASgB,mBAAmB,EAAE;YAChC,KAAK,MAAMC,OAAOhB,KAAM;gBACtB,IAAI,OAAOgB,IAAIC,IAAI,KAAK,UAAU;oBAChC,OAAOH,MAAMD,MAAM,CAACG,IAAIC,IAAI,CAAC;gBAC/B;YACF;QACF;QAEA,OAAO;YAAE,GAAGJ,MAAM;YAAE,GAAGC,MAAMD,MAAM;QAAC;IACtC;AACF"}