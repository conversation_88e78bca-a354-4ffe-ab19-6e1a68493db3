{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-no-store.ts"], "names": ["unstable_noStore", "callingExpression", "store", "staticGenerationAsyncStorage", "getStore", "forceStatic", "isUnstableNoStore", "markCurrentScopeAsDynamic"], "mappings": ";;;;+BAkBgBA;;;eAAAA;;;sDAlB6B;kCACH;AAiBnC,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,QAAQC,kEAA4B,CAACC,QAAQ;IACnD,IAAI,CAACF,OAAO;QACV,6FAA6F;QAC7F,6FAA6F;QAC7F,uEAAuE;QACvE;IACF,OAAO,IAAIA,MAAMG,WAAW,EAAE;QAC5B;IACF,OAAO;QACLH,MAAMI,iBAAiB,GAAG;QAC1BC,IAAAA,2CAAyB,EAACL,OAAOD;IACnC;AACF"}