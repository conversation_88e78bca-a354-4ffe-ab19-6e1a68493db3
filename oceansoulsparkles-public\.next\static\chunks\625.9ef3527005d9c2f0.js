"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[625],{3625:function(n,i,e){e.r(i),e.d(i,{getNotificationPermission:function(){return f},getUserId:function(){return s},initializeOneSignalPublic:function(){return o},isSubscribed:function(){return c},requestNotificationPermission:function(){return r},sendCustomerTags:function(){return a},sendTestNotification:function(){return l},unsubscribe:function(){return u}});let t=null;async function o(){try{t=(await e.e(74).then(e.bind(e,5074))).default,await t.init({appId:"687cfeae-1fec-4240-b094-1ce79ebcee51",safari_web_id:"web.onesignal.auto.************************************",allowLocalhostAsSecureOrigin:!1,autoRegister:!1,autoResubscribe:!0,persistNotification:!1,promptOptions:{slidedown:{enabled:!1},fullscreen:{enabled:!1}},welcomeNotification:{disable:!0},notificationClickHandlerMatch:"origin",notificationClickHandlerAction:"navigate"}),console.log("[OneSignal Public] Initialized successfully"),t&&(t.on("subscriptionChange",function(n){console.log("[OneSignal Public] Subscription changed:",n),n&&(t.sendTag("user_type","customer"),t.sendTag("source","public_website"),t.sendTag("subscribed_at",new Date().toISOString()))}),t.on("notificationPermissionChange",function(n){console.log("[OneSignal Public] Permission changed:",n)}),t.on("notificationDisplay",function(n){console.log("[OneSignal Public] Notification displayed:",n)}),t.on("notificationDismiss",function(n){console.log("[OneSignal Public] Notification dismissed:",n)}))}catch(n){throw console.error("[OneSignal Public] Initialization failed:",n),n}}async function r(){if(!t)throw Error("OneSignal not initialized");try{let n=await t.registerForPushNotifications();return console.log("[OneSignal Public] Permission granted:",n),n}catch(n){throw console.error("[OneSignal Public] Permission request failed:",n),n}}async function c(){if(!t)return!1;try{return await t.isPushNotificationsEnabled()}catch(n){return console.error("[OneSignal Public] Error checking subscription:",n),!1}}async function s(){if(!t)return null;try{return await t.getUserId()}catch(n){return console.error("[OneSignal Public] Error getting user ID:",n),null}}async function a(n){if(t)try{let i={customer_id:n.customer_id,email:n.email,first_name:n.first_name,booking_preferences:n.booking_preferences,location:n.location,last_booking:n.last_booking},e=Object.fromEntries(Object.entries(i).filter(n=>{let[i,e]=n;return void 0!==e}));await t.sendTags(e),console.log("[OneSignal Public] Customer tags sent:",e)}catch(n){console.error("[OneSignal Public] Error sending tags:",n)}}async function l(){}async function u(){if(t)try{await t.setSubscription(!1),console.log("[OneSignal Public] Unsubscribed successfully")}catch(n){throw console.error("[OneSignal Public] Error unsubscribing:",n),n}}async function f(){if(!t)return"default";try{return await t.getNotificationPermission()}catch(n){return console.error("[OneSignal Public] Error getting permission:",n),"default"}}}}]);