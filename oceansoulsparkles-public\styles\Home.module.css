/* Home Page Styles */
.main {
  padding-top: 80px; /* Account for fixed header */
}

.services {
  padding: 80px 0;
  background: white;
}

.sectionTitle {
  text-align: center;
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 16px;
}

.sectionDescription {
  text-align: center;
  font-size: 1.2rem;
  color: #6c757d;
  margin-bottom: 60px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.serviceGrid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.serviceCard {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.serviceCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.serviceImage {
  width: 100%;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 24px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.serviceImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.serviceCard h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 16px;
}

.serviceCard p {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 24px;
}

.serviceLink {
  display: inline-block;
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
}

.serviceLink:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(55, 136, 216, 0.3);
}

.servicesLink {
  text-align: center;
  margin-top: 60px;
}

.ecoFriendly {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.ecoContent {
  padding: 0 40px;
}

.ecoTitle {
  font-size: 2.2rem;
  color: #2c3e50;
  margin-bottom: 24px;
}

.ecoDescription {
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 20px;
}

.ecoImage {
  padding: 0 40px;
}

.ecoImg {
  width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.gallery {
  padding: 80px 0;
  background: white;
}

.galleryGrid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.galleryItem {
  position: relative;
  aspect-ratio: 1;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.galleryItem:hover {
  transform: scale(1.05);
}

.galleryImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.galleryOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.galleryItem:hover .galleryOverlay {
  transform: translateY(0);
}

.galleryLink {
  text-align: center;
  margin-top: 60px;
}

.contact {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.contactContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.contactInfo {
  padding: 40px;
}

.contactCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.contactCard h3 {
  color: white;
  margin-bottom: 16px;
}

.contactCard p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 24px;
}

.contactDetail {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  color: rgba(255, 255, 255, 0.9);
}

.contactDetail svg {
  color: #4ECDC4;
}

.contactDetail a {
  color: #4ECDC4;
  text-decoration: none;
}

.contactSocial {
  display: flex;
  gap: 16px;
  margin-top: 24px;
}

.contactSocial img {
  width: 32px;
  height: 32px;
  transition: transform 0.2s ease;
}

.contactSocial a:hover img {
  transform: scale(1.1);
}

.contactFormContainer {
  padding: 40px;
}

.contactForm {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  color: white;
  margin-bottom: 8px;
  font-weight: 600;
}

.formInput,
.formTextarea {
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.formInput::placeholder,
.formTextarea::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.formInput:focus,
.formTextarea:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

.formTextarea {
  resize: vertical;
  min-height: 120px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .serviceGrid {
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 0 16px;
  }

  .ecoFriendly {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 60px 16px;
  }

  .ecoContent,
  .ecoImage {
    padding: 0;
  }

  .galleryGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    padding: 0 16px;
  }

  .contactContainer {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 0 16px;
  }

  .contactInfo,
  .contactFormContainer {
    padding: 0;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .ecoTitle {
    font-size: 1.8rem;
  }
}
