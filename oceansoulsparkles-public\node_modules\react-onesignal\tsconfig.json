{"compilerOptions": {"outDir": "dist", "module": "esnext", "target": "es6", "lib": ["es6", "dom", "es2016", "es2017"], "sourceMap": true, "allowJs": true, "declaration": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "strictNullChecks": false, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "noUnusedParameters": true, "allowSyntheticDefaultImports": true, "downlevelIteration": true, "skipLibCheck": true}, "include": ["index.ts"], "exclude": ["node_modules", "build", "dist", "example", "rollup.config.js"]}