exports.id=414,exports.ids=[414],exports.modules={1111:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(997),o=r(6689),n=r.n(o);class a extends n().Component{constructor(e){super(e),this.logErrorToService=(e,t)=>{try{let r={message:e.message,stack:e.stack,componentStack:t.componentStack,timestamp:new Date().toISOString(),userAgent:"unknown",url:"unknown"};console.warn("Error logged to service:",r)}catch(e){console.error("Failed to log error to service:",e)}},this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t}),this.logErrorToService(e,t)}render(){return this.state.hasError?(0,s.jsxs)("div",{style:{padding:"40px",textAlign:"center",minHeight:"400px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"8px",margin:"20px",fontFamily:"Arial, sans-serif"},children:[s.jsx("div",{style:{fontSize:"48px",marginBottom:"20px",color:"#6c757d"},children:"\uD83C\uDF0A"}),s.jsx("h2",{style:{color:"#495057",marginBottom:"16px",fontSize:"24px"},children:"Oops! Something went wrong"}),s.jsx("p",{style:{color:"#6c757d",marginBottom:"24px",maxWidth:"500px",lineHeight:"1.5"},children:"We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue."}),(0,s.jsxs)("div",{style:{marginBottom:"20px"},children:[s.jsx("button",{onClick:this.handleRetry,style:{backgroundColor:"#3788d8",color:"white",border:"none",padding:"12px 24px",borderRadius:"6px",fontSize:"16px",cursor:"pointer",marginRight:"12px",transition:"background-color 0.2s"},onMouseOver:e=>e.target.style.backgroundColor="#2c6cb7",onMouseOut:e=>e.target.style.backgroundColor="#3788d8",children:"Try Again"}),s.jsx("a",{href:"/",style:{backgroundColor:"#6c757d",color:"white",textDecoration:"none",padding:"12px 24px",borderRadius:"6px",fontSize:"16px",transition:"background-color 0.2s"},onMouseOver:e=>e.target.style.backgroundColor="#5a6268",onMouseOut:e=>e.target.style.backgroundColor="#6c757d",children:"Go Home"})]}),!1,(0,s.jsxs)("p",{style:{fontSize:"14px",color:"#adb5bd",marginTop:"20px"},children:["If this problem persists, please contact us at"," ",s.jsx("a",{href:"mailto:<EMAIL>",style:{color:"#3788d8"},children:"<EMAIL>"})]})]}):this.props.children}}let i=a},5889:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Z:()=>l});var o=r(997),n=r(6689),a=r(3590),i=e([a]);function l({children:e}){let[t,r]=(0,n.useState)(!0),[s,a]=(0,n.useState)(null),[i,l]=(0,n.useState)(!1),c=async()=>{if(s)try{s.prompt();let{outcome:e}=await s.userChoice;"accepted"===e?console.log("User accepted the install prompt"):console.log("User dismissed the install prompt"),a(null)}catch(e){console.error("Error installing PWA:",e)}};return(0,o.jsxs)(o.Fragment,{children:[e,s&&!i&&(0,o.jsxs)("div",{style:{position:"fixed",bottom:"20px",left:"20px",right:"20px",backgroundColor:"#3788d8",color:"white",padding:"16px",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",zIndex:1e3,display:"flex",alignItems:"center",justifyContent:"space-between",maxWidth:"400px",margin:"0 auto"},children:[(0,o.jsxs)("div",{style:{flex:1,marginRight:"12px"},children:[o.jsx("strong",{children:"Install Ocean Soul Sparkles"}),o.jsx("br",{}),o.jsx("small",{children:"Get quick access to our services!"})]}),(0,o.jsxs)("div",{children:[o.jsx("button",{onClick:c,style:{backgroundColor:"white",color:"#3788d8",border:"none",padding:"8px 16px",borderRadius:"4px",fontSize:"14px",fontWeight:"bold",cursor:"pointer",marginRight:"8px"},children:"Install"}),o.jsx("button",{onClick:()=>a(null),style:{backgroundColor:"transparent",color:"white",border:"1px solid white",padding:"8px 16px",borderRadius:"4px",fontSize:"14px",cursor:"pointer"},children:"Later"})]})]}),!t&&o.jsx("div",{style:{position:"fixed",top:"0",left:"0",right:"0",backgroundColor:"#dc3545",color:"white",padding:"8px",textAlign:"center",fontSize:"14px",zIndex:1001},children:"You are currently offline. Some features may not work properly."})]})}a=(i.then?(await i)():i)[0],s()}catch(e){s(e)}})},288:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{O:()=>c,f:()=>u});var o=r(997),n=r(6689),a=r(2732),i=r(3590),l=e([i]);i=(l.then?(await l)():l)[0];let p=(0,n.createContext)();function c(){let e=(0,n.useContext)(p);if(!e)throw Error("useCustomer must be used within a CustomerProvider");return e}function u({children:e}){let[t,r]=(0,n.useState)(null),[s,l]=(0,n.useState)(!0),[c,u]=(0,n.useState)(null),d=async(e,t)=>{try{l(!0),u(null);let{data:s,error:o}=await a.vZ.signIn(e,t);if(o)throw o;return r(s.user),i.toast.success("Welcome back!"),{success:!0,data:s}}catch(e){return console.error("[Customer Auth] Sign in error:",e),u(e.message),i.toast.error(e.message||"Sign in failed"),{success:!1,error:e.message}}finally{l(!1)}},h=async(e,t,r={})=>{try{l(!0),u(null);let{data:s,error:o}=await a.vZ.signUp(e,t,r);if(o)throw o;return i.toast.success("Account created! Please check your email to verify your account."),{success:!0,data:s}}catch(e){return console.error("[Customer Auth] Sign up error:",e),u(e.message),i.toast.error(e.message||"Sign up failed"),{success:!1,error:e.message}}finally{l(!1)}},m=async()=>{try{l(!0);let{error:e}=await a.vZ.signOut();if(e)throw e;return r(null),u(null),i.toast.success("Signed out successfully"),{success:!0}}catch(e){return console.error("[Customer Auth] Sign out error:",e),i.toast.error("Sign out failed"),{success:!1,error:e.message}}finally{l(!1)}},g=async e=>{try{return l(!0),console.log("[Customer] Creating guest booking:",e),{success:!0,isGuest:!0}}catch(e){return console.error("[Customer] Guest booking error:",e),{success:!1,error:e.message}}finally{l(!1)}};return o.jsx(p.Provider,{value:{customer:t,loading:s,error:c,isAuthenticated:!!t,signIn:d,signUp:h,signOut:m,createGuestBooking:g,getCustomerEmail:()=>t?.email||null,getCustomerId:()=>t?.id||null,isCustomerVerified:()=>!!t?.email_confirmed_at},children:e})}s()}catch(e){s(e)}})},2732:(e,t,r)=>{"use strict";r.d(t,{v8:()=>p,vZ:()=>u});var s=r(2885);let o="https://ndlgbcsbidyhxbpqzgqp.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI";if(!o||!n)throw Error("Missing required Supabase environment variables for public client");let a={"X-Client-Info":"ocean-soul-sparkles-public@1.0.0","X-Client-Type":"public-website"},i=(e,t={})=>Promise.race([fetch(e,t),new Promise((e,t)=>setTimeout(()=>t(Error("Request timeout")),1e4))]),l=function(){try{console.log("[Public Supabase] Creating public client instance");let e=(0,s.createClient)(o,n,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!1,storageKey:"oss_public_auth_token",storage:void 0,cookieOptions:{path:"/",sameSite:"Lax",secure:!0}},global:{headers:a,fetch:i},realtime:{params:{eventsPerSecond:1}}});return console.log("[Public Supabase] Client created successfully"),e}catch(e){throw console.error("[Public Supabase] Error creating client:",e),e}}();async function c(){try{let{data:{user:e},error:t}=await l.auth.getUser();if(t||!e)return null;let{data:r}=await l.from("user_roles").select("role").eq("id",e.id).single();if(r?.role!=="user")return console.warn("[Public Supabase] Non-customer user attempted access:",r?.role),await l.auth.signOut(),null;return e}catch(e){return console.error("[Public Supabase] Error getting current customer:",e),null}}let u={async signIn(e,t){try{let{data:r,error:s}=await l.auth.signInWithPassword({email:e,password:t});if(s)throw s;if(!await c())throw Error("Access denied: Customer account required");return{data:r,error:null}}catch(e){return console.error("[Public Auth] Customer sign in error:",e),{data:null,error:e}}},async signUp(e,t,r={}){try{let{data:s,error:o}=await l.auth.signUp({email:e,password:t,options:{data:{role:"user",...r}}});return{data:s,error:o}}catch(e){return console.error("[Public Auth] Customer sign up error:",e),{data:null,error:e}}},async signOut(){try{let{error:e}=await l.auth.signOut();return{error:e}}catch(e){return console.error("[Public Auth] Customer sign out error:",e),{error:e}}}},p={async getServices(){try{let{data:e,error:t}=await l.from("services").select("*").eq("status","active").eq("visible_on_public",!0).gte("duration",120).lte("duration",360).order("name");return{data:e,error:t}}catch(e){return console.error("[Public Data] Error fetching services:",e),{data:null,error:e}}},async getProducts(e=null){try{let t=l.from("products").select("*").eq("status","active");e&&"all"!==e&&(t=t.eq("category_name",e));let{data:r,error:s}=await t.order("name");return{data:r,error:s}}catch(e){return console.error("[Public Data] Error fetching products:",e),{data:null,error:e}}}}},3414:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>g});var o=r(997);r(6764),r(8819);var n=r(968),a=r.n(n),i=r(6689),l=r(288),c=r(5889),u=r(3590),p=r(1111),d=e([l,c,u]);[l,c,u]=d.then?(await d)():d;let h=(0,i.memo)(function(){return(0,o.jsxs)(a(),{children:[o.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),o.jsx("link",{rel:"icon",href:"/favicon.ico"}),o.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),o.jsx("link",{rel:"manifest",href:"/manifest.json"}),o.jsx("meta",{name:"theme-color",content:"#3788d8"}),o.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),o.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),o.jsx("meta",{name:"apple-mobile-web-app-title",content:"Ocean Soul Sparkles"}),o.jsx("meta",{httpEquiv:"X-Content-Type-Options",content:"nosniff"}),o.jsx("meta",{httpEquiv:"X-Frame-Options",content:"DENY"}),o.jsx("meta",{httpEquiv:"X-XSS-Protection",content:"1; mode=block"}),o.jsx("meta",{property:"og:type",content:"website"}),o.jsx("meta",{property:"og:site_name",content:"Ocean Soul Sparkles"}),o.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),o.jsx("meta",{name:"twitter:site",content:"@oceansoulsparkles"}),o.jsx("link",{rel:"preconnect",href:"https://ndlgbcsbidyhxbpqzgqp.supabase.co"}),o.jsx("link",{rel:"preconnect",href:"https://js.squareup.com"}),o.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),o.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]})}),m=(0,i.memo)(function(){return o.jsx(u.ToastContainer,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light",toastClassName:"custom-toast",bodyClassName:"custom-toast-body",progressClassName:"custom-toast-progress"})}),g=function({Component:e,pageProps:t,router:r}){return r?.pathname?.startsWith("/admin")||r?.pathname?.startsWith("/staff")||r?.pathname?.startsWith("/artist")||r?.pathname?.startsWith("/apply")?(0,o.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",fontFamily:"Arial, sans-serif"},children:[o.jsx("h1",{children:"404 - Page Not Found"}),o.jsx("p",{children:"The page you're looking for doesn't exist."}),o.jsx("a",{href:"/",style:{color:"#3788d8",textDecoration:"none"},children:"Return to Home"})]}):o.jsx(p.Z,{children:o.jsx(l.f,{children:(0,o.jsxs)(c.Z,{children:[o.jsx(h,{}),o.jsx(e,{...t}),o.jsx(m,{})]})})})};s()}catch(e){s(e)}})},6764:()=>{}};