"use strict";(()=>{var e={};e.id=287,e.ids=[287],e.modules={145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6249:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8115:(e,t,r)=>{r.r(t),r.d(t,{config:()=>u,default:()=>l,routeModule:()=>d});var i={};r.r(i),r.d(i,{default:()=>o});var a=r(1802),s=r(7153),n=r(6249),c=r(395);async function o(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{console.log("[Public API] Fetching services for public display");let{data:e,error:r}=await c.v8.getServices();if(r)return console.error("[Public API] Error fetching services:",r),console.log("[Public API] Using fallback services data"),t.status(200).json({services:[{id:"fallback-1",name:"Face Painting",description:"Professional face painting for all ages",duration:180,price:150,category:"Face Painting",status:"active",visible_on_public:!0},{id:"fallback-2",name:"Airbrush Body Art",description:"Stunning airbrush body art and temporary tattoos",duration:240,price:200,category:"Body Painting",status:"active",visible_on_public:!0},{id:"fallback-3",name:"Festival Braiding",description:"Colorful braiding perfect for festivals and events",duration:120,price:80,category:"Hair & Braiding",status:"active",visible_on_public:!0}],fallback:!0,message:"Using cached service data"});let i=e.map(e=>({id:e.id,name:e.name,description:e.description,duration:e.duration,price:e.price,category:e.category,image_url:e.image_url,features:e.features,suitable_for:e.suitable_for}));return t.setHeader("Cache-Control","public, s-maxage=300, stale-while-revalidate=600"),console.log(`[Public API] Successfully fetched ${i.length} services`),t.status(200).json({services:i,count:i.length,fallback:!1})}catch(e){return console.error("[Public API] Unexpected error fetching services:",e),t.status(500).json({error:"Unable to fetch services",services:[],fallback:!0})}}let l=(0,n.l)(i,"default"),u=(0,n.l)(i,"config"),d=new a.PagesAPIRouteModule({definition:{kind:s.x.PAGES_API,page:"/api/public/services",pathname:"/api/public/services",bundlePath:"",filename:""},userland:i})},395:(e,t,r)=>{r.d(t,{v8:()=>l,OQ:()=>o});let i=require("@supabase/supabase-js"),a="https://ndlgbcsbidyhxbpqzgqp.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI";if(!a||!s)throw Error("Missing required Supabase environment variables for public client");let n={"X-Client-Info":"ocean-soul-sparkles-public@1.0.0","X-Client-Type":"public-website"},c=(e,t={})=>Promise.race([fetch(e,t),new Promise((e,t)=>setTimeout(()=>t(Error("Request timeout")),1e4))]),o=function(){try{console.log("[Public Supabase] Creating public client instance");let e=(0,i.createClient)(a,s,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!1,storageKey:"oss_public_auth_token",storage:void 0,cookieOptions:{path:"/",sameSite:"Lax",secure:!0}},global:{headers:n,fetch:c},realtime:{params:{eventsPerSecond:1}}});return console.log("[Public Supabase] Client created successfully"),e}catch(e){throw console.error("[Public Supabase] Error creating client:",e),e}}(),l={async getServices(){try{let{data:e,error:t}=await o.from("services").select("*").eq("status","active").eq("visible_on_public",!0).gte("duration",120).lte("duration",360).order("name");return{data:e,error:t}}catch(e){return console.error("[Public Data] Error fetching services:",e),{data:null,error:e}}},async getProducts(e=null){try{let t=o.from("products").select("*").eq("status","active");e&&"all"!==e&&(t=t.eq("category_name",e));let{data:r,error:i}=await t.order("name");return{data:r,error:i}}catch(e){return console.error("[Public Data] Error fetching products:",e),{data:null,error:e}}}}},7153:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},1802:(e,t,r)=>{e.exports=r(145)}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=8115);module.exports=r})();