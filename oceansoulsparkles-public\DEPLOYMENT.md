# Ocean Soul Sparkles - Public Subdomain Deployment Guide

## Phase 4 Stage 1: Public Subdomain (www.oceansoulsparkles.com.au)

This guide provides step-by-step instructions for deploying the Ocean Soul Sparkles public subdomain to production.

## Prerequisites

- [Vercel CLI](https://vercel.com/cli) installed globally: `npm i -g vercel`
- Access to the Ocean Soul Sparkles Vercel account
- Domain ownership verification for `oceansoulsparkles.com.au`
- Supabase project credentials
- Square payment credentials (production)
- Google Analytics account (optional)
- OneSignal account (optional)

## Pre-Deployment Checklist

### 1. Environment Variables Setup

Ensure you have the following environment variables ready:

**Required:**
- `NEXT_PUBLIC_SUPABASE_URL` - Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `NEXT_PUBLIC_SITE_URL` - https://www.oceansoulsparkles.com.au

**Payment Integration:**
- `NEXT_PUBLIC_SQUARE_APPLICATION_ID` - Square production application ID
- `NEXT_PUBLIC_SQUARE_LOCATION_ID` - Square production location ID

**Analytics (Optional):**
- `NEXT_PUBLIC_GOOGLE_ANALYTICS_ID` - Google Analytics measurement ID

**Notifications (Optional):**
- `NEXT_PUBLIC_ONESIGNAL_APP_ID` - OneSignal app ID
- `NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID` - OneSignal Safari web ID

### 2. Security Verification

Run the deployment validation script:
```bash
node scripts/deploy-public.js
```

Ensure all security checks pass before proceeding.

### 3. Build Verification

Test the production build locally:
```bash
npm run build
npm run start
```

Visit `http://localhost:3000` and verify:
- ✅ Homepage loads correctly
- ✅ Services page displays public services only
- ✅ Shop page loads products
- ✅ Booking form works (without payment processing)
- ✅ Contact form functions
- ✅ No admin routes are accessible
- ✅ All images and assets load properly

## Deployment Steps

### Step 1: Initial Vercel Setup

1. **Login to Vercel:**
   ```bash
   vercel login
   ```

2. **Link the project:**
   ```bash
   vercel link
   ```
   - Select your Vercel account
   - Choose "Link to existing project" if available, or create new
   - Project name: `oceansoulsparkles-public`

### Step 2: Configure Environment Variables

Set up environment variables in Vercel:

```bash
# Required variables
vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production
vercel env add NEXT_PUBLIC_SITE_URL production

# Payment variables
vercel env add NEXT_PUBLIC_SQUARE_APPLICATION_ID production
vercel env add NEXT_PUBLIC_SQUARE_LOCATION_ID production

# Optional analytics
vercel env add NEXT_PUBLIC_GOOGLE_ANALYTICS_ID production

# Optional notifications
vercel env add NEXT_PUBLIC_ONESIGNAL_APP_ID production
vercel env add NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID production
```

**Alternative:** Configure via Vercel Dashboard:
1. Go to your project settings
2. Navigate to "Environment Variables"
3. Add each variable for the "Production" environment

### Step 3: Deploy to Production

1. **Deploy the application:**
   ```bash
   vercel --prod
   ```

2. **Verify deployment:**
   - Note the deployment URL provided by Vercel
   - Test the deployment on the temporary Vercel URL first

### Step 4: Domain Configuration

#### 4.1 Add Custom Domain in Vercel

1. Go to your Vercel project dashboard
2. Navigate to "Settings" → "Domains"
3. Add domain: `www.oceansoulsparkles.com.au`
4. Add redirect domain: `oceansoulsparkles.com.au` → `www.oceansoulsparkles.com.au`

#### 4.2 DNS Configuration

Configure your DNS provider with the following records:

**CNAME Records:**
```
www.oceansoulsparkles.com.au → cname.vercel-dns.com
```

**A Records (for apex domain redirect):**
```
oceansoulsparkles.com.au → 76.76.19.19
```

**Additional DNS Records:**
```
# Email (if using external email provider)
MX @ your-email-provider-mx-records

# SPF Record
TXT @ "v=spf1 include:your-email-provider ~all"

# DMARC Record
TXT _dmarc "v=DMARC1; p=none; rua=mailto:<EMAIL>"
```

### Step 5: SSL Certificate Verification

1. Wait for DNS propagation (up to 24 hours)
2. Verify SSL certificate is issued automatically by Vercel
3. Test HTTPS access: `https://www.oceansoulsparkles.com.au`

### Step 6: Apple Pay Domain Verification (Optional)

If using Apple Pay with Square:

1. Verify the domain association endpoint works:
   ```
   https://www.oceansoulsparkles.com.au/.well-known/apple-developer-merchantid-domain-association
   ```

2. Register the domain in your Apple Developer account

## Post-Deployment Testing

### Functional Testing Checklist

- [ ] **Homepage**
  - [ ] Hero section loads with correct branding
  - [ ] Service showcase displays properly
  - [ ] Navigation menu works
  - [ ] Footer links function

- [ ] **Services Page**
  - [ ] Only public services are displayed
  - [ ] Service filtering works
  - [ ] "Book Now" buttons redirect correctly

- [ ] **Shop Page**
  - [ ] Products load from Supabase
  - [ ] Category filtering functions
  - [ ] Search functionality works
  - [ ] Product details display correctly

- [ ] **Booking System**
  - [ ] Service selection works
  - [ ] Customer form validation
  - [ ] Date/time selection (if implemented)
  - [ ] Booking submission (test mode)

- [ ] **Contact & Info Pages**
  - [ ] Contact form submission
  - [ ] Gallery images load
  - [ ] About page content displays
  - [ ] Policies page accessible

### Security Testing Checklist

- [ ] **Admin Route Protection**
  - [ ] `/admin/*` routes return 404
  - [ ] `/api/admin/*` endpoints blocked
  - [ ] `/staff/*` routes return 404
  - [ ] `/artist/*` routes return 404
  - [ ] `/apply/*` routes return 404

- [ ] **Security Headers**
  - [ ] X-Frame-Options: DENY
  - [ ] X-Content-Type-Options: nosniff
  - [ ] Referrer-Policy set correctly
  - [ ] Permissions-Policy configured

- [ ] **Data Security**
  - [ ] No admin data exposed in API responses
  - [ ] No sensitive environment variables in client
  - [ ] HTTPS enforced
  - [ ] No debug information exposed

### Performance Testing

- [ ] **Core Web Vitals**
  - [ ] Largest Contentful Paint (LCP) < 2.5s
  - [ ] First Input Delay (FID) < 100ms
  - [ ] Cumulative Layout Shift (CLS) < 0.1

- [ ] **Page Load Times**
  - [ ] Homepage < 3s
  - [ ] Services page < 3s
  - [ ] Shop page < 4s
  - [ ] Booking page < 3s

## Monitoring & Maintenance

### 1. Set Up Monitoring

- **Vercel Analytics:** Enable in project settings
- **Google Analytics:** Verify tracking is working
- **Error Tracking:** Monitor Vercel function logs
- **Uptime Monitoring:** Set up external monitoring service

### 2. Regular Maintenance Tasks

- **Weekly:**
  - Check Vercel deployment logs
  - Monitor Core Web Vitals
  - Verify all forms are working

- **Monthly:**
  - Review analytics data
  - Check for broken links
  - Update dependencies if needed
  - Verify SSL certificate status

### 3. Emergency Procedures

**If site goes down:**
1. Check Vercel status page
2. Verify DNS configuration
3. Check environment variables
4. Review recent deployments
5. Rollback if necessary: `vercel rollback`

**If admin routes become accessible:**
1. Immediately redeploy with correct configuration
2. Verify middleware is working
3. Check vercel.json redirects
4. Audit security settings

## Phase 4 Stage 2 Planning: Admin Subdomain

### Next Steps Overview

After successful public subdomain deployment, Phase 4 Stage 2 will involve:

1. **Admin Subdomain Setup** (`admin.oceansoulsparkles.com.au`)
   - Separate Vercel project for admin functionality
   - Enhanced security with IP restrictions
   - Staff authentication and role management

2. **Database Migration**
   - Move admin-specific tables to separate schema
   - Implement cross-subdomain data synchronization
   - Set up admin-specific RLS policies

3. **Staff Portal Development**
   - Artist/Braider onboarding system
   - Performance metrics dashboard
   - Commission tracking and reporting

4. **Advanced Features**
   - Real-time booking management
   - Customer communication system
   - Marketing automation tools
   - Advanced analytics and reporting

### Technical Requirements for Stage 2

- **Infrastructure:**
  - Separate Vercel project for admin subdomain
  - Enhanced security configurations
  - Staff VPN or IP allowlisting
  - Dedicated admin database schema

- **Authentication:**
  - Multi-factor authentication (MFA)
  - Role-based access control (RBAC)
  - Session management and timeout
  - Audit logging for admin actions

- **Integration:**
  - Secure API communication between subdomains
  - Real-time data synchronization
  - Backup and disaster recovery
  - Performance monitoring and alerting

## Support & Troubleshooting

### Common Issues

1. **Build Failures:**
   - Check environment variables are set
   - Verify all dependencies are installed
   - Review build logs in Vercel dashboard

2. **Domain Issues:**
   - Verify DNS propagation with `dig` or online tools
   - Check domain configuration in Vercel
   - Ensure SSL certificate is issued

3. **API Errors:**
   - Verify Supabase connection
   - Check RLS policies for public access
   - Review API endpoint logs

### Getting Help

- **Vercel Support:** [vercel.com/support](https://vercel.com/support)
- **Supabase Support:** [supabase.com/support](https://supabase.com/support)
- **Project Documentation:** See `/docs` folder for detailed technical docs

---

**Deployment completed successfully!** 🎉

The Ocean Soul Sparkles public subdomain is now live and ready to serve customers with a secure, fast, and beautiful booking experience.
