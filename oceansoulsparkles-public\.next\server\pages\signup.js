(()=>{var e={};e.id=616,e.ids=[616,660],e.modules={292:e=>{e.exports={authContainer:"Auth_authContainer__krRUD",authCard:"Auth_authCard__c52sr",authHeader:"Auth_authHeader__tImyy",authForm:"Auth_authForm__Q3DLf",formRow:"Auth_formRow__cgwUK",formGroup:"Auth_formGroup__9PGF_",formInput:"Auth_formInput__GBmIX",passwordInput:"Auth_passwordInput__Z_Gdm",passwordToggle:"Auth_passwordToggle__0CEK0",checkboxGroup:"Auth_checkboxGroup__GTN4W",checkboxLabel:"Auth_checkboxLabel__jHubw",checkbox:"Auth_checkbox__CWB76",checkboxText:"Auth_checkboxText__C8ZZD",authButton:"Auth_authButton__FY9nb",loading:"Auth_loading___YK33",buttonSpinner:"Auth_buttonSpinner__OxbRv",spin:"Auth_spin__3Kb1j",authLinks:"Auth_authLinks__wOm40",authLink:"Auth_authLink__3rvVg",guestOption:"Auth_guestOption__TtmNY",divider:"Auth_divider__bZzOZ",authFooter:"Auth_authFooter__Wv0sO",loadingSpinner:"Auth_loadingSpinner__UuqwH",spinner:"Auth_spinner__fRASl"}},1055:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{config:()=>O,default:()=>h,getServerSideProps:()=>p,getStaticPaths:()=>f,getStaticProps:()=>m,reportWebVitals:()=>N,routeModule:()=>y,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>x,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>_});var o=r(7093),a=r(5244),n=r(1323),l=r(2899),c=r.n(l),i=r(3414),d=r(2452),u=e([i,d]);[i,d]=u.then?(await u)():u;let h=(0,n.l)(d,"default"),m=(0,n.l)(d,"getStaticProps"),f=(0,n.l)(d,"getStaticPaths"),p=(0,n.l)(d,"getServerSideProps"),O=(0,n.l)(d,"config"),N=(0,n.l)(d,"reportWebVitals"),_=(0,n.l)(d,"unstable_getStaticProps"),j=(0,n.l)(d,"unstable_getStaticPaths"),x=(0,n.l)(d,"unstable_getStaticParams"),b=(0,n.l)(d,"unstable_getServerProps"),v=(0,n.l)(d,"unstable_getServerSideProps"),y=new o.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/signup",pathname:"/signup",bundlePath:"",filename:""},components:{App:i.default,Document:c()},userland:d});t()}catch(e){t(e)}})},1398:(e,s,r)=>{"use strict";r.d(s,{Z:()=>d});var t=r(997),o=r(968),a=r.n(o),n=r(1664),l=r.n(n),c=r(6689);!function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(1163);function d({children:e}){let[s,r]=(0,c.useState)(!1),[o,n]=(0,c.useState)(!1),[d,u]=(0,c.useState)(!1),h=(0,i.useRouter)(),m=[{label:"Home",href:"/"},{label:"About Us",href:"/about"},{label:"Services",href:"/services"},{label:"Gallery",href:"/gallery"},{label:"Shop",href:"/shop"},{label:"Gift Card",href:"/gift-card"},{label:"Contact",href:"/contact"}],{asPath:f}=h,p=`https://www.oceansoulsparkles.com.au${f}`,O=(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx(a(),{children:t.jsx("link",{rel:"canonical",href:p})}),t.jsx(Object(function(){var e=Error("Cannot find module './GoogleVerification'");throw e.code="MODULE_NOT_FOUND",e}()),{}),t.jsx(Object(function(){var e=Error("Cannot find module './StructuredData/SchemaManager'");throw e.code="MODULE_NOT_FOUND",e}()),{}),t.jsx("header",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${d?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,children:(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx(l(),{href:"/",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("img",{src:"/images/bannerlogo.PNG",alt:"Ocean Soul Sparkles"})}),(0,t.jsxs)("button",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${o?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,onClick:()=>{n(!o)},"aria-label":"Toggle Navigation",children:[t.jsx("span",{}),t.jsx("span",{}),t.jsx("span",{})]}),t.jsx("nav",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${o?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,children:t.jsx("ul",{children:m.map((e,s)=>t.jsx("li",{className:h.pathname===e.href?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):"",children:t.jsx(l(),{href:e.href,children:e.label})},s))})}),t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx(Object(function(){var e=Error("Cannot find module './SparkleButton'");throw e.code="MODULE_NOT_FOUND",e}()),{href:"/book-online",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Book Now"})})]})}),e,t.jsx(Object(function(){var e=Error("Cannot find module './NotificationPrompt'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,t.jsxs)("footer",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 320",preserveAspectRatio:"none",children:t.jsx("path",{fill:"#4ECDC4",fillOpacity:"0.2",d:"M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"})})}),t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx(l(),{href:"/",children:t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"OceanSoulSparkles"})}),t.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("span",{className:"accent-text",children:'"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!"'})}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("a",{href:"https://www.instagram.com/oceansoulsparkles",target:"_blank",rel:"noopener noreferrer",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("img",{src:"/images/social/instagram-icon.png",alt:"Instagram"})}),t.jsx("a",{href:"https://www.facebook.com/OceanSoulSparkles/",target:"_blank",rel:"noopener noreferrer",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:t.jsx("img",{src:"/images/social/facebook-icon.png",alt:"Facebook"})})]})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h3",{children:"Quick Links"}),(0,t.jsxs)("ul",{children:[m.map((e,s)=>t.jsx("li",{children:t.jsx(l(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.label})},s)),t.jsx("li",{children:t.jsx(l(),{href:"/book-online",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Book Online"})}),t.jsx("li",{children:t.jsx(l(),{href:"/book-events",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Events Booking"})}),t.jsx("li",{children:t.jsx(l(),{href:"/policies#return-policy",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Return & Refund Policy"})}),t.jsx("li",{children:t.jsx(l(),{href:"/policies#shipping-info",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Shipping Information"})})]})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h3",{children:"Contact Us"}),t.jsx("p",{children:t.jsx("a",{href:"mailto:<EMAIL>",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"<EMAIL>"})}),t.jsx("p",{children:"Melbourne, Victoria"}),t.jsx(l(),{href:"/contact",className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} button button--outline`,children:"Get in Touch"})]})]})}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("h3",{children:"Payment Methods"}),t.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),style:{justifyContent:"center"},children:t.jsx("img",{src:"/images/logos/square.png",alt:"Square Payments",width:150,height:40,style:{margin:"0 auto"}})}),(0,t.jsxs)("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[t.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z",fill:"#4CAF50"})}),t.jsx("span",{children:"Secure payment processing"})]})]}),(0,t.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,t.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," by OceanSoulSparkles. All rights reserved."]}),t.jsx("p",{children:"Proudly created with Next.js"})]}),s&&t.jsx("button",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Scroll to top",children:t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:t.jsx("polyline",{points:"18 15 12 9 6 15"})})})]})]});return t.jsx(Object(function(){var e=Error("Cannot find module './OneSignalProvider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:O})}(function(){var e=Error("Cannot find module './SparkleButton'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module './StructuredData/SchemaManager'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './NotificationPrompt'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './OneSignalProvider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './GoogleVerification'");throw e.code="MODULE_NOT_FOUND",e}()},2452:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{default:()=>N});var o=r(997),a=r(6689),n=r(1163),l=r(968),c=r.n(l),i=r(1664),d=r.n(i),u=r(1398),h=r(288),m=r(3590),f=r(292),p=r.n(f),O=e([h,m]);function N(){let[e,s]=(0,a.useState)({email:"",password:"",confirmPassword:"",firstName:"",lastName:"",phone:""}),[r,t]=(0,a.useState)(!1),[l,i]=(0,a.useState)(!1),[f,O]=(0,a.useState)(!1),[N,_]=(0,a.useState)(!1),{signUp:j,isAuthenticated:x,loading:b}=(0,h.O)(),v=(0,n.useRouter)(),y=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t}))},D=()=>{let{email:s,password:r,confirmPassword:t,firstName:o,lastName:a}=e;return s&&r&&t&&o&&a?r.length<8?(m.toast.error("Password must be at least 8 characters long"),!1):r!==t?(m.toast.error("Passwords do not match"),!1):!!N||(m.toast.error("Please accept the Terms of Service and Privacy Policy"),!1):(m.toast.error("Please fill in all required fields"),!1)},w=async s=>{if(s.preventDefault(),D()){t(!0);try{let s={first_name:e.firstName,last_name:e.lastName,phone:e.phone,full_name:`${e.firstName} ${e.lastName}`};(await j(e.email,e.password,s)).success&&(m.toast.success("Account created successfully! Please check your email to verify your account."),setTimeout(()=>{v.push("/login")},2e3))}catch(e){console.error("Signup error:",e),m.toast.error("Account creation failed. Please try again.")}finally{t(!1)}}};return b?o.jsx(u.Z,{children:o.jsx("div",{className:p().authContainer,children:o.jsx("div",{className:p().authCard,children:(0,o.jsxs)("div",{className:p().loadingSpinner,children:[o.jsx("div",{className:p().spinner}),o.jsx("p",{children:"Loading..."})]})})})}):x?null:(0,o.jsxs)(u.Z,{children:[(0,o.jsxs)(c(),{children:[o.jsx("title",{children:"Create Account - Ocean Soul Sparkles"}),o.jsx("meta",{name:"description",content:"Create your Ocean Soul Sparkles customer account to book services and manage your orders."}),o.jsx("meta",{name:"robots",content:"noindex, nofollow"})]}),(0,o.jsxs)("div",{className:p().authContainer,children:[(0,o.jsxs)("div",{className:p().authCard,children:[(0,o.jsxs)("div",{className:p().authHeader,children:[o.jsx("h1",{children:"Create Your Account"}),o.jsx("p",{children:"Join Ocean Soul Sparkles to book services and track your orders"})]}),(0,o.jsxs)("form",{onSubmit:w,className:p().authForm,children:[(0,o.jsxs)("div",{className:p().formRow,children:[(0,o.jsxs)("div",{className:p().formGroup,children:[o.jsx("label",{htmlFor:"firstName",children:"First Name *"}),o.jsx("input",{id:"firstName",name:"firstName",type:"text",value:e.firstName,onChange:y,placeholder:"Enter your first name",required:!0,disabled:r,className:p().formInput})]}),(0,o.jsxs)("div",{className:p().formGroup,children:[o.jsx("label",{htmlFor:"lastName",children:"Last Name *"}),o.jsx("input",{id:"lastName",name:"lastName",type:"text",value:e.lastName,onChange:y,placeholder:"Enter your last name",required:!0,disabled:r,className:p().formInput})]})]}),(0,o.jsxs)("div",{className:p().formGroup,children:[o.jsx("label",{htmlFor:"email",children:"Email Address *"}),o.jsx("input",{id:"email",name:"email",type:"email",value:e.email,onChange:y,placeholder:"Enter your email",required:!0,disabled:r,className:p().formInput})]}),(0,o.jsxs)("div",{className:p().formGroup,children:[o.jsx("label",{htmlFor:"phone",children:"Phone Number"}),o.jsx("input",{id:"phone",name:"phone",type:"tel",value:e.phone,onChange:y,placeholder:"Enter your phone number",disabled:r,className:p().formInput})]}),(0,o.jsxs)("div",{className:p().formGroup,children:[o.jsx("label",{htmlFor:"password",children:"Password *"}),(0,o.jsxs)("div",{className:p().passwordInput,children:[o.jsx("input",{id:"password",name:"password",type:l?"text":"password",value:e.password,onChange:y,placeholder:"Create a password (min. 8 characters)",required:!0,disabled:r,className:p().formInput,minLength:8}),o.jsx("button",{type:"button",onClick:()=>i(!l),className:p().passwordToggle,disabled:r,children:l?"\uD83D\uDC41️":"\uD83D\uDC41️‍\uD83D\uDDE8️"})]})]}),(0,o.jsxs)("div",{className:p().formGroup,children:[o.jsx("label",{htmlFor:"confirmPassword",children:"Confirm Password *"}),(0,o.jsxs)("div",{className:p().passwordInput,children:[o.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:f?"text":"password",value:e.confirmPassword,onChange:y,placeholder:"Confirm your password",required:!0,disabled:r,className:p().formInput}),o.jsx("button",{type:"button",onClick:()=>O(!f),className:p().passwordToggle,disabled:r,children:f?"\uD83D\uDC41️":"\uD83D\uDC41️‍\uD83D\uDDE8️"})]})]}),o.jsx("div",{className:p().checkboxGroup,children:(0,o.jsxs)("label",{className:p().checkboxLabel,children:[o.jsx("input",{type:"checkbox",checked:N,onChange:e=>_(e.target.checked),disabled:r,className:p().checkbox}),(0,o.jsxs)("span",{className:p().checkboxText,children:["I agree to the"," ",o.jsx(d(),{href:"/policies#terms",className:p().authLink,target:"_blank",children:"Terms of Service"})," ","and"," ",o.jsx(d(),{href:"/policies#privacy",className:p().authLink,target:"_blank",children:"Privacy Policy"})]})]})}),o.jsx("button",{type:"submit",disabled:r,className:`${p().authButton} ${r?p().loading:""}`,children:r?(0,o.jsxs)(o.Fragment,{children:[o.jsx("span",{className:p().buttonSpinner}),"Creating Account..."]}):"Create Account"})]}),o.jsx("div",{className:p().authLinks,children:(0,o.jsxs)("p",{children:["Already have an account?"," ",o.jsx(d(),{href:"/login",className:p().authLink,children:"Sign In"})]})}),(0,o.jsxs)("div",{className:p().guestOption,children:[o.jsx("div",{className:p().divider,children:o.jsx("span",{children:"or"})}),(0,o.jsxs)("p",{children:["Want to book without an account?"," ",o.jsx(d(),{href:"/book-online",className:p().authLink,children:"Continue as Guest"})]})]})]}),o.jsx("div",{className:p().authFooter,children:o.jsx("p",{children:"By creating an account, you agree to receive email updates about your bookings and our services. You can unsubscribe at any time."})})]})]})}[h,m]=O.then?(await O)():O,t()}catch(e){t(e)}})},2885:e=>{"use strict";e.exports=require("@supabase/supabase-js")},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[899,65,414],()=>r(1055));module.exports=t})();