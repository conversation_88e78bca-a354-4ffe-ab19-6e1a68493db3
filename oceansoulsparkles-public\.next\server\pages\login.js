(()=>{var e={};e.id=459,e.ids=[459,660],e.modules={292:e=>{e.exports={authContainer:"Auth_authContainer__krRUD",authCard:"Auth_authCard__c52sr",authHeader:"Auth_authHeader__tImyy",authForm:"Auth_authForm__Q3DLf",formRow:"Auth_formRow__cgwUK",formGroup:"Auth_formGroup__9PGF_",formInput:"Auth_formInput__GBmIX",passwordInput:"Auth_passwordInput__Z_Gdm",passwordToggle:"Auth_passwordToggle__0CEK0",checkboxGroup:"Auth_checkboxGroup__GTN4W",checkboxLabel:"Auth_checkboxLabel__jHubw",checkbox:"Auth_checkbox__CWB76",checkboxText:"Auth_checkboxText__C8ZZD",authButton:"Auth_authButton__FY9nb",loading:"Auth_loading___YK33",buttonSpinner:"Auth_buttonSpinner__OxbRv",spin:"Auth_spin__3Kb1j",authLinks:"Auth_authLinks__wOm40",authLink:"Auth_authLink__3rvVg",guestOption:"Auth_guestOption__TtmNY",divider:"Auth_divider__bZzOZ",authFooter:"Auth_authFooter__Wv0sO",loadingSpinner:"Auth_loadingSpinner__UuqwH",spinner:"Auth_spinner__fRASl"}},6990:(e,o,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(o),t.d(o,{config:()=>f,default:()=>h,getServerSideProps:()=>_,getStaticPaths:()=>O,getStaticProps:()=>m,reportWebVitals:()=>j,routeModule:()=>y,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>x,unstable_getStaticPaths:()=>N,unstable_getStaticProps:()=>p});var s=t(7093),n=t(5244),a=t(1323),l=t(2899),i=t.n(l),c=t(3414),d=t(2946),u=e([c,d]);[c,d]=u.then?(await u)():u;let h=(0,a.l)(d,"default"),m=(0,a.l)(d,"getStaticProps"),O=(0,a.l)(d,"getStaticPaths"),_=(0,a.l)(d,"getServerSideProps"),f=(0,a.l)(d,"config"),j=(0,a.l)(d,"reportWebVitals"),p=(0,a.l)(d,"unstable_getStaticProps"),N=(0,a.l)(d,"unstable_getStaticPaths"),x=(0,a.l)(d,"unstable_getStaticParams"),v=(0,a.l)(d,"unstable_getServerProps"),b=(0,a.l)(d,"unstable_getServerSideProps"),y=new s.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/login",pathname:"/login",bundlePath:"",filename:""},components:{App:c.default,Document:i()},userland:d});r()}catch(e){r(e)}})},1398:(e,o,t)=>{"use strict";t.d(o,{Z:()=>d});var r=t(997),s=t(968),n=t.n(s),a=t(1664),l=t.n(a),i=t(6689);!function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}();var c=t(1163);function d({children:e}){let[o,t]=(0,i.useState)(!1),[s,a]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!1),h=(0,c.useRouter)(),m=[{label:"Home",href:"/"},{label:"About Us",href:"/about"},{label:"Services",href:"/services"},{label:"Gallery",href:"/gallery"},{label:"Shop",href:"/shop"},{label:"Gift Card",href:"/gift-card"},{label:"Contact",href:"/contact"}],{asPath:O}=h,_=`https://www.oceansoulsparkles.com.au${O}`,f=(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx(n(),{children:r.jsx("link",{rel:"canonical",href:_})}),r.jsx(Object(function(){var e=Error("Cannot find module './GoogleVerification'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx(Object(function(){var e=Error("Cannot find module './StructuredData/SchemaManager'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("header",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${d?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,children:(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx(l(),{href:"/",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:r.jsx("img",{src:"/images/bannerlogo.PNG",alt:"Ocean Soul Sparkles"})}),(0,r.jsxs)("button",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${s?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,onClick:()=>{a(!s)},"aria-label":"Toggle Navigation",children:[r.jsx("span",{}),r.jsx("span",{}),r.jsx("span",{})]}),r.jsx("nav",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${s?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,children:r.jsx("ul",{children:m.map((e,o)=>r.jsx("li",{className:h.pathname===e.href?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):"",children:r.jsx(l(),{href:e.href,children:e.label})},o))})}),r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:r.jsx(Object(function(){var e=Error("Cannot find module './SparkleButton'");throw e.code="MODULE_NOT_FOUND",e}()),{href:"/book-online",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Book Now"})})]})}),e,r.jsx(Object(function(){var e=Error("Cannot find module './NotificationPrompt'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsxs)("footer",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 320",preserveAspectRatio:"none",children:r.jsx("path",{fill:"#4ECDC4",fillOpacity:"0.2",d:"M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"})})}),r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx(l(),{href:"/",children:r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"OceanSoulSparkles"})}),r.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:r.jsx("span",{className:"accent-text",children:'"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!"'})}),(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx("a",{href:"https://www.instagram.com/oceansoulsparkles",target:"_blank",rel:"noopener noreferrer",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:r.jsx("img",{src:"/images/social/instagram-icon.png",alt:"Instagram"})}),r.jsx("a",{href:"https://www.facebook.com/OceanSoulSparkles/",target:"_blank",rel:"noopener noreferrer",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:r.jsx("img",{src:"/images/social/facebook-icon.png",alt:"Facebook"})})]})]}),(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx("h3",{children:"Quick Links"}),(0,r.jsxs)("ul",{children:[m.map((e,o)=>r.jsx("li",{children:r.jsx(l(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.label})},o)),r.jsx("li",{children:r.jsx(l(),{href:"/book-online",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Book Online"})}),r.jsx("li",{children:r.jsx(l(),{href:"/book-events",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Events Booking"})}),r.jsx("li",{children:r.jsx(l(),{href:"/policies#return-policy",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Return & Refund Policy"})}),r.jsx("li",{children:r.jsx(l(),{href:"/policies#shipping-info",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Shipping Information"})})]})]}),(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx("h3",{children:"Contact Us"}),r.jsx("p",{children:r.jsx("a",{href:"mailto:<EMAIL>",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"<EMAIL>"})}),r.jsx("p",{children:"Melbourne, Victoria"}),r.jsx(l(),{href:"/contact",className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} button button--outline`,children:"Get in Touch"})]})]})}),(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx("h3",{children:"Payment Methods"}),r.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),style:{justifyContent:"center"},children:r.jsx("img",{src:"/images/logos/square.png",alt:"Square Payments",width:150,height:40,style:{margin:"0 auto"}})}),(0,r.jsxs)("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[r.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z",fill:"#4CAF50"})}),r.jsx("span",{children:"Secure payment processing"})]})]}),(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,r.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," by OceanSoulSparkles. All rights reserved."]}),r.jsx("p",{children:"Proudly created with Next.js"})]}),o&&r.jsx("button",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Scroll to top",children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:r.jsx("polyline",{points:"18 15 12 9 6 15"})})})]})]});return r.jsx(Object(function(){var e=Error("Cannot find module './OneSignalProvider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:f})}(function(){var e=Error("Cannot find module './SparkleButton'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module './StructuredData/SchemaManager'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './NotificationPrompt'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './OneSignalProvider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './GoogleVerification'");throw e.code="MODULE_NOT_FOUND",e}()},2946:(e,o,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(o),t.d(o,{default:()=>j});var s=t(997),n=t(6689),a=t(1163),l=t(968),i=t.n(l),c=t(1664),d=t.n(c),u=t(1398),h=t(288),m=t(3590),O=t(292),_=t.n(O),f=e([h,m]);function j(){let[e,o]=(0,n.useState)(""),[t,r]=(0,n.useState)(""),[l,c]=(0,n.useState)(!1),[O,f]=(0,n.useState)(!1),{signIn:j,isAuthenticated:p,loading:N}=(0,h.O)(),x=(0,a.useRouter)(),v=async o=>{if(o.preventDefault(),!e||!t){m.toast.error("Please fill in all fields");return}c(!0);try{if((await j(e,t)).success){let e=x.query.redirect||"/";x.push(e)}}catch(e){console.error("Login error:",e),m.toast.error("Login failed. Please try again.")}finally{c(!1)}};return N?s.jsx(u.Z,{children:s.jsx("div",{className:_().authContainer,children:s.jsx("div",{className:_().authCard,children:(0,s.jsxs)("div",{className:_().loadingSpinner,children:[s.jsx("div",{className:_().spinner}),s.jsx("p",{children:"Loading..."})]})})})}):p?null:(0,s.jsxs)(u.Z,{children:[(0,s.jsxs)(i(),{children:[s.jsx("title",{children:"Customer Login - Ocean Soul Sparkles"}),s.jsx("meta",{name:"description",content:"Sign in to your Ocean Soul Sparkles customer account to manage bookings and view order history."}),s.jsx("meta",{name:"robots",content:"noindex, nofollow"})]}),(0,s.jsxs)("div",{className:_().authContainer,children:[(0,s.jsxs)("div",{className:_().authCard,children:[(0,s.jsxs)("div",{className:_().authHeader,children:[s.jsx("h1",{children:"Welcome Back"}),s.jsx("p",{children:"Sign in to your customer account"})]}),(0,s.jsxs)("form",{onSubmit:v,className:_().authForm,children:[(0,s.jsxs)("div",{className:_().formGroup,children:[s.jsx("label",{htmlFor:"email",children:"Email Address"}),s.jsx("input",{id:"email",type:"email",value:e,onChange:e=>o(e.target.value),placeholder:"Enter your email",required:!0,disabled:l,className:_().formInput})]}),(0,s.jsxs)("div",{className:_().formGroup,children:[s.jsx("label",{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:_().passwordInput,children:[s.jsx("input",{id:"password",type:O?"text":"password",value:t,onChange:e=>r(e.target.value),placeholder:"Enter your password",required:!0,disabled:l,className:_().formInput}),s.jsx("button",{type:"button",onClick:()=>f(!O),className:_().passwordToggle,disabled:l,children:O?"\uD83D\uDC41️":"\uD83D\uDC41️‍\uD83D\uDDE8️"})]})]}),s.jsx("button",{type:"submit",disabled:l,className:`${_().authButton} ${l?_().loading:""}`,children:l?(0,s.jsxs)(s.Fragment,{children:[s.jsx("span",{className:_().buttonSpinner}),"Signing In..."]}):"Sign In"})]}),(0,s.jsxs)("div",{className:_().authLinks,children:[(0,s.jsxs)("p",{children:["Don't have an account?"," ",s.jsx(d(),{href:"/signup",className:_().authLink,children:"Create Account"})]}),s.jsx("p",{children:s.jsx(d(),{href:"/forgot-password",className:_().authLink,children:"Forgot your password?"})})]}),(0,s.jsxs)("div",{className:_().guestOption,children:[s.jsx("div",{className:_().divider,children:s.jsx("span",{children:"or"})}),(0,s.jsxs)("p",{children:["Want to book without an account?"," ",s.jsx(d(),{href:"/book-online",className:_().authLink,children:"Continue as Guest"})]})]})]}),s.jsx("div",{className:_().authFooter,children:(0,s.jsxs)("p",{children:["By signing in, you agree to our"," ",s.jsx(d(),{href:"/policies#terms",className:_().authLink,children:"Terms of Service"})," ","and"," ",s.jsx(d(),{href:"/policies#privacy",className:_().authLink,children:"Privacy Policy"})]})})]})]})}[h,m]=f.then?(await f)():f,r()}catch(e){r(e)}})},2885:e=>{"use strict";e.exports=require("@supabase/supabase-js")},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var o=require("../webpack-runtime.js");o.C(e);var t=e=>o(o.s=e),r=o.X(0,[899,65,414],()=>t(6990));module.exports=r})();