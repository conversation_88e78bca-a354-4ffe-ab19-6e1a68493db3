/* Booking Page Styles */
.bookingContainer {
  padding-top: 80px; /* Account for fixed header */
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.bookingHeader {
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 40px;
}

.bookingHeader h1 {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 700;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bookingHeader p {
  font-size: clamp(1rem, 2vw, 1.3rem);
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto 40px;
}

.progressIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  max-width: 400px;
  margin: 0 auto;
}

.progressStep {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.progressStep.active {
  opacity: 1;
}

.stepNumber {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.progressStep.active .stepNumber {
  background: white;
  color: #667eea;
  border-color: white;
}

.stepLabel {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.progressLine {
  width: 60px;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
}

.bookingContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 60px;
}

.serviceSelection {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.sectionHeader {
  text-align: center;
  margin-bottom: 40px;
}

.sectionHeader h2 {
  color: #2c3e50;
  font-size: 2rem;
  margin-bottom: 12px;
}

.sectionHeader p {
  color: #6c757d;
  font-size: 1.1rem;
}

.backButton {
  background: transparent;
  color: #3788d8;
  border: 2px solid #3788d8;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 20px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.backButton:hover {
  background: #3788d8;
  color: white;
}

.bookingFormSection {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.selectedServiceSummary {
  margin-bottom: 40px;
}

.serviceSummaryCard {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 24px;
  border-left: 4px solid #3788d8;
}

.serviceSummaryCard h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 12px;
}

.serviceDescription {
  color: #6c757d;
  margin-bottom: 16px;
  line-height: 1.6;
}

.serviceDetails {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.serviceDuration,
.servicePrice {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #495057;
  font-weight: 600;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #6c757d;
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #3788d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.noServices {
  text-align: center;
  padding: 80px 20px;
  color: #6c757d;
}

.noServices h3 {
  color: #495057;
  margin-bottom: 16px;
}

.noServices p {
  margin-bottom: 32px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.accountPrompt {
  background: white;
  border-radius: 16px;
  padding: 40px;
  margin: 40px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.promptCard {
  max-width: 500px;
  margin: 0 auto;
}

.promptCard h3 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.promptCard p {
  color: #6c757d;
  margin-bottom: 24px;
}

.promptActions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.bookingInfo {
  background: white;
  border-radius: 16px;
  padding: 40px;
  margin: 40px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
}

.infoCard {
  text-align: center;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.infoCard:hover {
  transform: translateY(-4px);
}

.infoCard h3 {
  color: #2c3e50;
  font-size: 1.2rem;
  margin-bottom: 12px;
}

.infoCard p {
  color: #6c757d;
  line-height: 1.6;
}

.contactSection {
  background: linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%);
  color: white;
  padding: 60px 40px;
  border-radius: 16px;
  text-align: center;
  margin: 40px 0;
}

.contactSection h3 {
  color: white;
  margin-bottom: 12px;
}

.contactSection p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 24px;
}

.contactOptions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.contactOption {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.contactOption:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .bookingHeader {
    padding: 40px 16px;
  }

  .progressIndicator {
    gap: 12px;
  }

  .stepNumber {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }

  .stepLabel {
    font-size: 12px;
  }

  .progressLine {
    width: 40px;
  }

  .bookingContent {
    padding: 0 16px 40px;
  }

  .serviceSelection,
  .bookingFormSection {
    padding: 24px;
  }

  .serviceSummaryCard {
    padding: 20px;
  }

  .serviceDetails {
    flex-direction: column;
    gap: 12px;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .contactOptions {
    flex-direction: column;
    align-items: center;
  }

  .contactOption {
    width: 250px;
    text-align: center;
  }

  .promptActions {
    flex-direction: column;
    align-items: center;
  }

  .promptActions .button {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .progressIndicator {
    flex-direction: column;
    gap: 16px;
  }

  .progressLine {
    width: 2px;
    height: 30px;
  }

  .stepLabel {
    text-align: center;
  }

  .serviceSelection,
  .bookingFormSection,
  .accountPrompt,
  .bookingInfo {
    margin: 20px 0;
    padding: 20px;
  }

  .contactSection {
    padding: 40px 20px;
    margin: 20px 0;
  }
}
