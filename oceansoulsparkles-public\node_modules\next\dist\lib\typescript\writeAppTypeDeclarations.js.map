{"version": 3, "sources": ["../../../src/lib/typescript/writeAppTypeDeclarations.ts"], "names": ["writeAppTypeDeclarations", "baseDir", "imageImportsEnabled", "hasPagesDir", "hasAppDir", "appTypeDeclarations", "path", "join", "eol", "os", "EOL", "currentC<PERSON>nt", "fs", "readFile", "lf", "indexOf", "directives", "push", "content", "writeFile"], "mappings": ";;;;+BAIsBA;;;eAAAA;;;2DAJP;6DACE;oBACc;;;;;;AAExB,eAAeA,yBAAyB,EAC7CC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,SAAS,EAMV;IACC,yBAAyB;IACzB,MAAMC,sBAAsBC,aAAI,CAACC,IAAI,CAACN,SAAS;IAE/C,iCAAiC;IACjC,IAAIO,MAAMC,WAAE,CAACC,GAAG;IAChB,IAAIC;IAEJ,IAAI;QACFA,iBAAiB,MAAMC,YAAE,CAACC,QAAQ,CAACR,qBAAqB;QACxD,uDAAuD;QACvD,MAAMS,KAAKH,eAAeI,OAAO,CAAC,MAAM,+BAA+B,GAAG;QAE1E,IAAID,OAAO,CAAC,GAAG;YACb,IAAIH,cAAc,CAACG,KAAK,EAAE,KAAK,MAAM;gBACnCN,MAAM;YACR,OAAO;gBACLA,MAAM;YACR;QACF;IACF,EAAE,OAAM,CAAC;IAET;;;;;GAKC,GACD,MAAMQ,aAAuB;QAC3B,oCAAoC;QACpC;KACD;IAED,IAAId,qBAAqB;QACvBc,WAAWC,IAAI,CAAC;IAClB;IAEA,IAAIb,aAAaD,aAAa;QAC5Ba,WAAWC,IAAI,CACb;IAEJ;IAEA,sBAAsB;IACtBD,WAAWC,IAAI,CACb,IACA,2CACA,CAAC,+BAA+B,EAC9Bb,YAAY,QAAQ,QACrB,uEAAuE,CAAC;IAG3E,MAAMc,UAAUF,WAAWT,IAAI,CAACC,OAAOA;IAEvC,+CAA+C;IAC/C,IAAIG,mBAAmBO,SAAS;QAC9B;IACF;IACA,MAAMN,YAAE,CAACO,SAAS,CAACd,qBAAqBa;AAC1C"}