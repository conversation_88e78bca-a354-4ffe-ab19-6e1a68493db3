(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[497],{5497:function(e,o,t){"use strict";t.d(o,{Z:function(){return _}});var a=t(5893),n=t(9008),s=t.n(n),r=t(1664),i=t.n(r),l=t(7294),c=t(1414),d=t.n(c),p=t(1163),f=t(645),m=t.n(f);function u(e){let{href:o,children:t,className:n="",onClick:s,disabled:r=!1,type:c="button"}=e,[d,p]=(0,l.useState)(!1),f={position:"relative",display:"inline-flex",alignItems:"center",justifyContent:"center",gap:"8px",padding:"12px 24px",background:"linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%)",color:"white",border:"none",borderRadius:"25px",fontSize:"16px",fontWeight:"600",textDecoration:"none",cursor:r?"not-allowed":"pointer",transition:"all 0.3s ease",overflow:"hidden",boxShadow:"0 4px 15px rgba(55, 136, 216, 0.3)",opacity:r?.6:1,transform:d&&!r?"translateY(-2px)":"translateY(0)",...d&&!r&&{boxShadow:"0 6px 20px rgba(55, 136, 216, 0.4)"}},u={position:"absolute",top:"50%",left:"50%",width:"100%",height:"100%",background:"radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)",borderRadius:"50%",transform:"translate(-50%, -50%) scale(0)",transition:"transform 0.6s ease",...d&&!r&&{transform:"translate(-50%, -50%) scale(2)"}},h=(0,a.jsxs)("span",{style:f,onMouseEnter:()=>p(!0),onMouseLeave:()=>p(!1),onClick:s,disabled:r,type:c,className:"jsx-92bdf0a77c9b034 "+(n||""),children:[(0,a.jsx)("span",{style:u,className:"jsx-92bdf0a77c9b034"}),(0,a.jsx)("span",{style:{position:"relative",zIndex:1},className:"jsx-92bdf0a77c9b034",children:t}),d&&!r&&(0,a.jsx)("span",{style:{position:"relative",zIndex:1,marginLeft:"4px",animation:"sparkle 0.6s ease-in-out"},className:"jsx-92bdf0a77c9b034",children:"✨"}),(0,a.jsx)(m(),{id:"92bdf0a77c9b034",children:"@-webkit-keyframes sparkle{0%{opacity:0;-webkit-transform:scale(.5);transform:scale(.5)}50%{opacity:1;-webkit-transform:scale(1.2);transform:scale(1.2)}100%{opacity:0;-webkit-transform:scale(1);transform:scale(1)}}@-moz-keyframes sparkle{0%{opacity:0;-moz-transform:scale(.5);transform:scale(.5)}50%{opacity:1;-moz-transform:scale(1.2);transform:scale(1.2)}100%{opacity:0;-moz-transform:scale(1);transform:scale(1)}}@-o-keyframes sparkle{0%{opacity:0;-o-transform:scale(.5);transform:scale(.5)}50%{opacity:1;-o-transform:scale(1.2);transform:scale(1.2)}100%{opacity:0;-o-transform:scale(1);transform:scale(1)}}@keyframes sparkle{0%{opacity:0;-webkit-transform:scale(.5);-moz-transform:scale(.5);-o-transform:scale(.5);transform:scale(.5)}50%{opacity:1;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}100%{opacity:0;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}}"})]});return o&&!r?(0,a.jsx)(i(),{href:o,style:{textDecoration:"none"},children:h}):h}function h(){let e=(0,p.useRouter)(),o=(()=>{let{pathname:o}=e;switch(o){case"/":return{"@context":"https://schema.org","@type":"WebSite",name:"Ocean Soul Sparkles",url:"https://www.oceansoulsparkles.com.au",potentialAction:{"@type":"SearchAction",target:"https://www.oceansoulsparkles.com.au/search?q={search_term_string}","query-input":"required name=search_term_string"}};case"/services":return{"@context":"https://schema.org","@type":"ItemList",name:"Ocean Soul Sparkles Services",description:"Professional face painting, body art, and braiding services",numberOfItems:3,itemListElement:[{"@type":"ListItem",position:1,item:{"@type":"Service",name:"Face Painting",description:"Professional face painting for all ages",provider:{"@type":"LocalBusiness",name:"Ocean Soul Sparkles"}}},{"@type":"ListItem",position:2,item:{"@type":"Service",name:"Airbrush Body Art",description:"Stunning airbrush body art and temporary tattoos",provider:{"@type":"LocalBusiness",name:"Ocean Soul Sparkles"}}},{"@type":"ListItem",position:3,item:{"@type":"Service",name:"Festival Braiding",description:"Colorful braiding perfect for festivals and events",provider:{"@type":"LocalBusiness",name:"Ocean Soul Sparkles"}}}]};case"/shop":return{"@context":"https://schema.org","@type":"Store",name:"Ocean Soul Sparkles Shop",description:"Eco-friendly glitter, face paints, and beauty products",url:"https://www.oceansoulsparkles.com.au/shop",parentOrganization:{"@type":"LocalBusiness",name:"Ocean Soul Sparkles"}};case"/book-online":return{"@context":"https://schema.org","@type":"ReservationAction",name:"Book Ocean Soul Sparkles Service",description:"Book face painting, body art, or braiding services online",target:"https://www.oceansoulsparkles.com.au/book-online",provider:{"@type":"LocalBusiness",name:"Ocean Soul Sparkles"}};default:return null}})();return(0,a.jsxs)(s(),{children:[(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"LocalBusiness",name:"Ocean Soul Sparkles",description:"Professional face painting, airbrush body art, and braiding services in Melbourne. Eco-friendly and sustainable glitter options available.",url:"https://www.oceansoulsparkles.com.au",telephone:"+61-XXX-XXX-XXX",email:"<EMAIL>",address:{"@type":"PostalAddress",addressLocality:"Melbourne",addressRegion:"Victoria",addressCountry:"AU"},geo:{"@type":"GeoCoordinates",latitude:-37.8136,longitude:144.9631},openingHours:["Mo-Fr 09:00-17:00","Sa 09:00-15:00"],priceRange:"$$",paymentAccepted:["Cash","Credit Card","Square"],currenciesAccepted:"AUD",serviceArea:{"@type":"GeoCircle",geoMidpoint:{"@type":"GeoCoordinates",latitude:-37.8136,longitude:144.9631},geoRadius:"50000"},sameAs:["https://www.facebook.com/OceanSoulSparkles/","https://www.instagram.com/oceansoulsparkles"],hasOfferCatalog:{"@type":"OfferCatalog",name:"Ocean Soul Sparkles Services",itemListElement:[{"@type":"Offer",itemOffered:{"@type":"Service",name:"Face Painting",description:"Professional face painting for all ages and events"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Airbrush Body Art",description:"Stunning airbrush body art and temporary tattoos"}},{"@type":"Offer",itemOffered:{"@type":"Service",name:"Festival Braiding",description:"Colorful braiding perfect for festivals and events"}}]}})}}),o&&(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(o)}})]})}function g(){let[e,o]=(0,l.useState)(!1),[t,n]=(0,l.useState)("default");(0,l.useEffect)(()=>{if("Notification"in window){n(Notification.permission);let e=localStorage.getItem("notification-prompt-shown"),t=Date.now();"default"===Notification.permission&&(!e||t-parseInt(e)>6048e5)&&setTimeout(()=>{o(!0)},5e3)}},[]);let s=async()=>{try{let e=await Notification.requestPermission();n(e),o(!1),localStorage.setItem("notification-prompt-shown",Date.now().toString()),"granted"===e&&new Notification("Ocean Soul Sparkles",{body:"Notifications enabled! We'll keep you updated on your bookings.",icon:"/images/icons/icon-192x192.png",badge:"/images/icons/icon-72x72.png"})}catch(e){console.error("Error requesting notification permission:",e),o(!1)}};return e&&"default"===t?(0,a.jsxs)("div",{style:{position:"fixed",bottom:"20px",left:"20px",right:"20px",maxWidth:"400px",margin:"0 auto",background:"white",border:"1px solid #dee2e6",borderRadius:"12px",padding:"20px",boxShadow:"0 4px 20px rgba(0, 0, 0, 0.15)",zIndex:1e3,animation:"slideUp 0.3s ease-out"},className:"jsx-67bc6494e075165d",children:[(0,a.jsxs)("div",{style:{marginBottom:"16px"},className:"jsx-67bc6494e075165d",children:[(0,a.jsx)("h4",{style:{margin:"0 0 8px 0",color:"#2c3e50",fontSize:"16px",fontWeight:"600"},className:"jsx-67bc6494e075165d",children:"Stay Updated! \uD83D\uDD14"}),(0,a.jsx)("p",{style:{margin:0,color:"#6c757d",fontSize:"14px",lineHeight:"1.4"},className:"jsx-67bc6494e075165d",children:"Get notified about booking confirmations, appointment reminders, and special offers."})]}),(0,a.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},className:"jsx-67bc6494e075165d",children:[(0,a.jsx)("button",{onClick:()=>{o(!1),localStorage.setItem("notification-prompt-shown",Date.now().toString())},style:{background:"transparent",border:"1px solid #dee2e6",color:"#6c757d",padding:"8px 16px",borderRadius:"6px",fontSize:"14px",cursor:"pointer",transition:"all 0.2s ease"},onMouseOver:e=>{e.target.style.background="#f8f9fa"},onMouseOut:e=>{e.target.style.background="transparent"},className:"jsx-67bc6494e075165d",children:"Not Now"}),(0,a.jsx)("button",{onClick:s,style:{background:"linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%)",border:"none",color:"white",padding:"8px 16px",borderRadius:"6px",fontSize:"14px",fontWeight:"600",cursor:"pointer",transition:"all 0.2s ease"},onMouseOver:e=>{e.target.style.transform="translateY(-1px)",e.target.style.boxShadow="0 4px 12px rgba(55, 136, 216, 0.3)"},onMouseOut:e=>{e.target.style.transform="translateY(0)",e.target.style.boxShadow="none"},className:"jsx-67bc6494e075165d",children:"Enable Notifications"})]}),(0,a.jsx)(m(),{id:"67bc6494e075165d",children:"@-webkit-keyframes slideUp{from{-webkit-transform:translatey(100%);transform:translatey(100%);opacity:0}to{-webkit-transform:translatey(0);transform:translatey(0);opacity:1}}@-moz-keyframes slideUp{from{-moz-transform:translatey(100%);transform:translatey(100%);opacity:0}to{-moz-transform:translatey(0);transform:translatey(0);opacity:1}}@-o-keyframes slideUp{from{-o-transform:translatey(100%);transform:translatey(100%);opacity:0}to{-o-transform:translatey(0);transform:translatey(0);opacity:1}}@keyframes slideUp{from{-webkit-transform:translatey(100%);-moz-transform:translatey(100%);-o-transform:translatey(100%);transform:translatey(100%);opacity:0}to{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:1}}"})]}):null}function y(e){let{children:o}=e;(0,l.useEffect)(()=>{a()},[]);let a=async()=>{try{let e=(await t.e(74).then(t.bind(t,5074))).default;await e.init({appId:"687cfeae-1fec-4240-b094-1ce79ebcee51",safari_web_id:"web.onesignal.auto.************************************",notifyButton:{enable:!1},allowLocalhostAsSecureOrigin:!1,autoRegister:!1,autoResubscribe:!0,persistNotification:!1,promptOptions:{slidedown:{enabled:!1},fullscreen:{enabled:!1}},welcomeNotification:{disable:!0},notificationClickHandlerMatch:"origin",notificationClickHandlerAction:"navigate"}),console.log("[OneSignal] Initialized for customer notifications"),e.on("subscriptionChange",function(o){console.log("[OneSignal] Subscription changed:",o),o&&(e.sendTag("user_type","customer"),e.sendTag("source","public_website"))}),e.on("notificationPermissionChange",function(e){console.log("[OneSignal] Permission changed:",e)})}catch(e){console.error("[OneSignal] Initialization failed:",e)}};return o}var x=t(7061);function b(){return(0,a.jsxs)(s(),{children:[(0,a.jsx)("meta",{name:"google-site-verification",content:"HtjqFmAXzFBvlS4IEWJe8iyN_UK4dROGIIt_oY-i1Ag"}),x.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("script",{async:!0,src:"https://www.googletagmanager.com/gtag/js?id=".concat(x.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID)}),(0,a.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n                window.dataLayer = window.dataLayer || [];\n                function gtag(){dataLayer.push(arguments);}\n                gtag('js', new Date());\n                gtag('config', '".concat(x.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,"', {\n                  page_title: document.title,\n                  page_location: window.location.href,\n                  anonymize_ip: true,\n                  allow_google_signals: false,\n                  allow_ad_personalization_signals: false\n                });\n              ")}})]})]})}function _(e){let{children:o}=e,[t,n]=(0,l.useState)(!1),[r,c]=(0,l.useState)(!1),[f,m]=(0,l.useState)(!1),x=(0,p.useRouter)();(0,l.useEffect)(()=>{let e=()=>{window.scrollY>500?n(!0):n(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,l.useEffect)(()=>{c(!1)},[x.pathname]),(0,l.useEffect)(()=>{let e=()=>{m(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let _=[{label:"Home",href:"/"},{label:"About Us",href:"/about"},{label:"Services",href:"/services"},{label:"Gallery",href:"/gallery"},{label:"Shop",href:"/shop"},{label:"Gift Card",href:"/gift-card"},{label:"Contact",href:"/contact"}],{asPath:w}=x;(0,l.useEffect)(()=>{console.log=()=>{},console.warn=()=>{},console.error=()=>{}},[]);let k=(0,a.jsxs)("div",{className:d().container,children:[(0,a.jsx)(s(),{children:(0,a.jsx)("link",{rel:"canonical",href:"https://www.oceansoulsparkles.com.au".concat(w)})}),(0,a.jsx)(b,{}),(0,a.jsx)(h,{}),(0,a.jsx)("header",{className:"".concat(d().header," ").concat(f?d().scrolled:""),children:(0,a.jsxs)("div",{className:d().headerContainer,children:[(0,a.jsx)(i(),{href:"/",className:d().logo,children:(0,a.jsx)("img",{src:"/images/bannerlogo.PNG",alt:"Ocean Soul Sparkles"})}),(0,a.jsxs)("button",{className:"".concat(d().mobileToggle," ").concat(r?d().active:""),onClick:()=>{c(!r)},"aria-label":"Toggle Navigation",children:[(0,a.jsx)("span",{}),(0,a.jsx)("span",{}),(0,a.jsx)("span",{})]}),(0,a.jsx)("nav",{className:"".concat(d().nav," ").concat(r?d().open:""),children:(0,a.jsx)("ul",{children:_.map((e,o)=>(0,a.jsx)("li",{className:x.pathname===e.href?d().active:"",children:(0,a.jsx)(i(),{href:e.href,children:e.label})},o))})}),(0,a.jsx)("div",{className:d().bookNowContainer,children:(0,a.jsx)(u,{href:"/book-online",className:d().bookNowButton,children:"Book Now"})})]})}),o,(0,a.jsx)(g,{}),(0,a.jsxs)("footer",{className:d().footer,children:[(0,a.jsx)("div",{className:d().footerWave,children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 320",preserveAspectRatio:"none",children:(0,a.jsx)("path",{fill:"#4ECDC4",fillOpacity:"0.2",d:"M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"})})}),(0,a.jsx)("div",{className:d().footerContent,children:(0,a.jsxs)("div",{className:d().footerGrid,children:[(0,a.jsxs)("div",{className:d().footerBranding,children:[(0,a.jsx)(i(),{href:"/",children:(0,a.jsx)("div",{className:d().rainbowLogoText,children:"OceanSoulSparkles"})}),(0,a.jsx)("p",{className:d().footerTagline,children:(0,a.jsx)("span",{className:"accent-text",children:'"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!"'})}),(0,a.jsxs)("div",{className:d().footerSocial,children:[(0,a.jsx)("a",{href:"https://www.instagram.com/oceansoulsparkles",target:"_blank",rel:"noopener noreferrer",className:d().footerSocialLink,children:(0,a.jsx)("img",{src:"/images/social/instagram-icon.png",alt:"Instagram"})}),(0,a.jsx)("a",{href:"https://www.facebook.com/OceanSoulSparkles/",target:"_blank",rel:"noopener noreferrer",className:d().footerSocialLink,children:(0,a.jsx)("img",{src:"/images/social/facebook-icon.png",alt:"Facebook"})})]})]}),(0,a.jsxs)("div",{className:d().footerLinks,children:[(0,a.jsx)("h3",{children:"Quick Links"}),(0,a.jsxs)("ul",{children:[_.map((e,o)=>(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:e.href,className:d().footerLink,children:e.label})},o)),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/book-online",className:d().footerLink,children:"Book Online"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/book-events",className:d().footerLink,children:"Events Booking"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/policies#return-policy",className:d().footerLink,children:"Return & Refund Policy"})}),(0,a.jsx)("li",{children:(0,a.jsx)(i(),{href:"/policies#shipping-info",className:d().footerLink,children:"Shipping Information"})})]})]}),(0,a.jsxs)("div",{className:d().footerContact,children:[(0,a.jsx)("h3",{children:"Contact Us"}),(0,a.jsx)("p",{children:(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:d().footerContactLink,children:"<EMAIL>"})}),(0,a.jsx)("p",{children:"Melbourne, Victoria"}),(0,a.jsx)(i(),{href:"/contact",className:"".concat(d().footerButton," button button--outline"),children:"Get in Touch"})]})]})}),(0,a.jsxs)("div",{className:d().footerPayments,children:[(0,a.jsx)("h3",{children:"Payment Methods"}),(0,a.jsx)("div",{className:d().paymentLogos,style:{justifyContent:"center"},children:(0,a.jsx)("img",{src:"/images/logos/square.png",alt:"Square Payments",width:150,height:40,style:{margin:"0 auto"}})}),(0,a.jsxs)("p",{className:d().securePaymentNote,children:[(0,a.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z",fill:"#4CAF50"})}),(0,a.jsx)("span",{children:"Secure payment processing"})]})]}),(0,a.jsxs)("div",{className:d().footerBottom,children:[(0,a.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," by OceanSoulSparkles. All rights reserved."]}),(0,a.jsx)("p",{children:"Proudly created with Next.js"})]}),t&&(0,a.jsx)("button",{className:d().scrollToTop,onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Scroll to top",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,a.jsx)("polyline",{points:"18 15 12 9 6 15"})})})]})]});return(0,a.jsx)(y,{children:k})}},1414:function(e){e.exports={container:"Layout_container__l2gjk",header:"Layout_header__8XhYm",scrolled:"Layout_scrolled__1D_i_",headerContainer:"Layout_headerContainer__eAYZP",logo:"Layout_logo__Yfd0y",nav:"Layout_nav__qOLUE",active:"Layout_active__obJLs",mobileToggle:"Layout_mobileToggle__bqS4s",bookNowContainer:"Layout_bookNowContainer__D_oIc",bookNowButton:"Layout_bookNowButton__pO3H4",footer:"Layout_footer__3v8iv",footerWave:"Layout_footerWave__gTMUT",footerContent:"Layout_footerContent__Vp3O_",footerGrid:"Layout_footerGrid__ROpY2",footerBranding:"Layout_footerBranding__vLK5u",footerTagline:"Layout_footerTagline__unxmY",footerSocial:"Layout_footerSocial__7W9Az",footerSocialLink:"Layout_footerSocialLink__ldprt",footerLinks:"Layout_footerLinks__IW6OI",footerContact:"Layout_footerContact__vYR0Y",footerLink:"Layout_footerLink__rBV0V",footerContactLink:"Layout_footerContactLink__v_olr",footerButton:"Layout_footerButton__yJruW",footerPayments:"Layout_footerPayments__xJs2e",paymentLogos:"Layout_paymentLogos__MQM1p",securePaymentNote:"Layout_securePaymentNote__7s5Lz",footerBottom:"Layout_footerBottom__CYzGn",scrollToTop:"Layout_scrollToTop__8Mp58",open:"Layout_open__pZ2tV"}}}]);