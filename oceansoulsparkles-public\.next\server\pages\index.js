"use strict";(()=>{var e={};e.id=405,e.ids=[405,660],e.modules={6030:(e,o,r)=>{r.a(e,async(e,s)=>{try{r.r(o),r.d(o,{config:()=>N,default:()=>m,getServerSideProps:()=>f,getStaticPaths:()=>h,getStaticProps:()=>O,reportWebVitals:()=>j,routeModule:()=>y,unstable_getServerProps:()=>U,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>D,unstable_getStaticPaths:()=>_,unstable_getStaticProps:()=>E});var n=r(7093),t=r(5244),a=r(1323),c=r(2899),l=r.n(c),i=r(3414),d=r(2285),u=e([i]);i=(u.then?(await u)():u)[0];let m=(0,a.l)(d,"default"),O=(0,a.l)(d,"getStaticProps"),h=(0,a.l)(d,"getStaticPaths"),f=(0,a.l)(d,"getServerSideProps"),N=(0,a.l)(d,"config"),j=(0,a.l)(d,"reportWebVitals"),E=(0,a.l)(d,"unstable_getStaticProps"),_=(0,a.l)(d,"unstable_getStaticPaths"),D=(0,a.l)(d,"unstable_getStaticParams"),U=(0,a.l)(d,"unstable_getServerProps"),v=(0,a.l)(d,"unstable_getServerSideProps"),y=new n.PagesRouteModule({definition:{kind:t.x.PAGES,page:"/index",pathname:"/",bundlePath:"",filename:""},components:{App:i.default,Document:l()},userland:d});s()}catch(e){s(e)}})},1398:(e,o,r)=>{r.d(o,{Z:()=>d});var s=r(997),n=r(968),t=r.n(n),a=r(1664),c=r.n(a),l=r(6689);!function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(1163);function d({children:e}){let[o,r]=(0,l.useState)(!1),[n,a]=(0,l.useState)(!1),[d,u]=(0,l.useState)(!1),m=(0,i.useRouter)(),O=[{label:"Home",href:"/"},{label:"About Us",href:"/about"},{label:"Services",href:"/services"},{label:"Gallery",href:"/gallery"},{label:"Shop",href:"/shop"},{label:"Gift Card",href:"/gift-card"},{label:"Contact",href:"/contact"}],{asPath:h}=m,f=`https://www.oceansoulsparkles.com.au${h}`,N=(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(t(),{children:s.jsx("link",{rel:"canonical",href:f})}),s.jsx(Object(function(){var e=Error("Cannot find module './GoogleVerification'");throw e.code="MODULE_NOT_FOUND",e}()),{}),s.jsx(Object(function(){var e=Error("Cannot find module './StructuredData/SchemaManager'");throw e.code="MODULE_NOT_FOUND",e}()),{}),s.jsx("header",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${d?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,children:(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(c(),{href:"/",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/images/bannerlogo.PNG",alt:"Ocean Soul Sparkles"})}),(0,s.jsxs)("button",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${n?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,onClick:()=>{a(!n)},"aria-label":"Toggle Navigation",children:[s.jsx("span",{}),s.jsx("span",{}),s.jsx("span",{})]}),s.jsx("nav",{className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} ${n?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):""}`,children:s.jsx("ul",{children:O.map((e,o)=>s.jsx("li",{className:m.pathname===e.href?Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()):"",children:s.jsx(c(),{href:e.href,children:e.label})},o))})}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx(Object(function(){var e=Error("Cannot find module './SparkleButton'");throw e.code="MODULE_NOT_FOUND",e}()),{href:"/book-online",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Book Now"})})]})}),e,s.jsx(Object(function(){var e=Error("Cannot find module './NotificationPrompt'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,s.jsxs)("footer",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 320",preserveAspectRatio:"none",children:s.jsx("path",{fill:"#4ECDC4",fillOpacity:"0.2",d:"M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"})})}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(c(),{href:"/",children:s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"OceanSoulSparkles"})}),s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{className:"accent-text",children:'"Bring the Joy of Creativity to Your Life with OceanSoulSparkles Facepainting!"'})}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("a",{href:"https://www.instagram.com/oceansoulsparkles",target:"_blank",rel:"noopener noreferrer",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/images/social/instagram-icon.png",alt:"Instagram"})}),s.jsx("a",{href:"https://www.facebook.com/OceanSoulSparkles/",target:"_blank",rel:"noopener noreferrer",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/images/social/facebook-icon.png",alt:"Facebook"})})]})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("h3",{children:"Quick Links"}),(0,s.jsxs)("ul",{children:[O.map((e,o)=>s.jsx("li",{children:s.jsx(c(),{href:e.href,className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:e.label})},o)),s.jsx("li",{children:s.jsx(c(),{href:"/book-online",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Book Online"})}),s.jsx("li",{children:s.jsx(c(),{href:"/book-events",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Events Booking"})}),s.jsx("li",{children:s.jsx(c(),{href:"/policies#return-policy",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Return & Refund Policy"})}),s.jsx("li",{children:s.jsx(c(),{href:"/policies#shipping-info",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Shipping Information"})})]})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("h3",{children:"Contact Us"}),s.jsx("p",{children:s.jsx("a",{href:"mailto:<EMAIL>",className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"<EMAIL>"})}),s.jsx("p",{children:"Melbourne, Victoria"}),s.jsx(c(),{href:"/contact",className:`${Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}())} button button--outline`,children:"Get in Touch"})]})]})}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("h3",{children:"Payment Methods"}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),style:{justifyContent:"center"},children:s.jsx("img",{src:"/images/logos/square.png",alt:"Square Payments",width:150,height:40,style:{margin:"0 auto"}})}),(0,s.jsxs)("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z",fill:"#4CAF50"})}),s.jsx("span",{children:"Secure payment processing"})]})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," by OceanSoulSparkles. All rights reserved."]}),s.jsx("p",{children:"Proudly created with Next.js"})]}),o&&s.jsx("button",{className:Object(function(){var e=Error("Cannot find module '@/styles/Layout.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Scroll to top",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:s.jsx("polyline",{points:"18 15 12 9 6 15"})})})]})]});return s.jsx(Object(function(){var e=Error("Cannot find module './OneSignalProvider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:N})}(function(){var e=Error("Cannot find module './SparkleButton'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module './StructuredData/SchemaManager'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './NotificationPrompt'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './OneSignalProvider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module './GoogleVerification'");throw e.code="MODULE_NOT_FOUND",e}()},2285:(e,o,r)=>{r.r(o),r.d(o,{default:()=>d});var s=r(997),n=r(968),t=r.n(n),a=r(1664),c=r.n(a),l=r(6689);!function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(1398);function d(){let e=(0,l.useRef)(null);return(0,s.jsxs)(i.Z,{children:[(0,s.jsxs)(t(),{children:[s.jsx("title",{children:"Ocean Soul Sparkles - Face Painting & Body Art Services"}),s.jsx("meta",{name:"description",content:"Professional face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne. Eco-friendly and sustainable glitter options available."}),s.jsx("meta",{name:"keywords",content:"face painting, body art, airbrush, braiding, melbourne, events, festivals, eco-friendly glitter"}),s.jsx("meta",{name:"google-site-verification",content:"HtjqFmAXzFBvlS4IEWJe8iyN_UK4dROGIIt_oY-i1Ag"})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/SEO/PageSEO'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"OceanSoulSparkles | Melbourne Facepaint & Entertainment",description:"OceanSoulSparkles offers face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne. Eco-friendly and sustainable glitter options available.",ogImage:"https://www.oceansoulsparkles.com.au/images/gallery/gallery-1.jpg"}),(0,s.jsxs)("main",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/HeroSection'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"A world of sparkling self expression awaits you.",subtitle:"We exist to help you unleash your creativity and SHINE. Let's create something wild & magical together.",backgroundImage:"/UV-Generic-Psychadelia.jpg",ctaText:"Sparkle Up",ctaLink:"/book-online",secondaryCtaText:"Our Services",secondaryCtaLink:"#services",height:"100vh"}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/OceanSparklesShowcase'");throw e.code="MODULE_NOT_FOUND",e}()),{title:"Dive Into a World of Magical Artistry",subtitle:"Express yourself with vibrant colors, dazzling sparkles, and creative designs that capture your unique spirit",ctaText:"Let's Create Magic",ctaLink:"/book-online"}),(0,s.jsxs)("section",{id:"services",ref:e,className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"fade-in",children:[s.jsx("h2",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Our Services"}),s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"We offer a range of services to suit all needs - hit the link to see more!"})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/StaggeredList'");throw e.code="MODULE_NOT_FOUND",e}()),{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),staggerDelay:150,children:[(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/Airbrush Face Body Painting.png",alt:"Airbrush Face & Body Painting",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})}),s.jsx("h3",{children:"Airbrush Face & Body Painting"}),s.jsx("p",{children:"Add flair and colour to any event with our airbrush face and body painting. From intricate designs to bold statements, we tailor our artistry to suit your theme or outfit. Perfect for festivals, birthdays, and corporate gatherings."}),s.jsx(c(),{href:"/services",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Learn More"})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/images/services/festival-braids.jpg",alt:"Braiding",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})}),s.jsx("h3",{children:"Braiding"}),s.jsx("p",{children:"Transform your look with our vibrant braiding services, including festival-ready styles and coloured extensions to match your vibe. We offer single bookings, braid parties, and festival services, starting from $60."}),s.jsx(c(),{href:"/services",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Learn More"})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/Airbrush Temporary Tattoos.png",alt:"Airbrush Temporary Tattoos",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})}),s.jsx("h3",{children:"Airbrush Temporary Tattoos"}),s.jsx("p",{children:"Explore our stunning airbrush temporary tattoos that last for days! Perfect for events, parties, or just trying out a new look without the commitment. Our designs range from delicate and intricate to bold and eye-catching."}),s.jsx(c(),{href:"/services",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Learn More"})]})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"fade-in",delay:300,children:s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx(c(),{href:"/services",className:"button",children:"Explore All Services"})})})]}),(0,s.jsxs)("section",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"slide-right",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("h2",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Shine Sustainably With Eco-Friendly Glitter"}),s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"We believe in creating magic without harming the planet. Sustainability is at the heart of everything we do, and that's why all our glitter is 100% biodegradable, made from eucalyptus trees, and even vegan-friendly!"}),s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Our eco-friendly glitter takes just six weeks to decompose in soil and breaks down even faster in water, ensuring it leaves no harmful trace. We're proud to be as friendly to the planet as we are to our customers, and to provide a way for both you and the earth to shine!"}),s.jsx(c(),{href:"/shop",className:"button button--secondary mt-4",children:"Shop Eco Glitter"})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"slide-left",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("img",{src:"/images/products/biodegradable-glitter.jpg",alt:"Eco-Friendly Biodegradable Glitter",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})})]}),(0,s.jsxs)("section",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"fade-in",children:[s.jsx("h2",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Our Gallery"}),s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Take a peek at some of our magical creations"})]}),(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/StaggeredList'");throw e.code="MODULE_NOT_FOUND",e}()),{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),staggerDelay:100,baseDelay:200,children:[(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("img",{src:"/images/gallery/gallery-1.jpg",alt:"Ocean Soul Sparkles Gallery",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{children:"Face Painting"})})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("img",{src:"/images/gallery/gallery-2.jpg",alt:"Ocean Soul Sparkles Gallery",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{children:"Festival Makeup"})})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("img",{src:"/images/gallery/gallery-3.jpg",alt:"Ocean Soul Sparkles Gallery",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{children:"Airbrush Art"})})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("img",{src:"/images/gallery/gallery-4.jpg",alt:"Ocean Soul Sparkles Gallery",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{children:"Kids Designs"})})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("img",{src:"/images/gallery/gallery-5.jpg",alt:"Ocean Soul Sparkles Gallery",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{children:"Braiding"})})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("img",{src:"/images/gallery/gallery-6.jpg",alt:"Ocean Soul Sparkles Gallery",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{children:"Glitter Art"})})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("img",{src:"/images/gallery/gallery-7.jpg",alt:"Ocean Soul Sparkles Gallery",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{children:"Special Events"})})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("img",{src:"/images/products/biodegradable-glitter.jpg",alt:"Ocean Soul Sparkles Gallery",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())}),s.jsx("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:s.jsx("span",{children:"Eco Products"})})]})]}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"fade-in",delay:500,children:(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(c(),{href:"/gallery",className:"button",children:"View Full Gallery"}),s.jsx(c(),{href:"/book-online",className:"button button--outline ml-4",children:"Book Your Experience"})]})})]}),(0,s.jsxs)("section",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),id:"contact",children:[(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"fade-in",children:[s.jsx("h2",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Get In Touch"}),s.jsx("p",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:"Have questions or ready to book? Send us a message!"})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"slide-right",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("h3",{children:"Contact Information"}),s.jsx("p",{children:"We'd love to hear from you! Whether you have questions about our services or want to book an appointment, we're here to help."}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("path",{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}),s.jsx("polyline",{points:"22,6 12,13 2,6"})]}),s.jsx("a",{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),s.jsx("circle",{cx:"12",cy:"10",r:"3"})]}),s.jsx("span",{children:"Melbourne, Victoria"})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("a",{href:"https://www.instagram.com/oceansoulsparkles",target:"_blank",rel:"noopener noreferrer",children:s.jsx("img",{src:"/images/social/instagram-icon.png",alt:"Instagram"})}),s.jsx("a",{href:"https://www.facebook.com/OceanSoulSparkles/",target:"_blank",rel:"noopener noreferrer",children:s.jsx("img",{src:"/images/social/facebook-icon.png",alt:"Facebook"})})]})]})}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}()),{animation:"slide-left",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:(0,s.jsxs)("form",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("label",{htmlFor:"name",children:"Name"}),s.jsx("input",{id:"name",type:"text",placeholder:"Your Name",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("label",{htmlFor:"email",children:"Email"}),s.jsx("input",{id:"email",type:"email",placeholder:"Your Email",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})]})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("label",{htmlFor:"subject",children:"Subject"}),s.jsx("input",{id:"subject",type:"text",placeholder:"Subject",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("label",{htmlFor:"phone",children:"Phone"}),s.jsx("input",{id:"phone",type:"tel",placeholder:"Your Phone",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})]})]}),(0,s.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}()),children:[s.jsx("label",{htmlFor:"message",children:"Message"}),s.jsx("textarea",{id:"message",placeholder:"Your Message",className:Object(function(){var e=Error("Cannot find module '@/styles/Home.module.css'");throw e.code="MODULE_NOT_FOUND",e}())})]}),s.jsx("button",{type:"submit",className:"button",children:"Send Message"})]})})]})]})]})]})}(function(){var e=Error("Cannot find module '@/components/HeroSection'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/AnimatedSection'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/StaggeredList'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/OceanSparklesShowcase'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/SEO/PageSEO'");throw e.code="MODULE_NOT_FOUND",e}()},2885:e=>{e.exports=require("@supabase/supabase-js")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},3590:e=>{e.exports=import("react-toastify")}};var o=require("../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),s=o.X(0,[899,65,414],()=>r(6030));module.exports=s})();