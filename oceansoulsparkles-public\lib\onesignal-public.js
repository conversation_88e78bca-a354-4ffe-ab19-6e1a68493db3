/**
 * OneSignal Public Integration
 * Customer-only notification system for the public subdomain
 */

let OneSignal = null;

/**
 * Initialize OneSignal for customer notifications
 */
export async function initializeOneSignalPublic() {
  if (typeof window === 'undefined' || !process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID) {
    return;
  }

  try {
    // Dynamically import OneSignal to avoid SSR issues
    OneSignal = (await import('react-onesignal')).default;
    
    await OneSignal.init({
      appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
      safari_web_id: process.env.NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID,
      allowLocalhostAsSecureOrigin: process.env.NODE_ENV === 'development',
      autoRegister: false,
      autoResubscribe: true,
      persistNotification: false,
      promptOptions: {
        slidedown: { enabled: false },
        fullscreen: { enabled: false },
      },
      welcomeNotification: { disable: true },
      notificationClickHandlerMatch: 'origin',
      notificationClickHandlerAction: 'navigate',
    });

    console.log('[OneSignal Public] Initialized successfully');

    // Set up customer-specific event listeners
    setupCustomerEventListeners();

  } catch (error) {
    console.error('[OneSignal Public] Initialization failed:', error);
    throw error;
  }
}

/**
 * Set up event listeners for customer notifications
 */
function setupCustomerEventListeners() {
  if (!OneSignal) return;

  OneSignal.on('subscriptionChange', function(isSubscribed) {
    console.log('[OneSignal Public] Subscription changed:', isSubscribed);
    
    if (isSubscribed) {
      // Tag user as customer
      OneSignal.sendTag('user_type', 'customer');
      OneSignal.sendTag('source', 'public_website');
      OneSignal.sendTag('subscribed_at', new Date().toISOString());
    }
  });

  OneSignal.on('notificationPermissionChange', function(permission) {
    console.log('[OneSignal Public] Permission changed:', permission);
  });

  OneSignal.on('notificationDisplay', function(event) {
    console.log('[OneSignal Public] Notification displayed:', event);
  });

  OneSignal.on('notificationDismiss', function(event) {
    console.log('[OneSignal Public] Notification dismissed:', event);
  });
}

/**
 * Request notification permission from customer
 */
export async function requestNotificationPermission() {
  if (!OneSignal) {
    throw new Error('OneSignal not initialized');
  }

  try {
    const permission = await OneSignal.registerForPushNotifications();
    console.log('[OneSignal Public] Permission granted:', permission);
    return permission;
  } catch (error) {
    console.error('[OneSignal Public] Permission request failed:', error);
    throw error;
  }
}

/**
 * Check if user is subscribed to notifications
 */
export async function isSubscribed() {
  if (!OneSignal) return false;

  try {
    return await OneSignal.isPushNotificationsEnabled();
  } catch (error) {
    console.error('[OneSignal Public] Error checking subscription:', error);
    return false;
  }
}

/**
 * Get user's OneSignal ID
 */
export async function getUserId() {
  if (!OneSignal) return null;

  try {
    return await OneSignal.getUserId();
  } catch (error) {
    console.error('[OneSignal Public] Error getting user ID:', error);
    return null;
  }
}

/**
 * Send tags for customer segmentation
 */
export async function sendCustomerTags(tags) {
  if (!OneSignal) return;

  try {
    // Only allow customer-safe tags
    const allowedTags = {
      customer_id: tags.customer_id,
      email: tags.email,
      first_name: tags.first_name,
      booking_preferences: tags.booking_preferences,
      location: tags.location,
      last_booking: tags.last_booking
    };

    // Filter out undefined values
    const filteredTags = Object.fromEntries(
      Object.entries(allowedTags).filter(([_, value]) => value !== undefined)
    );

    await OneSignal.sendTags(filteredTags);
    console.log('[OneSignal Public] Customer tags sent:', filteredTags);
  } catch (error) {
    console.error('[OneSignal Public] Error sending tags:', error);
  }
}

/**
 * Send a test notification (development only)
 */
export async function sendTestNotification() {
  if (process.env.NODE_ENV !== 'development' || !OneSignal) {
    return;
  }

  try {
    // This would typically be done from the backend
    console.log('[OneSignal Public] Test notification would be sent from backend');
  } catch (error) {
    console.error('[OneSignal Public] Error sending test notification:', error);
  }
}

/**
 * Unsubscribe from notifications
 */
export async function unsubscribe() {
  if (!OneSignal) return;

  try {
    await OneSignal.setSubscription(false);
    console.log('[OneSignal Public] Unsubscribed successfully');
  } catch (error) {
    console.error('[OneSignal Public] Error unsubscribing:', error);
    throw error;
  }
}

/**
 * Get notification permission status
 */
export async function getNotificationPermission() {
  if (!OneSignal) return 'default';

  try {
    return await OneSignal.getNotificationPermission();
  } catch (error) {
    console.error('[OneSignal Public] Error getting permission:', error);
    return 'default';
  }
}
