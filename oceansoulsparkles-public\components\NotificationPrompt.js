import { useState, useEffect } from 'react';

export default function NotificationPrompt() {
  const [showPrompt, setShowPrompt] = useState(false);
  const [permission, setPermission] = useState('default');

  useEffect(() => {
    // Check if notifications are supported
    if ('Notification' in window) {
      setPermission(Notification.permission);
      
      // Show prompt if permission is default and user hasn't been asked recently
      const lastPrompt = localStorage.getItem('notification-prompt-shown');
      const now = Date.now();
      const oneWeek = 7 * 24 * 60 * 60 * 1000; // 1 week in milliseconds
      
      if (Notification.permission === 'default' && 
          (!lastPrompt || now - parseInt(lastPrompt) > oneWeek)) {
        // Show prompt after a delay to not be intrusive
        setTimeout(() => {
          setShowPrompt(true);
        }, 5000);
      }
    }
  }, []);

  const requestPermission = async () => {
    try {
      const result = await Notification.requestPermission();
      setPermission(result);
      setShowPrompt(false);
      
      // Remember that we showed the prompt
      localStorage.setItem('notification-prompt-shown', Date.now().toString());
      
      if (result === 'granted') {
        // Show a test notification
        new Notification('Ocean Soul Sparkles', {
          body: 'Notifications enabled! We\'ll keep you updated on your bookings.',
          icon: '/images/icons/icon-192x192.png',
          badge: '/images/icons/icon-72x72.png'
        });
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setShowPrompt(false);
    }
  };

  const dismissPrompt = () => {
    setShowPrompt(false);
    // Remember that we showed the prompt
    localStorage.setItem('notification-prompt-shown', Date.now().toString());
  };

  if (!showPrompt || permission !== 'default') {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      left: '20px',
      right: '20px',
      maxWidth: '400px',
      margin: '0 auto',
      background: 'white',
      border: '1px solid #dee2e6',
      borderRadius: '12px',
      padding: '20px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
      zIndex: 1000,
      animation: 'slideUp 0.3s ease-out'
    }}>
      <div style={{ marginBottom: '16px' }}>
        <h4 style={{ 
          margin: '0 0 8px 0', 
          color: '#2c3e50',
          fontSize: '16px',
          fontWeight: '600'
        }}>
          Stay Updated! 🔔
        </h4>
        <p style={{ 
          margin: 0, 
          color: '#6c757d',
          fontSize: '14px',
          lineHeight: '1.4'
        }}>
          Get notified about booking confirmations, appointment reminders, and special offers.
        </p>
      </div>
      
      <div style={{ 
        display: 'flex', 
        gap: '12px',
        justifyContent: 'flex-end'
      }}>
        <button
          onClick={dismissPrompt}
          style={{
            background: 'transparent',
            border: '1px solid #dee2e6',
            color: '#6c757d',
            padding: '8px 16px',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onMouseOver={(e) => {
            e.target.style.background = '#f8f9fa';
          }}
          onMouseOut={(e) => {
            e.target.style.background = 'transparent';
          }}
        >
          Not Now
        </button>
        <button
          onClick={requestPermission}
          style={{
            background: 'linear-gradient(135deg, #3788d8 0%, #2c6cb7 100%)',
            border: 'none',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'translateY(-1px)';
            e.target.style.boxShadow = '0 4px 12px rgba(55, 136, 216, 0.3)';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = 'none';
          }}
        >
          Enable Notifications
        </button>
      </div>

      <style jsx>{`
        @keyframes slideUp {
          from {
            transform: translateY(100%);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}
